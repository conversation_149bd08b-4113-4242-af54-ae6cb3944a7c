<template>
  <q-page class="q-pa-md">
    <div class="row q-gutter-md">
      <!-- 左侧主要内容区域 -->
      <div class="col-8">
        <!-- 页面头部 -->
        <div class="row items-center q-mb-lg">
          <div class="col">
            <h4 class="q-my-none">{{ workflow.name }}</h4>
            <p class="text-grey-6 q-mb-none">{{ workflow.task_type_display }}</p>
          </div>
          <div class="col-auto">
            <q-btn
              flat
              icon="arrow_back"
              label="返回概览"
              @click="$router.push('/training-workflow')"
            />
          </div>
        </div>

        <!-- 步骤进度条 -->
        <q-card class="q-mb-lg">
          <q-card-section>
            <q-stepper
              v-model="currentStepNumber"
              ref="stepper"
              color="primary"
              animated
              flat
              :contracted="$q.screen.lt.md"
            >
              <q-step
                :name="1"
                title="数据配置"
                icon="dataset"
                :done="currentStepNumber > 1"
                :active="currentStepNumber === 1"
              >
                <DataConfigStep
                  v-if="currentStepNumber === 1"
                  :workflow="workflow"
                  :step-data="workflow.step1_data_config"
                  @save="saveStepData"
                  @next="nextStep"
                />
              </q-step>

              <q-step
                :name="2"
                title="模型配置"
                icon="model_training"
                :done="currentStepNumber > 2"
                :active="currentStepNumber === 2"
              >
                <ModelConfigStep
                  v-if="currentStepNumber === 2"
                  :workflow="workflow"
                  :step-data="workflow.step2_model_config"
                  @save="saveStepData"
                  @next="nextStep"
                  @previous="previousStep"
                />
              </q-step>

              <q-step
                :name="3"
                title="训练配置"
                icon="play_arrow"
                :done="currentStepNumber > 3"
                :active="currentStepNumber === 3"
              >
                <TrainingConfigStep
                  v-if="currentStepNumber === 3"
                  :workflow="workflow"
                  :step-data="workflow.step3_training_config"
                  @save="saveStepData"
                  @next="nextStep"
                  @previous="previousStep"
                  @start-training="startTraining"
                />
              </q-step>

              <q-step
                :name="4"
                title="结果评估"
                icon="assessment"
                :done="workflow.status === 'completed'"
                :active="currentStepNumber === 4"
              >
                <EvaluationStep
                  v-if="currentStepNumber === 4"
                  :workflow="workflow"
                  :step-data="workflow.step4_evaluation_config"
                  @save="saveStepData"
                  @complete="completeWorkflow"
                  @previous="previousStep"
                />
              </q-step>
            </q-stepper>
          </q-card-section>
        </q-card>

        <!-- 训练指标显示 -->
        <q-card v-if="workflow.training_task_id && showMetrics" class="q-mb-lg">
          <q-card-section>
            <div class="text-h6 q-mb-md">训练指标</div>
            <TrainingMetricsChart :workflow-id="workflow.id" />
          </q-card-section>
        </q-card>
      </div>

      <!-- 右侧训练任务列表 -->
      <div class="col-4">
        <TrainingTaskPanel
          ref="taskPanel"
          @task-selected="handleTaskSelected"
        />
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import { api } from 'boot/axios'

// 导入步骤组件
import DataConfigStep from './steps/DataConfigStep.vue'
import ModelConfigStep from './steps/ModelConfigStep.vue'
import TrainingConfigStep from './steps/TrainingConfigStep.vue'
import EvaluationStep from './steps/EvaluationStep.vue'
import TrainingMetricsChart from './components/TrainingMetricsChart.vue'
import TrainingTaskPanel from './components/TrainingTaskPanel.vue'

const route = useRoute()
const router = useRouter()
const $q = useQuasar()

// 响应式数据
const workflow = ref({
  id: null,
  name: '',
  task_type_display: '',
  current_step: 'draft',
  status: 'draft',
  step1_data_config: {},
  step2_model_config: {},
  step3_training_config: {},
  step4_evaluation_config: {},
  training_task_id: null
})

const loading = ref(false)
const saving = ref(false)
const taskPanel = ref(null)

// 计算属性
const currentStepNumber = computed(() => {
  const stepMapping = {
    'draft': 1,
    'step1_data': 1,
    'step2_model': 2,
    'step3_training': 3,
    'step4_evaluation': 4,
    'completed': 4
  }
  return stepMapping[workflow.value.current_step] || 1
})

const showMetrics = computed(() => {
  return workflow.value.training_task_id && currentStepNumber.value >= 3
})

// 方法
const loadWorkflow = async () => {
  const workflowId = route.params.id
  if (!workflowId) return

  loading.value = true
  try {
    const response = await api.get(`backend/workflows/${workflowId}/`)
    workflow.value = response
    
    // 根据URL参数调整当前步骤
    const urlStep = route.params.step
    if (urlStep) {
      const stepNumber = parseInt(urlStep.replace('step', ''))
      if (stepNumber >= 1 && stepNumber <= 4) {
        // 可以导航到指定步骤，但不能超过当前进度
        const maxStep = Math.max(currentStepNumber.value, stepNumber)
        if (stepNumber <= maxStep) {
          // 更新URL但不触发重新加载
          const stepName = `step${stepNumber}`
          if (stepName !== workflow.value.current_step) {
            router.replace(`/training-workflow/${workflowId}/step${stepNumber}`)
          }
        }
      }
    }
  } catch (error) {
    console.error('加载工作流失败:', error)
    $q.notify({
      type: 'negative',
      message: '加载工作流失败'
    })
  } finally {
    loading.value = false
  }
}

const saveStepData = async (stepName, data) => {
  saving.value = true
  try {
    await api.post(`backend/workflows/${workflow.value.id}/steps/${stepName}/`, {
      data: data
    })

    // 更新本地数据
    workflow.value[`${stepName}_config`] = data

    $q.notify({
      type: 'positive',
      message: '数据已保存'
    })

    // 重新加载工作流以获取最新状态
    await loadWorkflow()

    // 刷新右侧任务列表
    if (taskPanel.value && taskPanel.value.loadTasks) {
      taskPanel.value.loadTasks()
    }

  } catch (error) {
    console.error('保存步骤数据失败:', error)
    $q.notify({
      type: 'negative',
      message: '保存失败'
    })
  } finally {
    saving.value = false
  }
}

const nextStep = async (stepData = null) => {
  const currentStep = currentStepNumber.value
  const nextStepNumber = currentStep + 1
  
  if (nextStepNumber > 4) return
  
  // 如果有数据需要保存，先保存
  if (stepData) {
    const stepName = `step${currentStep}_${getStepType(currentStep)}`
    await saveStepData(stepName, stepData)
  }
  
  // 导航到下一步
  router.push(`/training-workflow/${workflow.value.id}/step${nextStepNumber}`)
}

const previousStep = () => {
  const currentStep = currentStepNumber.value
  const prevStepNumber = currentStep - 1
  
  if (prevStepNumber < 1) return
  
  router.push(`/training-workflow/${workflow.value.id}/step${prevStepNumber}`)
}

const getStepType = (stepNumber) => {
  const types = ['', 'data', 'model', 'training', 'evaluation']
  return types[stepNumber] || ''
}

const startTraining = async (trainingConfig) => {
  try {
    // 保存训练配置
    await saveStepData('step3_training', trainingConfig)

    // 启动训练任务
    const response = await api.post('backend/training/start', {
      workflow_id: workflow.value.id,
      ...trainingConfig
    })

    if (response.success) {
      workflow.value.training_task_id = response.trainingId

      $q.notify({
        type: 'positive',
        message: '训练任务已启动'
      })

      // 刷新右侧任务列表以显示新的训练任务
      if (taskPanel.value && taskPanel.value.loadTasks) {
        taskPanel.value.loadTasks()
      }

      // 可以选择自动跳转到下一步或保持在当前步骤观察训练进度
    }
  } catch (error) {
    console.error('启动训练失败:', error)
    $q.notify({
      type: 'negative',
      message: '启动训练失败'
    })
  }
}

const completeWorkflow = async (evaluationData) => {
  try {
    await saveStepData('step4_evaluation', evaluationData)

    // 标记工作流为完成
    await api.patch(`backend/workflows/${workflow.value.id}/`, {
      status: 'completed',
      current_step: 'completed'
    })

    $q.notify({
      type: 'positive',
      message: '训练工作流已完成'
    })

    // 重新加载数据
    await loadWorkflow()

    // 刷新右侧任务列表以更新任务状态
    if (taskPanel.value && taskPanel.value.loadTasks) {
      taskPanel.value.loadTasks()
    }

  } catch (error) {
    console.error('完成工作流失败:', error)
    $q.notify({
      type: 'negative',
      message: '完成工作流失败'
    })
  }
}

// 处理任务选择
const handleTaskSelected = (task) => {
  console.log('选择的任务:', task)
  // 可以在这里处理任务选择逻辑，比如跳转到对应的工作流
  if (task.workflow_id && task.workflow_id !== workflow.value.id) {
    router.push(`/training-workflow/${task.workflow_id}/step1`)
  }
}

// 监听路由变化
watch(() => route.params, () => {
  if (route.name === 'training-workflow-step') {
    loadWorkflow()
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  loadWorkflow()
})
</script>

<style scoped>
.q-stepper {
  box-shadow: none;
}

.q-stepper .q-stepper__step-inner {
  padding: 24px;
}
</style>
