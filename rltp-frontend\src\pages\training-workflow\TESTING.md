# 训练工作流右侧任务列表功能测试指南

## 问题分析

您遇到的问题是：点击下一步之后，页面没有保存数据，在右边侧边栏看不到新的任务。

## 可能的原因

1. **API认证问题**：前端可能没有正确的认证token
2. **后端服务未启动**：Django后端服务可能没有运行
3. **数据库连接问题**：后端无法连接到数据库
4. **API路径问题**：前端调用的API路径可能不正确

## 测试步骤

### 1. 检查服务状态

**前端服务**：
```bash
cd rltp-frontend
npm run dev
# 应该在 http://localhost:9002 运行
```

**后端服务**：
```bash
cd reinforce-platform
python manage.py runserver 8000
# 应该在 http://127.0.0.1:8000 运行
```

### 2. 使用测试页面

访问测试页面：`http://localhost:9002/#/training-workflow-test`

这个页面包含：
- 左侧：模拟的训练工作流步骤
- 右侧：训练任务列表面板
- 底部：测试日志，显示所有操作的详细信息

### 3. 测试流程

1. **查看右侧面板**：
   - 应该显示"智能试训环境"标题
   - 列表中应该有模拟的训练任务
   - 如果看不到任务，检查浏览器控制台的错误信息

2. **测试步骤保存**：
   - 填写步骤1的数据集信息
   - 点击"保存步骤1并下一步"
   - 观察测试日志中的信息
   - 检查右侧任务列表是否更新

3. **查看浏览器控制台**：
   - 按F12打开开发者工具
   - 查看Console标签页的日志信息
   - 查看Network标签页的API请求

### 4. 调试信息

我已经在代码中添加了详细的console.log，您可以在浏览器控制台中看到：

- API请求和响应
- 数据保存过程
- 任务列表刷新过程
- 错误信息

## 常见问题解决

### 1. 右侧面板显示空白

**检查项**：
- 浏览器控制台是否有JavaScript错误
- TrainingTaskPanel组件是否正确加载

**解决方法**：
```javascript
// 在浏览器控制台中执行
console.log('任务面板组件:', document.querySelector('.training-task-panel'))
```

### 2. API调用失败

**检查项**：
- 后端服务是否运行在8000端口
- 前端API配置是否正确
- 是否有CORS问题

**解决方法**：
```javascript
// 在浏览器控制台中测试API
fetch('/backend/workflows/')
  .then(response => response.json())
  .then(data => console.log('API响应:', data))
  .catch(error => console.error('API错误:', error))
```

### 3. 认证问题

**检查项**：
- LocalStorage中是否有token
- API请求头是否包含Authorization

**解决方法**：
```javascript
// 检查token
console.log('Token:', localStorage.getItem('token'))

// 如果没有token，可以临时设置一个
localStorage.setItem('token', 'test-token')
```

### 4. 数据不显示

**检查项**：
- 模拟数据是否正确加载
- 数据转换逻辑是否正确

**解决方法**：
- 查看控制台中的"模拟任务数据已加载"日志
- 检查tasks数组的内容

## 快速修复

如果遇到问题，可以尝试以下快速修复：

### 1. 强制显示模拟数据

在TrainingTaskPanel.vue的loadTasks方法开始处添加：
```javascript
// 直接加载模拟数据用于测试
loadMockTasks()
return
```

### 2. 跳过API认证

在axios.js中临时注释掉认证检查：
```javascript
// 注释掉这行
// config.headers.Authorization = `Bearer ${LocalStorage.getItem('token')}`
```

### 3. 使用本地存储模拟保存

在WorkflowStepPage.vue的saveStepData方法中添加：
```javascript
// 模拟保存到本地存储
localStorage.setItem(`workflow_${workflow.value.id}_${stepName}`, JSON.stringify(data))
```

## 预期结果

正常工作时，您应该看到：

1. **右侧面板**：
   - 显示多个训练任务卡片
   - 训练中的任务有进度条
   - 已完成的任务显示100%

2. **点击保存后**：
   - 显示"数据已保存"通知
   - 测试日志显示保存过程
   - 右侧列表可能增加新的任务

3. **控制台日志**：
   - 显示API请求和响应
   - 显示数据转换过程
   - 没有错误信息

## 联系支持

如果问题仍然存在，请提供：
1. 浏览器控制台的完整错误信息
2. Network标签页中的API请求详情
3. 后端服务器的日志输出
