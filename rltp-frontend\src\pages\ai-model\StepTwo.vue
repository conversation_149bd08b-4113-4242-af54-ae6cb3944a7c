<!--
 * @Author: Szc
 * @Date: 2025-08-06 14:37:36
 * @LastEditors: Szc
 * @LastEditTime: 2025-08-13 15:56:59
 * @Description: 
-->
<template>
    <div class="content">
        <div class="top">
            <div class="left">
                <div class="one">
                    <img class="samllIcon" src="../../assets/images/icon_dz.png" alt="">
                    <div class="labelColor title">请设置模型参数</div>
                </div>

                <!-- 参数说明界面 -->
                <!-- <div class="param-info">
                    <div class="info-text">参数说明详见</div>
                    <div class="tech-doc">
                        <img src="../../assets/images/icon_jswd.png" alt="" class="doc-icon">
                        <span>技术文档</span>
                    </div>
                </div> -->

                <!-- 模型参数设置 -->
                <div class="param-settings">
                    <div class="param-item">
                        <div class="param-row">
                            <div class="param-label">
                                <span class="required">*</span>
                                <span>轮次:</span>
                            </div>
                            <q-input class="param-input" v-model="params.epochs" outlined dense />
                        </div>
                        <div class="param-desc">训练轮次越大，耗时越久，最终精度通常越高</div>
                    </div>

                    <div class="param-item">
                        <div class="param-row">
                            <div class="param-label">
                                <span class="required">*</span>
                                <span>批大小:</span>
                            </div>
                            <q-input class="param-input" v-model="params.batchSize" outlined dense />
                        </div>
                        <div class="param-desc">单个 Batch Size，值越大，显存占用越高</div>
                    </div>

                    <!-- 高级配置 -->
                    <div class="advanced-config">
                        <div class="config-header" @click="toggleAdvanced">
                            <span>高级配置</span>
                            <q-icon :name="showAdvanced ? 'expand_less' : 'expand_more'" />
                        </div>

                        <div v-if="showAdvanced" class="config-content">
                            <div class="param-item">
                                <div class="param-row">
                                    <div class="param-label">优化器类型:</div>
                                    <q-select class="param-select" v-model="params.optimizer"
                                        :options="optimizerOptions" outlined dense emit-value map-options />
                                </div>
                                <div class="param-desc">从训练中断保存的 checkpoint 继续训练</div>
                            </div>

                            <div class="param-item">
                                <div class="param-row">
                                    <div class="param-label">学习率:</div>
                                    <q-input class="param-input" v-model="params.learningRate" outlined dense />
                                </div>
                                <div class="param-desc">从预训练的权重开始训练，提高训练效率</div>
                            </div>

                            <div class="param-item">
                                <div class="param-row">
                                    <div class="param-label">热启动步数:</div>
                                    <q-input class="param-input" v-model="params.warmupSteps" outlined dense />
                                </div>
                                <div class="param-desc">在训练初期缓慢增长小学习率到最初训练的 step 数</div>
                            </div>

                            <div class="param-item">
                                <div class="param-row">
                                    <div class="param-label">日志打印间隔:</div>
                                    <q-input class="param-input" v-model="params.logInterval" outlined dense />
                                </div>
                                <div class="param-desc">每隔多少个 step 打印一次 log 信息</div>
                            </div>

                            <div class="param-item">
                                <div class="param-row">
                                    <div class="param-label">评估、保存间隔:</div>
                                    <q-input class="param-input" v-model="params.evalInterval" outlined dense />
                                </div>
                                <div class="param-desc">每隔多少个 epoch 进行一次模型评估保存评估</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="right">
                <div class="one">
                    <img class="samllIcon" src="../../assets/images/icon_dz.png" alt="">
                    <div class="labelColor title">请设置计算资源配置</div>
                </div>

                <!-- 计算资源配置 -->
                <div class="resource-config">
                    <div class="resource-item">
                        <div class="resource-row">
                            <div class="resource-label">
                                <span class="required">*</span>
                                <span>NPU 数量:</span>
                            </div>
                            <q-input class="resource-input" v-model="resources.npuCount" outlined dense />
                        </div>
                        <div class="resource-desc">类似显卡数量，根据实际情况进行调整与</div>
                    </div>

                    <div class="resource-item">
                        <div class="resource-row">
                            <div class="resource-label">
                                <span class="required">*</span>
                                <span>CPU 数量:</span>
                            </div>
                            <q-input class="resource-input" v-model="resources.cpuCount" outlined dense />
                        </div>
                        <div class="resource-desc">学习率等IV参考 Batch Size 进行同比例的调整</div>
                    </div>
                </div>

                <!-- 下一步按钮 -->
                <div class="bottom">
                    <div class="next">
                        <q-btn class="prevBtn roundBox" color="grey-7" label="上一步" @click="prevStep" />
                        <div class="button-group">
                            <q-btn class="saveBtn roundBox" color="green" label="保存" @click="saveCurrentStep" :loading="saving" />
                            <q-btn class="nextBtn roundBox" color="primary" label="下一步" @click="nextStepOnly" />
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>
</template>

<script setup>
import { ref, defineEmits, onMounted } from 'vue'
import { useModelFormStore } from '../../stores/modelFormStore'
import { api } from 'boot/axios'
import { usePlugin } from 'composables/plugin.js'
import { useRouter } from 'vue-router'

const { notify } = usePlugin()

// 路由
const router = useRouter()

// 保存状态
const saving = ref(false)

// 定义事件
const emit = defineEmits(['next-step', 'prev-step'])

// 获取模型表单存储
const modelFormStore = useModelFormStore()

// 高级配置展开状态
const showAdvanced = ref(true)

// 模型参数
const params = ref({
    epochs: '5',
    batchSize: '5',
    optimizer: 'Adam',
    learningRate: '1e-5',
    warmupSteps: '1000',
    logInterval: '100',
    evalInterval: '10'
})

// 计算资源
const resources = ref({
    npuCount: '1',
    cpuCount: '16'
})

// 优化器选项
const optimizerOptions = [
    { label: 'Adam', value: 'Adam' },
    { label: 'SGD', value: 'SGD' },
    { label: 'AdamW', value: 'AdamW' }
]

// 组件挂载时从store加载数据
onMounted(() => {
    console.log('=== StepTwo 页面被加载 ===')
    console.log('当前路由:', router.currentRoute.value.path)

    // 从store加载数据
    if (modelFormStore.stepTwoData) {
        params.value = { ...modelFormStore.stepTwoData.params }
        resources.value = { ...modelFormStore.stepTwoData.resources }
    }
})

// 切换高级配置显示
function toggleAdvanced() {
    showAdvanced.value = !showAdvanced.value
}

// 保存当前步骤数据
const saveCurrentStep = async () => {
  console.log('=== StepTwo 保存按钮被点击 ===')
  try {
    saving.value = true
    console.log('设置 saving 状态为 true')

    // 验证必要数据
    console.log('验证参数:', params.value)
    if (!params.value.epochs || !params.value.batchSize) {
      console.log('验证失败: 缺少必要参数')
      notify('请填写完整的训练参数', 'negative')
      saving.value = false
      return
    }
    console.log('参数验证通过')

    // 构建保存数据
    const stepData = {
      params: { ...params.value },
      resources: { ...resources.value }
    }

    console.log('开始保存步骤2数据:', stepData)

    // 更新本地存储
    modelFormStore.updateStepTwoData(stepData)
    console.log('本地存储更新成功')

    // 尝试调用后端API保存数据
    try {
      console.log('调用后端API保存数据...')
      await api.post('/backend/workflows/1/steps/step2_model/', {
          data: stepData
      })
      console.log('步骤2数据保存API调用成功')

      // 更新训练任务状态
      console.log('更新训练任务状态...')
      await api.patch('/backend/training/tasks/1/', {
          current_step: 'step2_model',
          progress: 40,
          model_config: stepData
      })
      console.log('训练任务更新API调用成功')
    } catch (apiError) {
      console.warn('后端API调用失败，但本地数据已保存:', apiError)
    }

    notify('步骤2数据保存成功', 'positive')

    // 保存成功后跳转到下一步
    console.log('准备跳转到第三步: /ai-model/step3')
    await router.push('/ai-model/step3')
    console.log('跳转完成')

  } catch (error) {
    console.error('保存步骤2数据失败:', error)
    notify(`保存失败: ${error.message}`, 'negative')
  } finally {
    saving.value = false
  }
}

// 仅跳转下一步，不保存数据
const nextStepOnly = () => {
  router.push('/ai-model/step3')
}

// 下一步按钮点击事件（仅跳转，不保存数据）
function nextStep() {
    // 直接跳转到下一步页面
    router.push('/ai-model/step3')
}

// 上一步按钮点击事件
function prevStep() {
    router.push('/ai-model/step1')
}
</script>

<style lang="scss" scoped>
.content {
    display: flex;
    flex-direction: column;


    .top {
        display: flex;
        margin-bottom: .125rem;
        flex: 1;

        height: 9.875rem;

        .left,
        .right {
            width: 50%;
            height: inherit;
            border: .025rem solid #707070;
            background-color: #181a24;
            padding: .3375rem;
            display: flex;
            flex-direction: column;


            .one {
                display: flex;
                align-items: center;
                font-size: .225rem;
                margin-bottom: .25rem;

                .samllIcon {
                    width: .2rem;
                    height: .2rem;
                    margin-right: .1875rem;
                }

                .title {
                    color: #4ab4ff;
                }
            }
        }

        .right {
            position: relative;

            &::before {
                position: absolute;
                z-index: 10000;
                content: '';
                left: -0.1rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198);
                border-radius: .125rem;
            }

            &::after {
                position: absolute;
                content: '';
                left: -0.075rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198, .5)
            }

            .one {
                display: flex;
                align-items: center;
                font-size: .225rem;
                margin-bottom: .25rem;

                .samllIcon {
                    width: .2rem;
                    height: .2rem;
                    margin-right: .1875rem;
                }

                .title {
                    color: #4ab4ff;
                }
            }
        }

        .left {
            margin-right: .125rem;
            position: relative;
            &::before {
                position: absolute;
                z-index: 10000;
                content: '';
                left: -0.1rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198);
                border-radius: .125rem;
            }

            &::after {
                position: absolute;
                content: '';
                left: -0.075rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198, .5)
            }
        }
    }

    // 头部行布局 - 标题与参数说明在一行
    .header-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: .375rem;

        .title-section {
            display: flex;
            align-items: center;

            .samllIcon {
                width: .2rem;
                height: .2rem;
                margin-right: .1875rem;
            }

            .title {
                color: #4ab4ff;
                font-size: .225rem;
            }
        }

        .param-info {
            display: flex;
            align-items: center;
            gap: .25rem;

            .info-text {
                color: white;
                font-size: .2rem;
            }

            .tech-doc {
                display: flex;
                align-items: center;
                color: #63d4ff;
                font-size: .175rem;
                cursor: pointer;

                .doc-icon {
                    width: .15rem;
                    height: .15rem;
                    margin-right: .0625rem;
                }
            }
        }
    }

    // 参数设置
    .param-settings {
        overflow-y: auto;
        .param-item {
            margin-bottom: .3rem;
            padding-left: .125rem;

            .param-row {
                display: flex;
                align-items: center;
                gap: .25rem;
                margin-bottom: .1rem;

                .param-label {
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                    color: white;
                    font-size: .2rem;
                    white-space: nowrap;
                    min-width: 1.5rem;

                    .required {
                        color: #ff4757;
                        margin-right: .0625rem;
                    }
                }

                .param-input {
                    width: 1.5rem;

                    :deep(.q-field__control-container) {
                        padding-top: 0 !important;
                    }
                }

                .param-select {
                    width: 2.5rem;

                    :deep(.q-field__control-container) {
                        padding-top: 0 !important;
                    }
                }
            }

            .param-desc {
                color: #999;
                font-size: .175rem;
                line-height: 1.2;
                margin-left: 1.75rem;
            }
        }
    }

    // 高级配置
    .advanced-config {
        margin-top: .5rem;
        padding-left: .125rem;

        .config-header {
            display: flex;
            align-items: center;
            color: #63d4ff;
            font-size: .2rem;
            cursor: pointer;
            margin-bottom: .25rem;

            span {
                margin-right: .125rem;
            }
        }

        .config-content {
            .param-item {
                margin-bottom: .3rem;
                padding-left: 0;

                .param-row {
                    display: flex;
                    align-items: center;
                    gap: .25rem;
                    margin-bottom: .1rem;

                    .param-label {
                        display: flex;
                        align-items: center;
                        justify-content: flex-end;
                        color: white;
                        font-size: .2rem;
                        white-space: nowrap;
                        min-width: 1.5rem;
                        line-height: 1.2;
                    }

                    .param-input,
                    .param-select {
                        width: 1.5rem;

                        :deep(.q-field__control-container) {
                            padding-top: 0 !important;
                        }
                    }

                    .param-select {
                        width: 2.5rem;
                    }
                }

                .param-desc {
                    color: #999;
                    font-size: .175rem;
                    line-height: 1.2;
                    margin-left: 1.75rem;
                }
            }
        }
    }

    // 计算资源配置
    .resource-config {
        .resource-item {
            margin-bottom: .3rem;
            padding-left: .125rem;

            .resource-row {
                display: flex;
                align-items: center;
                gap: .25rem;
                margin-bottom: .1rem;

                .resource-label {
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                    color: white;
                    font-size: .2rem;
                    white-space: nowrap;
                    min-width: 1.5rem;

                    .required {
                        color: #ff4757;
                        margin-right: .0625rem;
                    }
                }

                .resource-input {
                    width: 1.5rem;

                    :deep(.q-field__control-container) {
                        padding-top: 0 !important;
                    }
                }
            }

            .resource-desc {
                color: #999;
                font-size: .175rem;
                line-height: 1.2;
                margin-left: 1.75rem;
            }
        }
    }

    // 底部按钮
    .bottom {
        height: .8rem; // 固定高度
        display: flex;
        align-items: center;
        flex-shrink: 0; // 不允许缩小
        margin-top: auto;

        .next {
            display: flex;
            justify-content: space-between;
            width: 100%;
            padding: 0 .25rem;
            gap: .25rem;

            .prevBtn {
                margin-right: auto;
            }

            .button-group {
                display: flex;
                gap: .25rem;
                margin-left: auto;
            }

            .nextBtn {
                margin-left: auto;
            }
        }
    }
}

// 通用样式
.labelColor {
    color: #4ab4ff;
}


</style>