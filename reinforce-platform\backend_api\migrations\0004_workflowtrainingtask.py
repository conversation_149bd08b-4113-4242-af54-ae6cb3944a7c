# Generated by Django 4.2.7 on 2025-08-16 02:31

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('backend_api', '0003_add_step5_deployment_config'),
    ]

    operations = [
        migrations.CreateModel(
            name='WorkflowTrainingTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='任务名称')),
                ('task_type', models.CharField(choices=[('object_detection_yolov8', '目标识别-YoloV8'), ('image_classification', '图像分类'), ('semantic_segmentation', '语义分割')], max_length=50, verbose_name='任务类型')),
                ('status', models.Char<PERSON>ield(choices=[('configuring', '配置中'), ('training', '训练中'), ('evaluating', '评估中'), ('completed', '已完成'), ('failed', '失败'), ('cancelled', '已取消')], default='configuring', max_length=20, verbose_name='状态')),
                ('workflow_id', models.IntegerField(verbose_name='工作流ID')),
                ('current_step', models.CharField(default='step1_data', max_length=50, verbose_name='当前步骤')),
                ('progress', models.IntegerField(default=0, verbose_name='进度百分比')),
                ('dataset_name', models.CharField(blank=True, max_length=200, verbose_name='数据集名称')),
                ('train_samples', models.IntegerField(default=0, verbose_name='训练样本数')),
                ('validation_samples', models.IntegerField(default=0, verbose_name='验证样本数')),
                ('model_config', models.JSONField(blank=True, default=dict, verbose_name='模型配置')),
                ('training_config', models.JSONField(blank=True, default=dict, verbose_name='训练配置')),
                ('evaluation_results', models.JSONField(blank=True, default=dict, verbose_name='评估结果')),
                ('deployment_config', models.JSONField(blank=True, default=dict, verbose_name='部署配置')),
                ('cpu_usage', models.IntegerField(default=0, verbose_name='CPU使用率')),
                ('memory_usage', models.IntegerField(default=0, verbose_name='内存使用率')),
                ('gpu_usage', models.IntegerField(default=0, verbose_name='GPU使用率')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('started_at', models.DateTimeField(blank=True, null=True, verbose_name='开始时间')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='完成时间')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
            ],
            options={
                'verbose_name': '训练任务',
                'verbose_name_plural': '训练任务',
                'db_table': 'workflow_training_task',
                'ordering': ['-created_at'],
            },
        ),
    ]
