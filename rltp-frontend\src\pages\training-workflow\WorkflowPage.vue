<template>
  <q-page class="training-workflow-page">
    <!-- 页面头部 -->
    <div class="page-header q-pa-md">
      <div class="row items-center justify-between">
        <div>
          <h4 class="q-my-none">训练工作流管理</h4>
          <p class="text-grey-6 q-mb-none">管理您的AI模型训练工作流程</p>
        </div>
        <q-btn
          color="primary"
          icon="add"
          label="新建工作流"
          @click="showCreateDialog = true"
        />
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section q-pa-md">
      <div class="row q-gutter-md">
        <div class="col-3">
          <q-card class="stat-card">
            <q-card-section>
              <div class="stat-number">{{ stats.total_workflows }}</div>
              <div class="stat-label">总工作流</div>
            </q-card-section>
          </q-card>
        </div>
        <div class="col-3">
          <q-card class="stat-card">
            <q-card-section>
              <div class="stat-number text-orange">{{ stats.active_workflows }}</div>
              <div class="stat-label">进行中</div>
            </q-card-section>
          </q-card>
        </div>
        <div class="col-3">
          <q-card class="stat-card">
            <q-card-section>
              <div class="stat-number text-green">{{ stats.completed_workflows }}</div>
              <div class="stat-label">已完成</div>
            </q-card-section>
          </q-card>
        </div>
        <div class="col-3">
          <q-card class="stat-card">
            <q-card-section>
              <div class="stat-number text-red">{{ stats.failed_workflows }}</div>
              <div class="stat-label">失败</div>
            </q-card-section>
          </q-card>
        </div>
      </div>
    </div>

    <!-- 工作流列表 -->
    <div class="workflow-list q-pa-md">
      <q-card>
        <q-card-section>
          <div class="row items-center justify-between q-mb-md">
            <div class="text-h6">工作流列表</div>
            <div class="row q-gutter-sm">
              <q-select
                v-model="statusFilter"
                :options="statusOptions"
                label="状态筛选"
                dense
                outlined
                style="min-width: 120px"
                @update:model-value="loadWorkflows"
              />
              <q-select
                v-model="taskTypeFilter"
                :options="taskTypeOptions"
                label="任务类型"
                dense
                outlined
                style="min-width: 150px"
                @update:model-value="loadWorkflows"
              />
            </div>
          </div>

          <q-table
            :rows="workflows"
            :columns="columns"
            :loading="loading"
            row-key="id"
            @row-click="handleRowClick"
          >
            <template v-slot:body-cell-status="props">
              <q-td :props="props">
                <q-badge
                  :color="getStatusColor(props.row.status)"
                  :label="props.row.status_display"
                />
              </q-td>
            </template>

            <template v-slot:body-cell-progress="props">
              <q-td :props="props">
                <div class="progress-container">
                  <q-linear-progress
                    :value="props.row.progress_percentage / 100"
                    color="primary"
                    size="8px"
                    class="q-mb-xs"
                  />
                  <div class="text-caption">{{ props.row.progress_percentage }}%</div>
                </div>
              </q-td>
            </template>

            <template v-slot:body-cell-actions="props">
              <q-td :props="props">
                <q-btn
                  flat
                  dense
                  icon="play_arrow"
                  color="primary"
                  @click.stop="enterWorkflow(props.row)"
                  :disable="props.row.status === 'completed'"
                >
                  <q-tooltip>进入工作流</q-tooltip>
                </q-btn>
                <q-btn
                  flat
                  dense
                  icon="edit"
                  color="grey"
                  @click.stop="editWorkflow(props.row)"
                >
                  <q-tooltip>编辑</q-tooltip>
                </q-btn>
                <q-btn
                  flat
                  dense
                  icon="delete"
                  color="negative"
                  @click.stop="deleteWorkflow(props.row)"
                >
                  <q-tooltip>删除</q-tooltip>
                </q-btn>
              </q-td>
            </template>
          </q-table>
        </q-card-section>
      </q-card>
    </div>

    <!-- 创建工作流对话框 -->
    <q-dialog v-model="showCreateDialog">
      <q-card style="min-width: 400px">
        <q-card-section>
          <div class="text-h6">新建训练工作流</div>
        </q-card-section>

        <q-card-section>
          <q-form @submit="createWorkflow" class="q-gutter-md">
            <q-input
              v-model="newWorkflow.name"
              label="工作流名称"
              outlined
              :rules="[val => !!val || '请输入工作流名称']"
            />

            <q-input
              v-model="newWorkflow.description"
              label="描述"
              type="textarea"
              outlined
              rows="3"
            />

            <q-select
              v-model="newWorkflow.task_type"
              :options="taskTypeOptions"
              label="任务类型"
              outlined
              :rules="[val => !!val || '请选择任务类型']"
            />
          </q-form>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" @click="showCreateDialog = false" />
          <q-btn
            color="primary"
            label="创建"
            @click="createWorkflow"
            :loading="creating"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import { api } from 'boot/axios'

const router = useRouter()
const $q = useQuasar()

// 响应式数据
const loading = ref(false)
const creating = ref(false)
const showCreateDialog = ref(false)

const stats = ref({
  total_workflows: 0,
  active_workflows: 0,
  completed_workflows: 0,
  failed_workflows: 0
})

const workflows = ref([])
const statusFilter = ref(null)
const taskTypeFilter = ref(null)

const newWorkflow = ref({
  name: '',
  description: '',
  task_type: null
})

// 选项数据
const statusOptions = [
  { label: '全部', value: null },
  { label: '草稿', value: 'draft' },
  { label: '配置中', value: 'configuring' },
  { label: '训练中', value: 'training' },
  { label: '评估中', value: 'evaluating' },
  { label: '已完成', value: 'completed' },
  { label: '失败', value: 'failed' }
]

const taskTypeOptions = [
  { label: '全部', value: null },
  { label: '目标识别-YoloV8', value: 'object_detection_yolov8' },
  { label: '目标识别-YoloV5', value: 'object_detection_yolov5' },
  { label: '图像分类', value: 'image_classification' },
  { label: '语义分割', value: 'semantic_segmentation' },
  { label: '实例分割', value: 'instance_segmentation' }
]

// 表格列定义
const columns = [
  {
    name: 'name',
    label: '工作流名称',
    align: 'left',
    field: 'name',
    sortable: true
  },
  {
    name: 'task_type',
    label: '任务类型',
    align: 'left',
    field: 'task_type_display'
  },
  {
    name: 'status',
    label: '状态',
    align: 'center',
    field: 'status'
  },
  {
    name: 'progress',
    label: '进度',
    align: 'center',
    field: 'progress_percentage'
  },
  {
    name: 'created_at',
    label: '创建时间',
    align: 'left',
    field: 'created_at',
    format: val => new Date(val).toLocaleString()
  },
  {
    name: 'actions',
    label: '操作',
    align: 'center'
  }
]

// 方法
const loadOverview = async () => {
  try {
    const response = await api.get('backend/workflows/overview/')
    stats.value = response
  } catch (error) {
    console.error('加载概览数据失败:', error)
  }
}

const loadWorkflows = async () => {
  loading.value = true
  try {
    const params = {}
    if (statusFilter.value) params.status = statusFilter.value
    if (taskTypeFilter.value) params.task_type = taskTypeFilter.value

    const response = await api.get('backend/workflows/', { params })
    workflows.value = response.results || []
  } catch (error) {
    console.error('加载工作流列表失败:', error)
    $q.notify({
      type: 'negative',
      message: '加载工作流列表失败'
    })
  } finally {
    loading.value = false
  }
}

const createWorkflow = async () => {
  if (!newWorkflow.value.name || !newWorkflow.value.task_type) {
    $q.notify({
      type: 'negative',
      message: '请填写必要信息'
    })
    return
  }

  creating.value = true
  try {
    const response = await api.post('backend/workflows/', newWorkflow.value)

    $q.notify({
      type: 'positive',
      message: '工作流创建成功'
    })

    showCreateDialog.value = false
    newWorkflow.value = { name: '', description: '', task_type: null }

    // 刷新列表
    await loadWorkflows()
    await loadOverview()

    // 跳转到新创建的工作流
    router.push(`/training-workflow/${response.id}/step1`)

  } catch (error) {
    console.error('创建工作流失败:', error)
    $q.notify({
      type: 'negative',
      message: '创建工作流失败'
    })
  } finally {
    creating.value = false
  }
}

const handleRowClick = (evt, row) => {
  enterWorkflow(row)
}

const enterWorkflow = (workflow) => {
  // 根据当前步骤跳转到对应页面
  const step = workflow.current_step === 'draft' ? 'step1' : workflow.current_step
  router.push(`/training-workflow/${workflow.id}/${step}`)
}

const editWorkflow = (workflow) => {
  // 编辑工作流逻辑
  console.log('编辑工作流:', workflow)
}

const deleteWorkflow = (workflow) => {
  $q.dialog({
    title: '确认删除',
    message: `确定要删除工作流"${workflow.name}"吗？`,
    cancel: true,
    persistent: true
  }).onOk(async () => {
    try {
      await api.delete(`backend/workflows/${workflow.id}/`)
      $q.notify({
        type: 'positive',
        message: '工作流删除成功'
      })
      await loadWorkflows()
      await loadOverview()
    } catch (error) {
      console.error('删除工作流失败:', error)
      $q.notify({
        type: 'negative',
        message: '删除工作流失败'
      })
    }
  })
}

const getStatusColor = (status) => {
  const colors = {
    draft: 'grey',
    configuring: 'blue',
    training: 'orange',
    evaluating: 'purple',
    completed: 'green',
    failed: 'red',
    cancelled: 'grey'
  }
  return colors[status] || 'grey'
}

// 生命周期
onMounted(() => {
  loadOverview()
  loadWorkflows()
})
</script>

<style scoped>
.training-workflow-page {
  background-color: #f5f5f5;
}

.page-header {
  background: white;
  border-bottom: 1px solid #e0e0e0;
}

.stat-card {
  text-align: center;
  padding: 16px;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: #1976d2;
}

.stat-label {
  color: #666;
  margin-top: 8px;
}

.progress-container {
  min-width: 100px;
}

.workflow-list .q-table tbody tr {
  cursor: pointer;
}

.workflow-list .q-table tbody tr:hover {
  background-color: #f5f5f5;
}
</style>
