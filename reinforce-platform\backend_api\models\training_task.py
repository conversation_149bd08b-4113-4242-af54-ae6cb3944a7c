from django.db import models
from django.contrib.auth import get_user_model
import json

User = get_user_model()


class WorkflowTrainingTask(models.Model):
    """训练任务模型"""
    
    STATUS_CHOICES = [
        ('configuring', '配置中'),
        ('training', '训练中'),
        ('evaluating', '评估中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
    ]
    
    TASK_TYPE_CHOICES = [
        ('object_detection_yolov8', '目标识别-YoloV8'),
        ('image_classification', '图像分类'),
        ('semantic_segmentation', '语义分割'),
    ]
    
    name = models.CharField(max_length=200, verbose_name='任务名称')
    task_type = models.CharField(max_length=50, choices=TASK_TYPE_CHOICES, default='object_detection_yolov8', verbose_name='任务类型')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='configuring', verbose_name='状态')
    workflow_id = models.IntegerField(default=1, verbose_name='工作流ID')
    current_step = models.CharField(max_length=50, default='step1_data', verbose_name='当前步骤')
    progress = models.IntegerField(default=0, verbose_name='进度百分比')
    
    # 数据集信息
    dataset_name = models.CharField(max_length=200, blank=True, verbose_name='数据集名称')
    train_samples = models.IntegerField(default=0, verbose_name='训练样本数')
    validation_samples = models.IntegerField(default=0, verbose_name='验证样本数')
    
    # 配置信息（JSON格式存储）
    model_config = models.JSONField(default=dict, blank=True, verbose_name='模型配置')
    training_config = models.JSONField(default=dict, blank=True, verbose_name='训练配置')
    evaluation_results = models.JSONField(default=dict, blank=True, verbose_name='评估结果')
    deployment_config = models.JSONField(default=dict, blank=True, verbose_name='部署配置')
    
    # 资源使用情况
    cpu_usage = models.IntegerField(default=0, verbose_name='CPU使用率')
    memory_usage = models.IntegerField(default=0, verbose_name='内存使用率')
    gpu_usage = models.IntegerField(default=0, verbose_name='GPU使用率')
    
    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    started_at = models.DateTimeField(null=True, blank=True, verbose_name='开始时间')
    completed_at = models.DateTimeField(null=True, blank=True, verbose_name='完成时间')
    
    # 创建者
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, verbose_name='创建者')
    
    class Meta:
        db_table = 'workflow_training_task'
        verbose_name = '训练任务'
        verbose_name_plural = '训练任务'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} - {self.get_status_display()}"
    
    @property
    def duration(self):
        """计算任务持续时间"""
        if self.started_at and self.completed_at:
            return self.completed_at - self.started_at
        elif self.started_at:
            from django.utils import timezone
            return timezone.now() - self.started_at
        return None
    
    @property
    def status_display(self):
        """获取状态显示文本"""
        return self.get_status_display()
    
    def get_step_display(self):
        """获取步骤显示文本"""
        step_map = {
            'step1_data': '数据准备',
            'step2_model': '模型配置',
            'step3_training': '模型训练',
            'step4_evaluation': '模型评估',
            'step5_deployment': '模型部署',
        }
        return step_map.get(self.current_step, self.current_step)
