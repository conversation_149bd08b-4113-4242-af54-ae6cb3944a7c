<template>
    <div class="main-Form">
        <!-- 按钮区 -->
        <div class="Top">
            <TitleBtn ref="titleBtnRef"></TitleBtn>
            <q-btn class="doReturn" @click="returnToOverview">
                <img class="returnIcon" src="../../assets/images/icon_fh.png" alt="">
                <div class="labelColor">返回模型概览</div>
            </q-btn>
        </div>

        <!-- 根据当前步骤显示对应表单 -->
        <StepOne v-if="currentStep === 1" @next-step="handleNextStep" @prev-step="handlePrevStep"></StepOne>
        <StepTwo v-if="currentStep === 2" @next-step="handleNextStep" @prev-step="handlePrevStep"></StepTwo>
        <StepThree v-if="currentStep === 3" @next-step="handleNextStep" @prev-step="handlePrevStep"></StepThree>
        <StepFour v-if="currentStep === 4" @next-step="handleNextStep" @prev-step="handlePrevStep"></StepFour>
        <StepFive v-if="currentStep === 5" @next-step="handleNextStep" @prev-step="handlePrevStep"></StepFive>

    </div>


</template>


<script setup>
import { ref, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import TitleBtn from './components/TitleBtn.vue';
import StepOne from './StepOne.vue';
import StepTwo from './StepTwo.vue';
import StepThree from './StepThree.vue';
import StepFour from './StepFour.vue';
import StepFive from './StepFive.vue';

const router = useRouter()

// TitleBtn组件引用
const titleBtnRef = ref(null)

// 当前步骤计算属性，与TitleBtn同步
const currentStep = computed(() => {
    return titleBtnRef.value?.currentStep || 1
})

// 处理下一步
function handleNextStep() {
    if (titleBtnRef.value) {
        titleBtnRef.value.nextStep()
        console.log('当前步骤:', titleBtnRef.value.currentStep)
        
        // 根据当前步骤执行相应逻辑
        const currentStep = titleBtnRef.value.currentStep
        if (currentStep > 1) {
            console.log('执行步骤', currentStep, '的相关逻辑')
            
            // 如果切换到包含图表的步骤，确保DOM完全渲染
            if (currentStep === 3 || currentStep === 4) {
                nextTick(() => {
                    console.log('图表步骤DOM已准备就绪')
                })
            }
        }
    }
}

// 处理上一步
function handlePrevStep() {
    if (titleBtnRef.value) {
        titleBtnRef.value.prevStep()
        console.log('当前步骤:', titleBtnRef.value.currentStep)
    }
}

// 直接跳转到指定步骤的方法
function goToStep(step) {
    if (titleBtnRef.value) {
        titleBtnRef.value.goToStep(step)
        console.log('跳转到步骤:', step)
    }
}


function returnToOverview() {
    router.push('/ai-model/intelligent')//跳转到深度学习入口页
}
</script>


<style lang="scss" scoped>
.main-Form {
    background-color: #131520;
    height: calc(100vh - 1rem); // 减去顶部padding和margin
    display: flex;
    flex-direction: column;
}

.Top {
    margin-top: 1rem;
    margin-bottom: .125rem;
    position: relative;
    .doReturn{
        position: absolute;
        left:0;
        top:0;
        display: flex;
        .returnIcon{
            width: .375rem;
            height:.375rem;
            margin-right: .125rem;
        }
    }
}
</style>