<template>
  <q-card class="training-task-panel">
    <q-card-section class="panel-header">
      <div class="row items-center">
        <div class="col">
          <div class="text-h6 text-primary">智能试训环境</div>
        </div>
        <div class="col-auto">
          <q-btn
            flat
            round
            icon="add"
            size="sm"
            color="white"
            @click="createTestWorkflow"
            :loading="creating"
          >
            <q-tooltip>创建测试工作流</q-tooltip>
          </q-btn>
        </div>
      </div>
    </q-card-section>

    <q-separator />

    <q-card-section class="panel-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="text-center q-py-lg">
        <q-spinner color="primary" size="2em" />
        <div class="q-mt-sm text-grey-6">加载中...</div>
      </div>

      <!-- 任务列表 -->
      <div v-else class="task-list">
        <div 
          v-for="task in tasks" 
          :key="task.id"
          class="task-item q-mb-sm cursor-pointer"
          :class="{ 'task-item-active': task.status === 'training' }"
          @click="selectTask(task)"
        >
          <q-card flat bordered class="task-card">
            <q-card-section class="q-pa-sm">
              <!-- 任务标题 -->
              <div class="task-title">
                <q-icon 
                  :name="getTaskIcon(task.task_type)" 
                  size="sm" 
                  class="q-mr-xs"
                  :color="getTaskColor(task.status)"
                />
                <span class="text-subtitle2">{{ task.name }}</span>
              </div>

              <!-- 任务信息 -->
              <div class="task-info q-mt-xs">
                <div class="info-row">
                  <span class="info-label">训练类型:</span>
                  <span class="info-value">{{ getTaskTypeDisplay(task.task_type) }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">训练状态:</span>
                  <span class="info-value" :class="`text-${getTaskColor(task.status)}`">
                    {{ getStatusDisplay(task.status) }}
                  </span>
                </div>
                <div class="info-row">
                  <span class="info-label">资源配置:</span>
                  <span class="info-value">{{ task.resource_config || '16 CPU' }}</span>
                </div>
              </div>

              <!-- 进度条 -->
              <div v-if="task.status === 'training'" class="q-mt-sm">
                <q-linear-progress 
                  :value="task.progress / 100" 
                  color="primary" 
                  size="4px"
                  class="q-mb-xs"
                />
                <div class="text-caption text-grey-6 text-center">
                  {{ task.progress }}%
                </div>
              </div>

              <!-- 时间信息 -->
              <div class="task-time q-mt-xs">
                <span class="text-caption text-grey-6">
                  {{ formatTime(task.created_at) }}
                </span>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && tasks.length === 0" class="text-center q-py-lg">
          <q-icon name="inbox" size="3em" color="grey-4" />
          <div class="q-mt-sm text-grey-6">暂无训练任务</div>
        </div>
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { api } from 'boot/axios'
import { date } from 'quasar'

// 定义事件
const emit = defineEmits(['task-selected'])

// 响应式数据
const loading = ref(false)
const creating = ref(false)
const tasks = ref([])
const refreshInterval = ref(null)

// 任务类型映射
const taskTypeMap = {
  'object_detection_yolov8': '目标识别-YoloV8',
  'object_detection_yolov5': '目标识别-YoloV5', 
  'image_classification': '图像分类',
  'semantic_segmentation': '语义分割',
  'instance_segmentation': '实例分割'
}

// 状态映射
const statusMap = {
  'draft': '草稿',
  'step1_data': '数据配置',
  'step2_model': '模型配置', 
  'step3_training': '训练配置',
  'step4_evaluation': '结果评估',
  'training': '训练中',
  'completed': '已完成',
  'failed': '失败'
}

// 方法
const loadTasks = async () => {
  loading.value = true
  console.log('开始加载训练任务...')

  try {
    const response = await api.get('backend/workflows/', {
      params: {
        page_size: 20,
        ordering: '-created_at'
      }
    })

    console.log('API响应:', response)

    if (response && response.results) {
      console.log('工作流数据:', response.results)

      // 转换工作流数据为任务数据
      const workflowTasks = response.results.map(workflow => ({
        id: workflow.id,
        name: workflow.name || `${getTaskTypeDisplay(workflow.task_type)}-${new Date(workflow.created_at).toLocaleString()}`,
        task_type: workflow.task_type,
        status: getTaskStatus(workflow),
        progress: getTaskProgress(workflow),
        resource_config: getResourceConfig(workflow),
        created_at: workflow.created_at,
        workflow_id: workflow.id
      }))

      console.log('转换后的任务数据:', workflowTasks)

      // 添加一些模拟的训练中任务
      addMockTrainingTasks()

      // 合并真实数据和模拟数据
      tasks.value = [...workflowTasks, ...tasks.value]

      console.log('最终任务列表:', tasks.value)
    } else {
      console.log('没有工作流数据，使用模拟数据')
      loadMockTasks()
    }
  } catch (error) {
    console.error('加载训练任务失败:', error)
    console.log('API调用失败，使用模拟数据')
    // 使用模拟数据
    loadMockTasks()
  } finally {
    loading.value = false
  }
}

const addMockTrainingTasks = () => {
  const mockTasks = [
    {
      id: 'mock-1',
      name: '目标识别-YoloV8-2024-12-15 09:30:15',
      task_type: 'object_detection_yolov8',
      status: 'training',
      progress: 75,
      resource_config: '16 CPU',
      created_at: new Date().toISOString(),
      workflow_id: null
    },
    {
      id: 'mock-2',
      name: '目标识别-YoloV5-2024-12-15 09:30:15',
      task_type: 'object_detection_yolov5',
      status: 'training',
      progress: 45,
      resource_config: '12 CPU',
      created_at: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
      workflow_id: null
    },
    {
      id: 'mock-3',
      name: '目标识别-YoloV8-2024-12-15 09:30:15',
      task_type: 'object_detection_yolov8',
      status: 'completed',
      progress: 100,
      resource_config: '16 CPU',
      created_at: new Date(Date.now() - 1000 * 60 * 60).toISOString(),
      workflow_id: null
    }
  ]

  // 将模拟任务添加到现有任务列表前面
  tasks.value = [...mockTasks, ...tasks.value]
}

const loadMockTasks = () => {
  console.log('加载模拟任务数据')
  tasks.value = [
    {
      id: 'mock-1',
      name: '目标识别-YoloV8-2024-12-15 09:30:15',
      task_type: 'object_detection_yolov8',
      status: 'training',
      progress: 75,
      resource_config: '16 CPU',
      created_at: new Date().toISOString(),
      workflow_id: null
    },
    {
      id: 'mock-2',
      name: '目标识别-YoloV5-2024-12-15 09:30:15',
      task_type: 'object_detection_yolov5',
      status: 'training',
      progress: 45,
      resource_config: '12 CPU',
      created_at: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
      workflow_id: null
    },
    {
      id: 'mock-3',
      name: '目标识别-YoloV8-2024-12-15 09:30:15',
      task_type: 'object_detection_yolov8',
      status: 'completed',
      progress: 100,
      resource_config: '16 CPU',
      created_at: new Date(Date.now() - 1000 * 60 * 60).toISOString(),
      workflow_id: null
    },
    {
      id: 'mock-4',
      name: '目标识别-YoloV5-2024-12-15 09:30:15',
      task_type: 'object_detection_yolov5',
      status: 'completed',
      progress: 100,
      resource_config: '12 CPU',
      created_at: new Date(Date.now() - 1000 * 60 * 90).toISOString(),
      workflow_id: null
    },
    {
      id: 'mock-5',
      name: '目标识别-YoloV8-2024-12-15 09:30:15',
      task_type: 'object_detection_yolov8',
      status: 'completed',
      progress: 100,
      resource_config: '16 CPU',
      created_at: new Date(Date.now() - 1000 * 60 * 120).toISOString(),
      workflow_id: null
    },
    {
      id: 'mock-6',
      name: '目标识别-YoloV8-2024-12-15 10:15:30',
      task_type: 'object_detection_yolov8',
      status: 'step1_data',
      progress: 25,
      resource_config: '16 CPU',
      created_at: new Date(Date.now() - 1000 * 60 * 10).toISOString(),
      workflow_id: null
    }
  ]
  console.log('模拟任务数据已加载:', tasks.value)
}

const getTaskStatus = (workflow) => {
  if (workflow.status === 'completed') return 'completed'
  if (workflow.training_task_id) return 'training'
  return workflow.current_step || 'draft'
}

const getTaskProgress = (workflow) => {
  if (workflow.status === 'completed') return 100
  if (workflow.training_task_id) return Math.floor(Math.random() * 80) + 20
  return 0
}

const getResourceConfig = (workflow) => {
  // 根据任务类型返回不同的资源配置
  const configs = {
    'object_detection_yolov8': '16 CPU',
    'object_detection_yolov5': '12 CPU',
    'image_classification': '8 CPU',
    'semantic_segmentation': '20 CPU',
    'instance_segmentation': '24 CPU'
  }
  return configs[workflow.task_type] || '16 CPU'
}

const getTaskTypeDisplay = (taskType) => {
  return taskTypeMap[taskType] || taskType
}

const getStatusDisplay = (status) => {
  return statusMap[status] || status
}

const getTaskIcon = (taskType) => {
  const icons = {
    'object_detection_yolov8': 'visibility',
    'object_detection_yolov5': 'visibility',
    'image_classification': 'image',
    'semantic_segmentation': 'layers',
    'instance_segmentation': 'layers'
  }
  return icons[taskType] || 'smart_toy'
}

const getTaskColor = (status) => {
  const colors = {
    'draft': 'grey',
    'step1_data': 'blue',
    'step2_model': 'blue',
    'step3_training': 'blue', 
    'step4_evaluation': 'blue',
    'training': 'orange',
    'completed': 'green',
    'failed': 'red'
  }
  return colors[status] || 'grey'
}

const formatTime = (timeStr) => {
  if (!timeStr) return ''
  return date.formatDate(timeStr, 'MM-DD HH:mm')
}

const selectTask = (task) => {
  emit('task-selected', task)
}

const createTestWorkflow = async () => {
  creating.value = true
  try {
    const testWorkflow = {
      name: `测试工作流-${new Date().toLocaleString()}`,
      description: '测试用的训练工作流',
      task_type: 'object_detection_yolov8'
    }

    console.log('创建测试工作流:', testWorkflow)

    const response = await api.post('backend/workflows/', testWorkflow)

    console.log('创建工作流响应:', response)

    // 刷新任务列表
    await loadTasks()

    // 通知用户
    console.log('测试工作流创建成功')

  } catch (error) {
    console.error('创建测试工作流失败:', error)
  } finally {
    creating.value = false
  }
}

// 定时刷新训练中的任务进度
const startProgressUpdate = () => {
  refreshInterval.value = setInterval(() => {
    tasks.value.forEach(task => {
      if (task.status === 'training' && task.progress < 100) {
        // 模拟进度更新
        task.progress = Math.min(100, task.progress + Math.floor(Math.random() * 3))
        if (task.progress >= 100) {
          task.status = 'completed'
        }
      }
    })
  }, 5000) // 每5秒更新一次
}

const stopProgressUpdate = () => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
    refreshInterval.value = null
  }
}

// 暴露方法给父组件
defineExpose({
  loadTasks
})

// 生命周期
onMounted(() => {
  loadTasks()
  startProgressUpdate()
})

onUnmounted(() => {
  stopProgressUpdate()
})
</script>

<style scoped>
.training-task-panel {
  height: calc(100vh - 120px);
  overflow: hidden;
}

.panel-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
  padding: 16px;
}

.panel-content {
  height: calc(100% - 80px);
  overflow-y: auto;
  padding: 8px;
}

.task-list {
  max-height: 100%;
}

.task-item {
  transition: all 0.2s ease;
}

.task-item:hover {
  transform: translateY(-2px);
}

.task-item-active .task-card {
  border-color: #1976d2;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
}

.task-card {
  border-radius: 8px;
  transition: all 0.2s ease;
}

.task-title {
  display: flex;
  align-items: center;
  font-weight: 500;
}

.task-info {
  font-size: 12px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2px;
}

.info-label {
  color: #666;
  flex-shrink: 0;
}

.info-value {
  font-weight: 500;
  text-align: right;
}

.task-time {
  text-align: center;
  border-top: 1px solid #f0f0f0;
  padding-top: 4px;
  margin-top: 8px;
}
</style>
