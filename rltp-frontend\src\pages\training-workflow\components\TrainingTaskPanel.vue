<template>
  <div class="training-task-panel">
    <q-card class="panel-card">
      <q-card-section class="panel-header">
        <div class="text-h6">训练任务列表</div>
        <q-btn
          flat
          dense
          icon="refresh"
          @click="loadTasks"
          :loading="loading"
        >
          <q-tooltip>刷新</q-tooltip>
        </q-btn>
      </q-card-section>

      <q-separator />

      <!-- 任务筛选 -->
      <q-card-section class="filter-section">
        <q-select
          v-model="statusFilter"
          :options="statusOptions"
          label="状态筛选"
          dense
          outlined
          clearable
          @update:model-value="loadTasks"
        />
      </q-card-section>

      <!-- 任务列表 -->
      <q-card-section class="task-list">
        <div v-if="loading" class="text-center q-pa-md">
          <q-spinner color="primary" size="2em" />
          <div class="q-mt-sm">加载中...</div>
        </div>

        <div v-else-if="tasks.length === 0" class="text-center q-pa-md text-grey-6">
          暂无训练任务
        </div>

        <div v-else>
          <div
            v-for="task in tasks"
            :key="task.id"
            class="task-item"
            @click="selectTask(task)"
            :class="{ 'task-item-selected': selectedTaskId === task.id }"
          >
            <div class="task-header">
              <div class="task-name">{{ task.name || `任务 ${task.id}` }}</div>
              <q-badge
                :color="getStatusColor(task.status)"
                :label="getStatusText(task.status)"
                class="task-status"
              />
            </div>

            <div class="task-info">
              <div class="task-type">{{ getTaskTypeText(task.task_type) }}</div>
              <div class="task-time">{{ formatTime(task.created_at) }}</div>
            </div>

            <!-- 进度条 -->
            <div v-if="task.status === 'training' || task.status === 'running'" class="task-progress">
              <q-linear-progress
                :value="task.progress / 100"
                color="primary"
                size="4px"
                class="q-mb-xs"
              />
              <div class="progress-text">{{ task.progress }}% ({{ task.current_epoch }}/{{ task.total_epochs }})</div>
            </div>

            <!-- 训练指标 -->
            <div v-if="task.latest_metrics" class="task-metrics">
              <div class="metric-item">
                <span class="metric-label">损失:</span>
                <span class="metric-value">{{ task.latest_metrics.train_loss?.toFixed(4) }}</span>
              </div>
              <div class="metric-item" v-if="task.latest_metrics.map50">
                <span class="metric-label">mAP:</span>
                <span class="metric-value">{{ task.latest_metrics.map50?.toFixed(3) }}</span>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="task-actions">
              <q-btn
                flat
                dense
                size="sm"
                icon="visibility"
                color="primary"
                @click.stop="viewTaskDetails(task)"
              >
                <q-tooltip>查看详情</q-tooltip>
              </q-btn>
              
              <q-btn
                flat
                dense
                size="sm"
                icon="stop"
                color="negative"
                @click.stop="stopTask(task)"
                v-if="task.status === 'training' || task.status === 'running'"
              >
                <q-tooltip>停止训练</q-tooltip>
              </q-btn>
            </div>
          </div>
        </div>
      </q-card-section>

      <!-- 创建新任务按钮 -->
      <q-card-section class="panel-footer">
        <q-btn
          color="primary"
          icon="add"
          label="新建训练任务"
          @click="createNewTask"
          class="full-width"
        />
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref, onMounted, defineExpose } from 'vue'
import { useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import { api } from 'boot/axios'

const emit = defineEmits(['task-selected'])

const router = useRouter()
const $q = useQuasar()

// 响应式数据
const loading = ref(false)
const tasks = ref([])
const selectedTaskId = ref(null)
const statusFilter = ref(null)

const statusOptions = [
  { label: '全部', value: null },
  { label: '等待中', value: 'pending' },
  { label: '运行中', value: 'running' },
  { label: '训练中', value: 'training' },
  { label: '已完成', value: 'completed' },
  { label: '失败', value: 'failed' }
]

// 方法
const loadTasks = async () => {
  loading.value = true
  try {
    console.log('TrainingTaskPanel: 加载训练任务列表')
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟任务数据
    const mockTasks = [
      {
        id: 1,
        name: '目标检测训练-YOLOv8',
        task_type: 'object_detection_yolov8',
        status: 'training',
        progress: 65,
        current_epoch: 65,
        total_epochs: 100,
        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        latest_metrics: {
          train_loss: 0.0234,
          val_loss: 0.0267,
          map50: 0.823
        },
        workflow_id: 1
      },
      {
        id: 2,
        name: '图像分类训练-ResNet',
        task_type: 'image_classification',
        status: 'completed',
        progress: 100,
        current_epoch: 50,
        total_epochs: 50,
        created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        latest_metrics: {
          train_loss: 0.0156,
          val_loss: 0.0189,
          accuracy: 0.945
        },
        workflow_id: 2
      },
      {
        id: 3,
        name: '语义分割训练-UNet',
        task_type: 'semantic_segmentation',
        status: 'pending',
        progress: 0,
        current_epoch: 0,
        total_epochs: 80,
        created_at: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        workflow_id: 3
      },
      {
        id: 4,
        name: '目标检测训练-YOLOv5',
        task_type: 'object_detection_yolov5',
        status: 'failed',
        progress: 23,
        current_epoch: 23,
        total_epochs: 100,
        created_at: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
        latest_metrics: {
          train_loss: 0.0456,
          val_loss: 0.0523
        },
        workflow_id: 4
      }
    ]
    
    // 应用状态筛选
    let filteredTasks = mockTasks
    if (statusFilter.value) {
      filteredTasks = mockTasks.filter(task => task.status === statusFilter.value)
    }
    
    tasks.value = filteredTasks
    console.log('加载的任务列表:', tasks.value)
    
  } catch (error) {
    console.error('加载训练任务失败:', error)
    $q.notify({
      type: 'negative',
      message: '加载训练任务失败'
    })
  } finally {
    loading.value = false
  }
}

const selectTask = (task) => {
  console.log('TrainingTaskPanel: 选择任务', task)
  selectedTaskId.value = task.id
  emit('task-selected', task)
  
  // 如果任务有关联的工作流，跳转到对应的工作流页面
  if (task.workflow_id) {
    router.push(`/ai-model/training-workflow/${task.workflow_id}/step1`)
  }
}

const viewTaskDetails = (task) => {
  console.log('查看任务详情:', task)
  
  $q.dialog({
    title: '任务详情',
    message: `
      <div style="text-align: left;">
        <p><strong>任务名称:</strong> ${task.name}</p>
        <p><strong>任务类型:</strong> ${getTaskTypeText(task.task_type)}</p>
        <p><strong>状态:</strong> ${getStatusText(task.status)}</p>
        <p><strong>进度:</strong> ${task.progress}%</p>
        <p><strong>轮次:</strong> ${task.current_epoch}/${task.total_epochs}</p>
        <p><strong>创建时间:</strong> ${formatTime(task.created_at)}</p>
        ${task.latest_metrics ? `
          <p><strong>最新指标:</strong></p>
          <ul>
            <li>训练损失: ${task.latest_metrics.train_loss?.toFixed(4) || 'N/A'}</li>
            <li>验证损失: ${task.latest_metrics.val_loss?.toFixed(4) || 'N/A'}</li>
            ${task.latest_metrics.map50 ? `<li>mAP@0.5: ${task.latest_metrics.map50.toFixed(3)}</li>` : ''}
            ${task.latest_metrics.accuracy ? `<li>准确率: ${task.latest_metrics.accuracy.toFixed(3)}</li>` : ''}
          </ul>
        ` : ''}
      </div>
    `,
    html: true
  })
}

const stopTask = (task) => {
  $q.dialog({
    title: '确认停止',
    message: `确定要停止训练任务"${task.name}"吗？`,
    cancel: true,
    persistent: true
  }).onOk(async () => {
    try {
      // 这里调用停止训练的API
      console.log('停止训练任务:', task.id)
      
      // 更新任务状态
      const taskIndex = tasks.value.findIndex(t => t.id === task.id)
      if (taskIndex !== -1) {
        tasks.value[taskIndex].status = 'cancelled'
      }
      
      $q.notify({
        type: 'positive',
        message: '训练任务已停止'
      })
      
    } catch (error) {
      console.error('停止训练任务失败:', error)
      $q.notify({
        type: 'negative',
        message: '停止训练任务失败'
      })
    }
  })
}

const createNewTask = () => {
  console.log('创建新训练任务')
  router.push('/ai-model/training-workflow')
}

const getStatusColor = (status) => {
  const colors = {
    pending: 'orange',
    running: 'blue',
    training: 'blue',
    completed: 'green',
    failed: 'red',
    cancelled: 'grey'
  }
  return colors[status] || 'grey'
}

const getStatusText = (status) => {
  const texts = {
    pending: '等待中',
    running: '运行中',
    training: '训练中',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消'
  }
  return texts[status] || '未知'
}

const getTaskTypeText = (taskType) => {
  const types = {
    object_detection_yolov8: '目标识别-YOLOv8',
    object_detection_yolov5: '目标识别-YOLOv5',
    image_classification: '图像分类',
    semantic_segmentation: '语义分割',
    instance_segmentation: '实例分割'
  }
  return types[taskType] || taskType
}

const formatTime = (timeString) => {
  return new Date(timeString).toLocaleString()
}

// 暴露方法给父组件
defineExpose({
  loadTasks
})

// 生命周期
onMounted(() => {
  loadTasks()
})
</script>

<style scoped>
.training-task-panel {
  height: 100%;
}

.panel-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
}

.filter-section {
  padding: 8px 16px;
}

.task-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.task-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
}

.task-item:hover {
  border-color: #1976d2;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.task-item-selected {
  border-color: #1976d2;
  background-color: #e3f2fd;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.task-name {
  font-weight: 500;
  font-size: 14px;
  color: #333;
}

.task-status {
  font-size: 10px;
}

.task-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.task-type {
  font-size: 12px;
  color: #666;
}

.task-time {
  font-size: 12px;
  color: #999;
}

.task-progress {
  margin-bottom: 8px;
}

.progress-text {
  font-size: 11px;
  color: #666;
  text-align: center;
}

.task-metrics {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
}

.metric-item {
  font-size: 11px;
}

.metric-label {
  color: #666;
}

.metric-value {
  color: #333;
  font-weight: 500;
}

.task-actions {
  display: flex;
  justify-content: flex-end;
  gap: 4px;
}

.panel-footer {
  padding: 16px;
  border-top: 1px solid #e0e0e0;
}
</style>
