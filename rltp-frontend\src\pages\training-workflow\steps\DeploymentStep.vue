<template>
  <div class="deployment-step">
    <div class="step-header q-mb-lg">
      <h5 class="q-my-none">模型部署</h5>
      <p class="text-grey-6">将训练好的模型部署为推理服务</p>
    </div>

    <q-form @submit.prevent="handleSaveAndNext" class="deployment-form">
      <!-- 模型信息 -->
      <q-card flat bordered class="q-mb-md">
        <q-card-section>
          <div class="text-h6 q-mb-md">模型信息</div>
          
          <div class="row q-gutter-md">
            <div class="col-5">
              <q-input
                v-model="formData.model_name"
                label="模型名称"
                outlined
                :rules="[val => !!val || '请输入模型名称']"
              />
            </div>
            <div class="col-3">
              <q-input
                v-model="formData.model_version"
                label="模型版本"
                outlined
                :rules="[val => !!val || '请输入模型版本']"
              />
            </div>
            <div class="col-3">
              <q-select
                v-model="formData.deployment_type"
                :options="deploymentTypeOptions"
                label="部署类型"
                outlined
                :rules="[val => !!val || '请选择部署类型']"
              />
            </div>
          </div>

          <div class="row q-gutter-md q-mt-md">
            <div class="col-11">
              <q-input
                v-model="formData.model_path"
                label="模型路径"
                outlined
                readonly
                hint="训练完成后自动填充"
              />
            </div>
          </div>

          <div class="q-mt-md">
            <q-input
              v-model="formData.description"
              label="部署描述"
              type="textarea"
              outlined
              rows="3"
            />
          </div>
        </q-card-section>
      </q-card>

      <!-- 部署配置 -->
      <q-card flat bordered class="q-mb-md">
        <q-card-section>
          <div class="text-h6 q-mb-md">部署配置</div>
          
          <div class="row q-gutter-md">
            <div class="col-3">
              <q-select
                v-model="formData.device_type"
                :options="deviceTypeOptions"
                label="设备类型"
                outlined
              />
            </div>
            <div class="col-3">
              <q-input
                v-model.number="formData.max_batch_size"
                label="最大批次大小"
                type="number"
                outlined
                min="1"
                max="64"
              />
            </div>
            <div class="col-3">
              <q-input
                v-model.number="formData.max_concurrent_requests"
                label="最大并发请求"
                type="number"
                outlined
                min="1"
                max="100"
              />
            </div>
          </div>

          <div class="row q-gutter-md q-mt-md">
            <div class="col-3">
              <q-input
                v-model.number="formData.timeout_seconds"
                label="超时时间(秒)"
                type="number"
                outlined
                min="1"
                max="300"
              />
            </div>
            <div class="col-3">
              <q-toggle
                v-model="formData.auto_scaling"
                label="自动扩缩容"
              />
            </div>
            <div class="col-3">
              <q-toggle
                v-model="formData.enable_monitoring"
                label="启用监控"
              />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 资源配置 -->
      <q-card flat bordered class="q-mb-md">
        <q-card-section>
          <div class="text-h6 q-mb-md">资源配置</div>
          
          <div class="row q-gutter-md">
            <div class="col-3">
              <q-input
                v-model.number="formData.cpu_cores"
                label="CPU核心数"
                type="number"
                outlined
                min="1"
                max="16"
              />
            </div>
            <div class="col-3">
              <q-input
                v-model.number="formData.memory_gb"
                label="内存(GB)"
                type="number"
                outlined
                min="1"
                max="64"
              />
            </div>
            <div class="col-3">
              <q-input
                v-model.number="formData.gpu_count"
                label="GPU数量"
                type="number"
                outlined
                min="0"
                max="8"
              />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 部署状态 -->
      <q-card flat bordered class="q-mb-md" v-if="deploymentStatus">
        <q-card-section>
          <div class="text-h6 q-mb-md">部署状态</div>
          
          <div class="deployment-status">
            <div class="status-item">
              <q-icon :name="getStatusIcon(deploymentStatus.status)" :color="getStatusColor(deploymentStatus.status)" size="md" />
              <span class="status-text">{{ getStatusText(deploymentStatus.status) }}</span>
            </div>
            
            <div v-if="deploymentStatus.status === 'deployed'" class="deployment-info q-mt-md">
              <div class="info-row">
                <span class="info-label">服务地址:</span>
                <span class="info-value">{{ deploymentStatus.service_url }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">部署时间:</span>
                <span class="info-value">{{ formatDate(deploymentStatus.deployed_at) }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">请求总数:</span>
                <span class="info-value">{{ deploymentStatus.total_requests }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">成功率:</span>
                <span class="info-value">{{ deploymentStatus.success_rate }}%</span>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 操作按钮 -->
      <div class="step-actions q-mt-lg">
        <q-btn
          flat
          label="上一步"
          @click="$emit('previous')"
          class="q-mr-md"
        />
        
        <q-btn
          color="secondary"
          label="保存配置"
          @click="handleSave"
          :loading="saving"
          class="q-mr-md"
        />
        
        <q-btn
          color="primary"
          label="部署模型"
          @click="handleDeploy"
          :loading="deploying"
          :disable="!isFormValid || deploymentStatus?.status === 'deployed'"
          class="q-mr-md"
        />
        
        <q-btn
          color="positive"
          label="完成工作流"
          @click="handleComplete"
          :disable="deploymentStatus?.status !== 'deployed'"
        />
      </div>
    </q-form>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useQuasar } from 'quasar'
import { api } from 'boot/axios'

const props = defineProps({
  workflow: {
    type: Object,
    required: true
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['save', 'next', 'previous'])

const $q = useQuasar()

// 响应式数据
const formData = ref({
  model_name: '',
  model_version: '1.0',
  model_path: '',
  description: '',
  deployment_type: 'api_service',
  device_type: 'cpu',
  max_batch_size: 8,
  max_concurrent_requests: 10,
  timeout_seconds: 30,
  auto_scaling: false,
  enable_monitoring: true,
  cpu_cores: 2,
  memory_gb: 4,
  gpu_count: 0
})

const saving = ref(false)
const deploying = ref(false)
const deploymentStatus = ref(null)

// 选项数据
const deploymentTypeOptions = [
  { label: 'API服务', value: 'api_service' },
  { label: '批处理', value: 'batch_processing' },
  { label: '边缘部署', value: 'edge_deployment' },
  { label: '容器部署', value: 'container_deployment' }
]

const deviceTypeOptions = [
  { label: 'CPU', value: 'cpu' },
  { label: 'GPU', value: 'gpu' },
  { label: 'NPU', value: 'npu' }
]

// 计算属性
const isFormValid = computed(() => {
  return formData.value.model_name && 
         formData.value.model_version && 
         formData.value.deployment_type
})

// 监听props变化
watch(() => props.data, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(formData.value, newData)
  }
}, { immediate: true })

watch(() => props.workflow, (newWorkflow) => {
  if (newWorkflow.model_path) {
    formData.value.model_path = newWorkflow.model_path
  }
  if (newWorkflow.name && !formData.value.model_name) {
    formData.value.model_name = `${newWorkflow.name}-model`
  }
}, { immediate: true })

// 方法
const handleSave = async () => {
  saving.value = true
  try {
    emit('save', 'step5_deployment', formData.value)
    
    $q.notify({
      type: 'positive',
      message: '部署配置已保存'
    })
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

const handleDeploy = async () => {
  if (!isFormValid.value) {
    $q.notify({
      type: 'negative',
      message: '请完善必填信息'
    })
    return
  }

  deploying.value = true
  try {
    // 先保存配置
    await handleSave()
    
    // 创建推理模型
    const deploymentData = {
      workflow: props.workflow.id,
      model_name: formData.value.model_name,
      model_version: formData.value.model_version,
      model_path: formData.value.model_path,
      deployment_config: {
        deployment_type: formData.value.deployment_type,
        device_type: formData.value.device_type,
        max_batch_size: formData.value.max_batch_size,
        max_concurrent_requests: formData.value.max_concurrent_requests,
        timeout_seconds: formData.value.timeout_seconds,
        auto_scaling: formData.value.auto_scaling,
        enable_monitoring: formData.value.enable_monitoring,
        resources: {
          cpu_cores: formData.value.cpu_cores,
          memory_gb: formData.value.memory_gb,
          gpu_count: formData.value.gpu_count
        },
        description: formData.value.description
      }
    }
    
    const response = await api.post('backend/inference-models/', deploymentData)
    
    deploymentStatus.value = {
      status: 'deployed',
      service_url: `http://api.example.com/inference/${response.id}`,
      deployed_at: new Date().toISOString(),
      total_requests: 0,
      success_rate: 100
    }
    
    $q.notify({
      type: 'positive',
      message: '模型部署成功'
    })
    
  } catch (error) {
    console.error('部署失败:', error)
    $q.notify({
      type: 'negative',
      message: '模型部署失败'
    })
  } finally {
    deploying.value = false
  }
}

const handleComplete = async () => {
  try {
    // 更新工作流状态为已完成
    await api.patch(`backend/workflows/${props.workflow.id}/`, {
      status: 'completed',
      current_step: 'completed'
    })
    
    $q.notify({
      type: 'positive',
      message: '工作流已完成'
    })
    
    emit('next')
    
  } catch (error) {
    console.error('完成工作流失败:', error)
    $q.notify({
      type: 'negative',
      message: '完成工作流失败'
    })
  }
}

const handleSaveAndNext = async () => {
  await handleSave()
  emit('next')
}

const getStatusIcon = (status) => {
  const icons = {
    preparing: 'hourglass_empty',
    ready: 'check_circle',
    deployed: 'cloud_done',
    failed: 'error'
  }
  return icons[status] || 'help'
}

const getStatusColor = (status) => {
  const colors = {
    preparing: 'orange',
    ready: 'blue',
    deployed: 'green',
    failed: 'red'
  }
  return colors[status] || 'grey'
}

const getStatusText = (status) => {
  const texts = {
    preparing: '准备中',
    ready: '就绪',
    deployed: '已部署',
    failed: '失败'
  }
  return texts[status] || '未知'
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString()
}

// 生命周期
onMounted(() => {
  // 检查是否已有部署记录
  if (props.workflow.id) {
    loadDeploymentStatus()
  }
})

const loadDeploymentStatus = async () => {
  try {
    const response = await api.get(`backend/inference-models/?workflow=${props.workflow.id}`)
    if (response.results && response.results.length > 0) {
      const latestDeployment = response.results[0]
      deploymentStatus.value = {
        status: latestDeployment.status,
        service_url: `http://api.example.com/inference/${latestDeployment.id}`,
        deployed_at: latestDeployment.deployed_at,
        total_requests: latestDeployment.total_requests,
        success_rate: latestDeployment.success_rate || 0
      }
    }
  } catch (error) {
    console.error('加载部署状态失败:', error)
  }
}
</script>

<style scoped>
.deployment-step {
  padding: 24px;
}

.step-header {
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 16px;
}

.deployment-form .q-card {
  margin-bottom: 16px;
}

.deployment-status {
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-text {
  font-size: 16px;
  font-weight: 500;
}

.deployment-info {
  background: white;
  padding: 16px;
  border-radius: 8px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.info-label {
  color: #666;
  font-weight: 500;
}

.info-value {
  color: #333;
}

.step-actions {
  display: flex;
  justify-content: flex-end;
  padding-top: 24px;
  border-top: 1px solid #e0e0e0;
}
</style>
