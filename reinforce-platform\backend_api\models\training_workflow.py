from django.db import models
from django.contrib.auth.models import User
import json


class TrainingWorkflow(models.Model):
    """训练工作流模型 - 管理整个训练任务的生命周期"""
    
    TASK_TYPES = [
        ('object_detection_yolov8', '目标识别-YoloV8'),
        ('object_detection_yolov5', '目标识别-YoloV5'),
        ('image_classification', '图像分类'),
        ('semantic_segmentation', '语义分割'),
        ('instance_segmentation', '实例分割'),
    ]
    
    WORKFLOW_STATUS = [
        ('draft', '草稿'),
        ('step1_data', '步骤1-数据配置'),
        ('step2_model', '步骤2-模型配置'),
        ('step3_training', '步骤3-训练配置'),
        ('step4_evaluation', '步骤4-结果评估'),
        ('completed', '已完成'),
        ('failed', '失败'),
    ]
    
    # 基本信息
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200, verbose_name='任务名称')
    description = models.TextField(blank=True, verbose_name='任务描述')
    task_type = models.CharField(max_length=50, choices=TASK_TYPES, verbose_name='任务类型')
    
    # 状态管理
    current_step = models.CharField(max_length=20, choices=WORKFLOW_STATUS, default='draft', verbose_name='当前步骤')
    status = models.CharField(max_length=20, choices=WORKFLOW_STATUS, default='draft', verbose_name='任务状态')
    
    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    started_at = models.DateTimeField(null=True, blank=True, verbose_name='开始训练时间')
    completed_at = models.DateTimeField(null=True, blank=True, verbose_name='完成时间')
    
    # 用户关联
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='创建者')
    
    # 配置数据存储
    step1_data_config = models.JSONField(default=dict, blank=True, verbose_name='步骤1数据配置')
    step2_model_config = models.JSONField(default=dict, blank=True, verbose_name='步骤2模型配置')
    step3_training_config = models.JSONField(default=dict, blank=True, verbose_name='步骤3训练配置')
    step4_evaluation_config = models.JSONField(default=dict, blank=True, verbose_name='步骤4评估配置')
    
    # 训练结果
    training_task_id = models.IntegerField(null=True, blank=True, verbose_name='关联的训练任务ID')
    model_path = models.CharField(max_length=500, blank=True, verbose_name='训练完成的模型路径')
    best_metrics = models.JSONField(default=dict, blank=True, verbose_name='最佳训练指标')
    
    class Meta:
        db_table = 'training_workflow'
        verbose_name = '训练工作流'
        verbose_name_plural = '训练工作流'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} ({self.get_task_type_display()})"
    
    def get_step_progress(self):
        """获取步骤进度百分比"""
        step_mapping = {
            'draft': 0,
            'step1_data': 25,
            'step2_model': 50,
            'step3_training': 75,
            'step4_evaluation': 90,
            'completed': 100,
            'failed': 0,
        }
        return step_mapping.get(self.current_step, 0)
    
    def can_proceed_to_next_step(self):
        """检查是否可以进入下一步"""
        if self.current_step == 'draft':
            return bool(self.step1_data_config)
        elif self.current_step == 'step1_data':
            return bool(self.step2_model_config)
        elif self.current_step == 'step2_model':
            return bool(self.step3_training_config)
        elif self.current_step == 'step3_training':
            return self.training_task_id is not None
        return False


class TrainingMetricsRecord(models.Model):
    """训练指标记录模型"""
    
    workflow = models.ForeignKey(TrainingWorkflow, on_delete=models.CASCADE, related_name='metrics_records')
    training_task_id = models.IntegerField(verbose_name='训练任务ID')
    
    # 训练指标
    epoch = models.IntegerField(verbose_name='训练轮次')
    train_loss = models.FloatField(null=True, blank=True, verbose_name='训练损失')
    val_loss = models.FloatField(null=True, blank=True, verbose_name='验证损失')
    train_accuracy = models.FloatField(null=True, blank=True, verbose_name='训练准确率')
    val_accuracy = models.FloatField(null=True, blank=True, verbose_name='验证准确率')
    
    # YOLO特定指标
    precision = models.FloatField(null=True, blank=True, verbose_name='精确率')
    recall = models.FloatField(null=True, blank=True, verbose_name='召回率')
    map50 = models.FloatField(null=True, blank=True, verbose_name='mAP@0.5')
    map50_95 = models.FloatField(null=True, blank=True, verbose_name='mAP@0.5:0.95')
    
    # 其他指标
    learning_rate = models.FloatField(null=True, blank=True, verbose_name='学习率')
    gpu_memory = models.FloatField(null=True, blank=True, verbose_name='GPU内存使用')
    training_time = models.FloatField(null=True, blank=True, verbose_name='训练时间(秒)')
    
    # 扩展指标存储
    additional_metrics = models.JSONField(default=dict, blank=True, verbose_name='其他指标')
    
    # 时间戳
    recorded_at = models.DateTimeField(auto_now_add=True, verbose_name='记录时间')
    
    class Meta:
        db_table = 'training_metrics_record'
        verbose_name = '训练指标记录'
        verbose_name_plural = '训练指标记录'
        ordering = ['workflow', 'epoch']
        unique_together = ['workflow', 'training_task_id', 'epoch']
    
    def __str__(self):
        return f"{self.workflow.name} - Epoch {self.epoch}"


class ModelEvaluation(models.Model):
    """模型评估结果"""
    
    workflow = models.OneToOneField(TrainingWorkflow, on_delete=models.CASCADE, related_name='evaluation')
    
    # 评估指标
    overall_accuracy = models.FloatField(null=True, blank=True, verbose_name='总体准确率')
    class_accuracies = models.JSONField(default=dict, blank=True, verbose_name='各类别准确率')
    confusion_matrix = models.JSONField(default=list, blank=True, verbose_name='混淆矩阵')
    
    # 测试结果
    test_loss = models.FloatField(null=True, blank=True, verbose_name='测试损失')
    test_samples_count = models.IntegerField(null=True, blank=True, verbose_name='测试样本数量')
    
    # 评估报告
    evaluation_report = models.JSONField(default=dict, blank=True, verbose_name='详细评估报告')
    evaluation_images = models.JSONField(default=list, blank=True, verbose_name='评估结果图片')
    
    # 时间戳
    evaluated_at = models.DateTimeField(auto_now_add=True, verbose_name='评估时间')
    
    class Meta:
        db_table = 'model_evaluation'
        verbose_name = '模型评估'
        verbose_name_plural = '模型评估'
    
    def __str__(self):
        return f"{self.workflow.name} - 评估结果"


class InferenceModel(models.Model):
    """推理模型管理"""
    
    MODEL_STATUS = [
        ('training', '训练中'),
        ('ready', '就绪'),
        ('deployed', '已部署'),
        ('archived', '已归档'),
    ]
    
    workflow = models.OneToOneField(TrainingWorkflow, on_delete=models.CASCADE, related_name='inference_model')
    
    # 模型信息
    model_name = models.CharField(max_length=200, verbose_name='模型名称')
    model_version = models.CharField(max_length=50, default='v1.0', verbose_name='模型版本')
    model_path = models.CharField(max_length=500, verbose_name='模型文件路径')
    model_size = models.BigIntegerField(null=True, blank=True, verbose_name='模型文件大小(字节)')
    
    # 模型状态
    status = models.CharField(max_length=20, choices=MODEL_STATUS, default='training', verbose_name='模型状态')
    
    # 性能指标
    inference_time = models.FloatField(null=True, blank=True, verbose_name='推理时间(ms)')
    memory_usage = models.FloatField(null=True, blank=True, verbose_name='内存使用(MB)')
    
    # 部署信息
    deployment_config = models.JSONField(default=dict, blank=True, verbose_name='部署配置')
    api_endpoint = models.URLField(blank=True, verbose_name='API端点')
    
    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    deployed_at = models.DateTimeField(null=True, blank=True, verbose_name='部署时间')
    
    class Meta:
        db_table = 'inference_model'
        verbose_name = '推理模型'
        verbose_name_plural = '推理模型'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.model_name} {self.model_version}"
