from django.db import models
from django.conf import settings
from django.utils import timezone
import uuid


class TrainingWorkflow(models.Model):
    """训练工作流模型"""
    
    TASK_TYPE_CHOICES = [
        ('object_detection_yolov8', '目标识别-YoloV8'),
        ('object_detection_yolov5', '目标识别-YoloV5'),
        ('image_classification', '图像分类'),
        ('semantic_segmentation', '语义分割'),
        ('instance_segmentation', '实例分割'),
    ]
    
    STEP_CHOICES = [
        ('draft', '草稿'),
        ('step1_data', '步骤1-数据配置'),
        ('step2_model', '步骤2-模型配置'),
        ('step3_training', '步骤3-训练配置'),
        ('step4_evaluation', '步骤4-结果评估'),
        ('step5_deployment', '步骤5-模型部署'),
        ('completed', '已完成'),
        ('failed', '失败'),
    ]
    
    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('configuring', '配置中'),
        ('training', '训练中'),
        ('evaluating', '评估中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
    ]
    
    # 基本信息
    id = models.AutoField(primary_key=True)
    name = models.CharField('任务名称', max_length=200)
    description = models.TextField('任务描述', blank=True)
    task_type = models.CharField('任务类型', max_length=50, choices=TASK_TYPE_CHOICES)
    
    # 状态管理
    current_step = models.CharField('当前步骤', max_length=20, choices=STEP_CHOICES, default='draft')
    status = models.CharField('任务状态', max_length=20, choices=STATUS_CHOICES, default='draft')
    
    # 时间信息
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    started_at = models.DateTimeField('开始训练时间', null=True, blank=True)
    completed_at = models.DateTimeField('完成时间', null=True, blank=True)
    
    # 步骤配置数据
    step1_data_config = models.JSONField('步骤1数据配置', default=dict, blank=True)
    step2_model_config = models.JSONField('步骤2模型配置', default=dict, blank=True)
    step3_training_config = models.JSONField('步骤3训练配置', default=dict, blank=True)
    step4_evaluation_config = models.JSONField('步骤4评估配置', default=dict, blank=True)
    step5_deployment_config = models.JSONField('步骤5部署配置', default=dict, blank=True)
    
    # 关联信息
    training_task_id = models.IntegerField('关联的训练任务ID', null=True, blank=True)
    model_path = models.CharField('训练完成的模型路径', max_length=500, blank=True)
    best_metrics = models.JSONField('最佳训练指标', default=dict, blank=True)
    
    # 用户关联
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name='创建者')
    
    class Meta:
        verbose_name = '训练工作流'
        verbose_name_plural = '训练工作流'
        db_table = 'training_workflow'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} ({self.get_task_type_display()})"
    
    def get_progress_percentage(self):
        """获取进度百分比"""
        step_progress = {
            'draft': 0,
            'step1_data': 20,
            'step2_model': 40,
            'step3_training': 60,
            'step4_evaluation': 80,
            'step5_deployment': 90,
            'completed': 100,
            'failed': 0,
        }
        return step_progress.get(self.current_step, 0)
    
    def can_proceed_to_next_step(self):
        """检查是否可以进入下一步"""
        if self.current_step == 'draft':
            return True
        elif self.current_step == 'step1_data':
            return bool(self.step1_data_config.get('dataset_id'))
        elif self.current_step == 'step2_model':
            return bool(self.step2_model_config.get('model_type'))
        elif self.current_step == 'step3_training':
            return bool(self.step3_training_config.get('epochs'))
        elif self.current_step == 'step4_evaluation':
            return bool(self.training_task_id)
        return False
    
    def get_next_step(self):
        """获取下一步骤"""
        step_order = ['draft', 'step1_data', 'step2_model', 'step3_training', 'step4_evaluation', 'step5_deployment', 'completed']
        try:
            current_index = step_order.index(self.current_step)
            if current_index < len(step_order) - 1:
                return step_order[current_index + 1]
        except ValueError:
            pass
        return None


class TrainingMetrics(models.Model):
    """训练指标记录模型"""
    
    workflow = models.ForeignKey(TrainingWorkflow, on_delete=models.CASCADE, related_name='metrics_records')
    
    # 训练基本信息
    epoch = models.IntegerField('训练轮次')
    step = models.IntegerField('训练步数', default=0)
    
    # 损失指标
    train_loss = models.FloatField('训练损失', null=True, blank=True)
    val_loss = models.FloatField('验证损失', null=True, blank=True)
    
    # 准确率指标
    train_accuracy = models.FloatField('训练准确率', null=True, blank=True)
    val_accuracy = models.FloatField('验证准确率', null=True, blank=True)
    
    # 目标检测特有指标
    precision = models.FloatField('精确率', null=True, blank=True)
    recall = models.FloatField('召回率', null=True, blank=True)
    map50 = models.FloatField('mAP@0.5', null=True, blank=True)
    map50_95 = models.FloatField('mAP@0.5:0.95', null=True, blank=True)
    
    # 训练环境指标
    learning_rate = models.FloatField('学习率', null=True, blank=True)
    gpu_memory = models.FloatField('GPU内存使用', null=True, blank=True)
    training_time = models.FloatField('训练时间(秒)', null=True, blank=True)
    
    # 其他指标
    additional_metrics = models.JSONField('其他指标', default=dict, blank=True)
    
    # 记录时间
    recorded_at = models.DateTimeField('记录时间', auto_now_add=True)
    
    class Meta:
        verbose_name = '训练指标'
        verbose_name_plural = '训练指标'
        db_table = 'training_metrics'
        ordering = ['-recorded_at']
        indexes = [
            models.Index(fields=['workflow', 'epoch']),
            models.Index(fields=['recorded_at']),
        ]
    
    def __str__(self):
        return f"{self.workflow.name} - Epoch {self.epoch}"


class ModelEvaluation(models.Model):
    """模型评估结果模型"""
    
    workflow = models.ForeignKey(TrainingWorkflow, on_delete=models.CASCADE, related_name='evaluations')
    
    # 评估基本信息
    evaluation_name = models.CharField('评估名称', max_length=200)
    model_path = models.CharField('模型路径', max_length=500)
    test_dataset_path = models.CharField('测试数据集路径', max_length=500)
    
    # 评估结果
    overall_accuracy = models.FloatField('总体准确率', null=True, blank=True)
    precision = models.FloatField('精确率', null=True, blank=True)
    recall = models.FloatField('召回率', null=True, blank=True)
    f1_score = models.FloatField('F1分数', null=True, blank=True)
    
    # 目标检测特有指标
    map50 = models.FloatField('mAP@0.5', null=True, blank=True)
    map50_95 = models.FloatField('mAP@0.5:0.95', null=True, blank=True)
    
    # 性能指标
    inference_time_ms = models.FloatField('推理时间(毫秒)', null=True, blank=True)
    fps = models.FloatField('FPS', null=True, blank=True)
    model_size_mb = models.FloatField('模型大小(MB)', null=True, blank=True)
    
    # 详细结果
    class_metrics = models.JSONField('各类别指标', default=dict, blank=True)
    confusion_matrix = models.JSONField('混淆矩阵', default=dict, blank=True)
    evaluation_report = models.JSONField('评估报告', default=dict, blank=True)
    
    # 评估状态
    status = models.CharField('评估状态', max_length=20, choices=[
        ('pending', '等待中'),
        ('running', '评估中'),
        ('completed', '已完成'),
        ('failed', '失败'),
    ], default='pending')
    
    # 时间信息
    started_at = models.DateTimeField('开始时间', null=True, blank=True)
    completed_at = models.DateTimeField('完成时间', null=True, blank=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        verbose_name = '模型评估'
        verbose_name_plural = '模型评估'
        db_table = 'model_evaluation'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.workflow.name} - {self.evaluation_name}"


class InferenceModel(models.Model):
    """推理模型模型"""
    
    workflow = models.ForeignKey(TrainingWorkflow, on_delete=models.CASCADE, related_name='inference_models')
    
    # 模型基本信息
    model_name = models.CharField('模型名称', max_length=200)
    model_version = models.CharField('模型版本', max_length=50, default='1.0')
    model_path = models.CharField('模型路径', max_length=500)
    
    # 部署配置
    deployment_config = models.JSONField('部署配置', default=dict, blank=True)
    
    # 模型状态
    status = models.CharField('状态', max_length=20, choices=[
        ('preparing', '准备中'),
        ('ready', '就绪'),
        ('deployed', '已部署'),
        ('failed', '失败'),
        ('archived', '已归档'),
    ], default='preparing')
    
    # 性能信息
    inference_time_ms = models.FloatField('推理时间(毫秒)', null=True, blank=True)
    throughput_qps = models.FloatField('吞吐量(QPS)', null=True, blank=True)
    model_size_mb = models.FloatField('模型大小(MB)', null=True, blank=True)
    
    # 使用统计
    total_requests = models.IntegerField('总请求数', default=0)
    successful_requests = models.IntegerField('成功请求数', default=0)
    
    # 时间信息
    deployed_at = models.DateTimeField('部署时间', null=True, blank=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '推理模型'
        verbose_name_plural = '推理模型'
        db_table = 'inference_model'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.model_name} v{self.model_version}"
