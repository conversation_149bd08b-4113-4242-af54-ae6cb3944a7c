#!/usr/bin/env python3
import argparse
import os
import sys
import argparse
import subprocess
import base64
import tempfile
from pathlib import Path


# # 添加当前目录到Python路径
# ultralytics_dir = '/root/siton-data-c16a16a2cdc44452a1c4267121b485aa/data/ultralytics_v8'
# sys.path.insert(0, str(ultralytics_dir))
# # 设置环境变量
# os.environ["PYTHONPATH"] = str(ultralytics_dir)


def convert_model(mount_path, model_path, chip_name="910B3"):
    """
    将模型转换为ONNX格式，然后转换为OM格式
    
    Args:
        mount_path: 数据挂载路径
        model_path: 模型文件路径
        chip_name: 芯片型号
    
    Returns:
        tuple: (onnx_path, om_path) 转换后的ONNX和OM模型路径
    """
    from ultralytics import YOLO
    
    # 获取模型名称（不带扩展名）
    model_name = os.path.splitext(os.path.basename(model_path))[0]
    
    print(f"开始转换模型: {model_path}")
    
    # 加载模型并导出为ONNX格式
    model = YOLO(model_path)
    onnx_model = model.export(format="onnx", dynamic=True, simplify=True, opset=11)
    print(f"ONNX模型已导出: {onnx_model}")
    
    # 安装必要的pip包
    data_dir = os.path.join(mount_path, 'data') if mount_path else os.path.join('/root/siton-data-b496463103254f46976c4ff88ea74bc9', 'data')
    pip_packages = [
        os.path.join(data_dir, "aclruntime-0.0.2-cp39-cp39-linux_aarch64.whl"),
        os.path.join(data_dir, "ais_bench-0.0.2-py3-none-any.whl")
    ]
    
    for package in pip_packages:
        try:
            print(f"正在安装: {package}")
            result = subprocess.run(f"pip install {package}", shell=True, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            print(f"安装成功: {package}")
        except subprocess.CalledProcessError as e:
            print(f"安装失败: {package}")
            print(f"错误信息: {e.stderr}")
            return None, None  # 如果安装失败，返回None
    
    # 设置参数
    batchsize = 1
    
    # 构建输出OM模型路径
    om_model = f"{model_name}_bs{batchsize}.om"
    
    # 获取onnx_model的上一层目录
    parent_dir = os.path.dirname(os.path.dirname(onnx_model))
    output_name = f"{os.path.join(parent_dir, model_name)}_bs{batchsize}"
    
    # 构建atc命令
    atc_cmd = f"atc --framework=5 --model={onnx_model} --input_format=NCHW --input_shape=\"images:{batchsize},3,640,640\" --output_type=FP16 --output={output_name} --log=error --soc_version=Ascend{chip_name}"
    
    print(f"执行ATC命令: {atc_cmd}")
    
    # 设置环境变量
    env_setup = 'source ~/.bashrc && source /usr/local/Ascend/ascend-toolkit/set_env.sh && source /usr/local/Ascend/ascend-toolkit/8.0.RC2.2/aarch64-linux/script/set_env.sh && export LD_LIBRARY_PATH=/usr/local/Ascend/driver/lib64/driver/:/usr/local/python3.9.2/lib/:$LD_LIBRARY_PATH'
    
    # 执行atc命令
    try:
        # 使用bash -c来确保在正确的shell环境中执行，并设置环境变量
        bash_cmd = f"bash -c '{env_setup} && {atc_cmd}'"
        print(f"完整命令: {bash_cmd}")
        result = subprocess.run(bash_cmd, shell=True, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # 更新om_model为完整路径
        om_model_path = f"{output_name}.om"
        print(f"ATC转换成功: {om_model_path}")
        print(result.stdout)
        return onnx_model, om_model_path
    except subprocess.CalledProcessError as e:
        print(f"ATC转换失败: {e}")
        print(f"错误信息: {e.stderr}")
        return onnx_model, None


def process_input_source(input_source):
    """
    处理输入源，支持文件路径和base64数据

    Args:
        input_source: 输入源，可以是文件路径或base64编码的图片数据

    Returns:
        tuple: (processed_path, is_temp_file) 处理后的文件路径和是否为临时文件的标志
    """
    # 检查是否为base64数据
    if input_source.startswith('data:image/') or (len(input_source) > 100 and not os.path.exists(input_source)):
        try:
            # 处理base64数据
            if input_source.startswith('data:image/'):
                # 移除data:image/...;base64,前缀
                header, encoded_data = input_source.split(',', 1)
                # 从header中提取文件格式
                format_info = header.split(';')[0].split('/')[-1]
                if format_info.lower() in ['jpeg', 'jpg']:
                    ext = '.jpg'
                elif format_info.lower() == 'png':
                    ext = '.png'
                else:
                    ext = '.jpg'  # 默认
            else:
                # 纯base64数据，默认为jpg
                encoded_data = input_source
                ext = '.jpg'

            # 解码base64数据
            image_data = base64.b64decode(encoded_data)

            # 创建临时文件
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=ext)
            temp_file.write(image_data)
            temp_file.close()

            print(f"Base64数据已转换为临时文件: {temp_file.name}")
            return temp_file.name, True

        except Exception as e:
            print(f"处理base64数据失败: {e}")
            raise ValueError(f"无效的base64图片数据: {e}")
    else:
        # 普通文件路径
        if not os.path.exists(input_source):
            raise FileNotFoundError(f"图片文件不存在: {input_source}")
        return input_source, False

def infer_model(model_path, image_path, save_path="/workspace/", conf_thres=0.25):
    """
    使用YOLO模型进行单张图片推理

    Args:
        model_path: 模型文件路径
        image_path: 输入图片路径或base64数据
        save_path: 结果保存路径，默认为workspace
        conf_thres: 置信度阈值

    Returns:
        str: 结果图片保存路径
    """
    from ultralytics import YOLO

    # 暂时写死，使用最佳模型
    model_path = os.path.join('/root/siton-data-b496463103254f46976c4ff88ea74bc9', 'data', 'best.pt')
    print(f"加载模型: '{model_path}'")
    model = YOLO(model_path)

    # 处理输入源
    processed_image_path, is_temp_file = process_input_source(image_path)

    try:
        print(f"对图片进行推理: {processed_image_path}")
        results = model.predict(
            source=processed_image_path,
            conf=conf_thres,
            save=True,
            save_txt=True,
            save_conf=True,
            project="results",
            imgsz=1024,
            name=os.path.splitext(os.path.basename(processed_image_path))[0]
        )
    
        # 获取结果图片路径
        result_path = results[0].save_dir
        result_img = os.path.join(result_path, os.path.basename(processed_image_path))

        # 如果指定了保存路径，则复制结果
        if save_path:
            import shutil
            shutil.copy(result_img, save_path)
            result_img = save_path

        print(f"推理完成，结果保存在: {result_img}")

        # 打印检测结果
        for r in results:
            boxes = r.boxes
            print(f"检测到 {len(boxes)} 个目标")
            for i, box in enumerate(boxes):
                cls = int(box.cls[0])
                conf = float(box.conf[0])
                name = model.names[cls]
                print(f"目标 {i+1}: 类别={name}, 置信度={conf:.2f}, 坐标={box.xyxy[0].tolist()}")

        return result_img

    finally:
        # 清理临时文件
        if is_temp_file and os.path.exists(processed_image_path):
            try:
                os.unlink(processed_image_path)
                print(f"已清理临时文件: {processed_image_path}")
            except Exception as e:
                print(f"清理临时文件失败: {e}")


def batch_infer_model(model_path, image_paths_str, conf_thres=0.25, output_dir="/workspace/batch_results/"):
    """
    使用YOLO模型进行批量推理

    Args:
        model_path: 模型文件路径
        image_paths_str: 输入图片路径列表（以逗号分隔的字符串）
        output_dir: 结果保存目录，默认为workspace/batch_results/
        conf_thres: 置信度阈值

    Returns:
        list: 结果图片保存路径列表
    """
    from ultralytics import YOLO
    import os
    import shutil

    # 暂时写死，使用最佳模型
    model_path = os.path.join('/root/siton-data-b496463103254f46976c4ff88ea74bc9', 'data', 'best.pt')
    print(f"加载模型: '{model_path}'")
    model = YOLO(model_path)

    # 解析图片路径列表
    image_paths = [path.strip() for path in image_paths_str.split(',') if path.strip()]

    if not image_paths:
        print("错误: 没有提供有效的图片路径")
        return []

    print(f"开始批量推理 {len(image_paths)} 张图片")

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    results_paths = [] 
    temp_files_to_cleanup = []

    for i, image_path in enumerate(image_paths):
        print(f"\n=== 处理图片 {i+1}/{len(image_paths)}: {image_path[:50]}{'...' if len(image_path) > 50 else ''} ===")

        try:
            # 处理输入源
            processed_image_path, is_temp_file = process_input_source(image_path)

            if is_temp_file:
                temp_files_to_cleanup.append(processed_image_path)

            print(f"对图片进行推理: {processed_image_path}")

            # 为每张图片创建独立的输出目录
            image_output_dir = os.path.join(output_dir, f"image_{i+1}")
            os.makedirs(image_output_dir, exist_ok=True)

            results = model.predict(
                source=processed_image_path,
                conf=conf_thres,
                save=True,
                save_txt=True,
                save_conf=True,
                project=image_output_dir,
                imgsz=1024,
                name="result"
            )

            # 获取结果图片路径
            result_path = results[0].save_dir
            result_img = os.path.join(result_path, os.path.basename(processed_image_path))

            # 复制结果到输出目录
            final_result_path = os.path.join(image_output_dir, f"result_{i+1}.jpg")
            if os.path.exists(result_img):
                shutil.copy(result_img, final_result_path)
                print(f"推理完成，结果保存在: {final_result_path}")
                print(f"BATCH_INFERENCE_RESULT_PATH:{final_result_path}")
                results_paths.append(final_result_path)
            else:
                print(f"警告: 结果图片不存在: {result_img}")
                results_paths.append(None)

            # 打印检测结果
            for r in results:
                boxes = r.boxes
                print(f"检测到 {len(boxes)} 个目标")
                for j, box in enumerate(boxes):
                    cls = int(box.cls[0])
                    conf = float(box.conf[0])
                    name = model.names[cls]
                    print(f"目标 {j+1}: 类别={name}, 置信度={conf:.2f}, 坐标={box.xyxy[0].tolist()}")

        except Exception as e:
            print(f"处理图片 {i+1} 时发生错误: {e}")
            results_paths.append(None)

    # 清理临时文件
    for temp_file in temp_files_to_cleanup:
        try:
            if os.path.exists(temp_file):
                os.unlink(temp_file)
                print(f"已清理临时文件: {temp_file}")
        except Exception as e:
            print(f"清理临时文件失败: {e}")

    print(f"\n批量推理完成，成功处理 {len([p for p in results_paths if p])} / {len(image_paths)} 张图片")
    return results_paths


def main():
    parser = argparse.ArgumentParser(description="YOLOv8模型转换与推理工具")
    subparsers = parser.add_subparsers(dest="command", help="选择命令")
    
    # 转换模型命令
    convert_parser = subparsers.add_parser("convert", help="转换模型格式")
    convert_parser.add_argument('--pt', required=True, help='输入模型路径(.pt文件)')
    convert_parser.add_argument('--chip_name', default="910B3", help='芯片型号')
    convert_parser.add_argument('--mount_path', help='数据挂载路径')
    convert_parser.add_argument('--ultralytics_mount_path', help='ultralytics源码路径')
    
    # 推理命令
    infer_parser = subparsers.add_parser("infer", help="使用模型进行单张图片推理")
    infer_parser.add_argument('--model', required=True, help='模型路径(.pt, .onnx或其他支持的格式)')
    infer_parser.add_argument('--image', required=True, help='输入图片路径')
    infer_parser.add_argument('--save_path', help='结果保存路径')
    # 添加数据和代码路径参数
    infer_parser.add_argument('--mount_path', help='数据挂载路径')
    infer_parser.add_argument('--ultralytics_mount_path', help='ultralytics源码路径')
    infer_parser.add_argument('--conf', type=float, default=0.25, help='置信度阈值')

    # 批量推理命令
    batch_infer_parser = subparsers.add_parser("batch_infer", help="使用模型进行批量推理")
    batch_infer_parser.add_argument('--model', required=True, help='模型路径(.pt, .onnx或其他支持的格式)')
    batch_infer_parser.add_argument('--images', required=True, help='输入图片路径列表（以逗号分隔）')
    batch_infer_parser.add_argument('--output_dir', help='结果保存目录')
    # 添加数据和代码路径参数
    batch_infer_parser.add_argument('--mount_path', help='数据挂载路径')
    batch_infer_parser.add_argument('--ultralytics_mount_path', help='ultralytics源码路径')
    batch_infer_parser.add_argument('--conf', type=float, default=0.25, help='置信度阈值')

    args = parser.parse_args()


    # 如果指定了ultralytics_dir，则更新Python路径
    if args.ultralytics_mount_path:
        sys.path.insert(0, str(args.ultralytics_mount_path))
        os.environ["PYTHONPATH"] = str(args.ultralytics_mount_path)
    
    # 如果指定了mount_path，则更新模型路径
    # if args.mount_path:
    #     args.model = os.path.join(args.mount_path, 'data', os.path.basename(args.model))
    
    if args.command == "convert":
        convert_model(args.mount_path, args.pt, args.chip_name)
    elif args.command == "infer":
        infer_model(args.model, args.image, args.save_path, args.conf)
    elif args.command == "batch_infer":
        if args.output_dir:
            batch_infer_model(args.model, args.images, args.conf, args.output_dir)
        else:
            batch_infer_model(args.model, args.images, args.conf)
    else:
        parser.print_help()


if __name__ == '__main__':
    main()