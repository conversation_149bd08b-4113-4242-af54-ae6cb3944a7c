<template>
  <div class="evaluation-step">
    <div class="step-header q-mb-lg">
      <h5 class="q-my-none">结果评估</h5>
      <p class="text-grey-6">查看训练结果和模型性能评估</p>
    </div>

    <div class="evaluation-content">
      <!-- 训练指标概览 -->
      <q-card flat bordered class="q-mb-md">
        <q-card-section>
          <div class="text-h6 q-mb-md">训练指标概览</div>
          
          <div class="metrics-grid">
            <div class="metric-card">
              <div class="metric-value">{{ bestMetrics.map50_95?.toFixed(3) || 'N/A' }}</div>
              <div class="metric-label">mAP@0.5:0.95</div>
            </div>
            <div class="metric-card">
              <div class="metric-value">{{ bestMetrics.map50?.toFixed(3) || 'N/A' }}</div>
              <div class="metric-label">mAP@0.5</div>
            </div>
            <div class="metric-card">
              <div class="metric-value">{{ bestMetrics.precision?.toFixed(3) || 'N/A' }}</div>
              <div class="metric-label">精确率</div>
            </div>
            <div class="metric-card">
              <div class="metric-value">{{ bestMetrics.recall?.toFixed(3) || 'N/A' }}</div>
              <div class="metric-label">召回率</div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 训练曲线图表 -->
      <q-card flat bordered class="q-mb-md">
        <q-card-section>
          <div class="text-h6 q-mb-md">训练曲线</div>
          
          <div class="chart-container">
            <div ref="chartRef" style="width: 100%; height: 400px;"></div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 模型评估配置 -->
      <q-card flat bordered class="q-mb-md">
        <q-card-section>
          <div class="text-h6 q-mb-md">模型评估配置</div>
          
          <q-form @submit.prevent="runEvaluation" class="evaluation-form">
            <div class="row q-gutter-md">
              <div class="col-5">
                <q-input
                  v-model="evaluationConfig.evaluation_name"
                  label="评估名称"
                  outlined
                  :rules="[val => !!val || '请输入评估名称']"
                />
              </div>
              <div class="col-6">
                <q-input
                  v-model="evaluationConfig.test_dataset_path"
                  label="测试数据集路径"
                  outlined
                  :rules="[val => !!val || '请输入测试数据集路径']"
                />
              </div>
            </div>

            <div class="row q-gutter-md q-mt-md">
              <div class="col-3">
                <q-input
                  v-model.number="evaluationConfig.confidence_threshold"
                  label="置信度阈值"
                  type="number"
                  outlined
                  min="0.1"
                  max="0.9"
                  step="0.1"
                />
              </div>
              <div class="col-3">
                <q-input
                  v-model.number="evaluationConfig.iou_threshold"
                  label="IoU阈值"
                  type="number"
                  outlined
                  min="0.1"
                  max="0.9"
                  step="0.1"
                />
              </div>
              <div class="col-3">
                <q-toggle
                  v-model="evaluationConfig.save_results"
                  label="保存评估结果"
                />
              </div>
            </div>

            <div class="evaluation-actions q-mt-md">
              <q-btn
                color="secondary"
                label="运行评估"
                type="submit"
                :loading="evaluating"
                :disable="!isEvaluationValid"
              />
            </div>
          </q-form>
        </q-card-section>
      </q-card>

      <!-- 评估结果 -->
      <q-card flat bordered class="q-mb-md" v-if="evaluationResults.length > 0">
        <q-card-section>
          <div class="text-h6 q-mb-md">评估结果</div>
          
          <q-table
            :rows="evaluationResults"
            :columns="evaluationColumns"
            row-key="id"
            flat
            bordered
          >
            <template v-slot:body-cell-status="props">
              <q-td :props="props">
                <q-badge
                  :color="getEvaluationStatusColor(props.row.status)"
                  :label="getEvaluationStatusText(props.row.status)"
                />
              </q-td>
            </template>

            <template v-slot:body-cell-actions="props">
              <q-td :props="props">
                <q-btn
                  flat
                  dense
                  icon="visibility"
                  color="primary"
                  @click="viewEvaluationDetails(props.row)"
                >
                  <q-tooltip>查看详情</q-tooltip>
                </q-btn>
              </q-td>
            </template>
          </q-table>
        </q-card-section>
      </q-card>

      <!-- 操作按钮 -->
      <div class="step-actions q-mt-lg">
        <q-btn
          flat
          label="上一步"
          @click="$emit('previous')"
          class="q-mr-md"
        />
        
        <q-btn
          color="secondary"
          label="保存评估配置"
          @click="handleSave"
          :loading="saving"
          class="q-mr-md"
        />
        
        <q-btn
          color="primary"
          label="下一步"
          @click="handleNext"
          :loading="saving"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useQuasar } from 'quasar'
import * as echarts from 'echarts'
import { api } from 'boot/axios'

const props = defineProps({
  workflow: {
    type: Object,
    required: true
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['save', 'next', 'previous'])

const $q = useQuasar()

// 响应式数据
const chartRef = ref(null)
let chartInstance = null

const bestMetrics = ref({
  map50_95: 0.65,
  map50: 0.82,
  precision: 0.78,
  recall: 0.71
})

const evaluationConfig = ref({
  evaluation_name: '模型评估-' + new Date().toLocaleDateString(),
  test_dataset_path: '/data/test_dataset',
  confidence_threshold: 0.5,
  iou_threshold: 0.5,
  save_results: true
})

const evaluationResults = ref([])
const saving = ref(false)
const evaluating = ref(false)

// 评估结果表格列定义
const evaluationColumns = [
  {
    name: 'evaluation_name',
    label: '评估名称',
    align: 'left',
    field: 'evaluation_name'
  },
  {
    name: 'overall_accuracy',
    label: '总体准确率',
    align: 'center',
    field: 'overall_accuracy',
    format: val => val ? val.toFixed(3) : 'N/A'
  },
  {
    name: 'map50_95',
    label: 'mAP@0.5:0.95',
    align: 'center',
    field: 'map50_95',
    format: val => val ? val.toFixed(3) : 'N/A'
  },
  {
    name: 'status',
    label: '状态',
    align: 'center',
    field: 'status'
  },
  {
    name: 'created_at',
    label: '创建时间',
    align: 'left',
    field: 'created_at',
    format: val => new Date(val).toLocaleString()
  },
  {
    name: 'actions',
    label: '操作',
    align: 'center'
  }
]

// 计算属性
const isEvaluationValid = computed(() => {
  return evaluationConfig.value.evaluation_name && 
         evaluationConfig.value.test_dataset_path
})

// 监听props变化
watch(() => props.data, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(evaluationConfig.value, newData)
  }
}, { immediate: true })

watch(() => props.workflow, (newWorkflow) => {
  if (newWorkflow.best_metrics) {
    bestMetrics.value = newWorkflow.best_metrics
  }
}, { immediate: true })

// 方法
const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)
  
  // 模拟训练数据
  const epochs = Array.from({length: 100}, (_, i) => i + 1)
  const trainLoss = epochs.map(e => 1.2 * Math.exp(-e/30) + 0.1 + Math.random() * 0.05)
  const valLoss = epochs.map(e => 1.3 * Math.exp(-e/25) + 0.15 + Math.random() * 0.05)
  const map50 = epochs.map(e => 0.8 * (1 - Math.exp(-e/20)) + Math.random() * 0.02)

  const option = {
    title: {
      text: '训练指标曲线'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['训练损失', '验证损失', 'mAP@0.5']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: epochs
    },
    yAxis: [
      {
        type: 'value',
        name: '损失',
        position: 'left'
      },
      {
        type: 'value',
        name: 'mAP',
        position: 'right',
        min: 0,
        max: 1
      }
    ],
    series: [
      {
        name: '训练损失',
        type: 'line',
        data: trainLoss,
        smooth: true,
        lineStyle: {
          color: '#ff6b6b'
        }
      },
      {
        name: '验证损失',
        type: 'line',
        data: valLoss,
        smooth: true,
        lineStyle: {
          color: '#4ecdc4'
        }
      },
      {
        name: 'mAP@0.5',
        type: 'line',
        yAxisIndex: 1,
        data: map50,
        smooth: true,
        lineStyle: {
          color: '#45b7d1'
        }
      }
    ]
  }

  chartInstance.setOption(option)
}

const runEvaluation = async () => {
  if (!isEvaluationValid.value) {
    $q.notify({
      type: 'negative',
      message: '请完善评估配置'
    })
    return
  }

  evaluating.value = true
  try {
    // 模拟评估过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const newEvaluation = {
      id: Date.now(),
      evaluation_name: evaluationConfig.value.evaluation_name,
      overall_accuracy: 0.85 + Math.random() * 0.1,
      precision: 0.82 + Math.random() * 0.1,
      recall: 0.78 + Math.random() * 0.1,
      map50_95: 0.65 + Math.random() * 0.1,
      status: 'completed',
      created_at: new Date().toISOString()
    }
    
    evaluationResults.value.unshift(newEvaluation)
    
    $q.notify({
      type: 'positive',
      message: '模型评估完成'
    })
    
  } catch (error) {
    console.error('评估失败:', error)
    $q.notify({
      type: 'negative',
      message: '模型评估失败'
    })
  } finally {
    evaluating.value = false
  }
}

const viewEvaluationDetails = (evaluation) => {
  $q.dialog({
    title: '评估详情',
    message: `
      评估名称: ${evaluation.evaluation_name}
      总体准确率: ${evaluation.overall_accuracy?.toFixed(3)}
      精确率: ${evaluation.precision?.toFixed(3)}
      召回率: ${evaluation.recall?.toFixed(3)}
      mAP@0.5:0.95: ${evaluation.map50_95?.toFixed(3)}
    `,
    html: true
  })
}

const getEvaluationStatusColor = (status) => {
  const colors = {
    pending: 'orange',
    running: 'blue',
    completed: 'green',
    failed: 'red'
  }
  return colors[status] || 'grey'
}

const getEvaluationStatusText = (status) => {
  const texts = {
    pending: '等待中',
    running: '评估中',
    completed: '已完成',
    failed: '失败'
  }
  return texts[status] || '未知'
}

const handleSave = async () => {
  saving.value = true
  try {
    console.log('EvaluationStep: 保存评估配置')
    emit('save', 'step4_evaluation', {
      evaluation_config: evaluationConfig.value,
      evaluation_results: evaluationResults.value
    })
    
    $q.notify({
      type: 'positive',
      message: '评估配置已保存'
    })
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

const handleNext = async () => {
  console.log('EvaluationStep: 点击下一步')
  
  saving.value = true
  try {
    console.log('发送save事件:', 'step4_evaluation')
    emit('save', 'step4_evaluation', {
      evaluation_config: evaluationConfig.value,
      evaluation_results: evaluationResults.value
    })
    
    console.log('发送next事件')
    emit('next')
    
    $q.notify({
      type: 'positive',
      message: '步骤4数据已保存'
    })
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

// 生命周期
onMounted(() => {
  initChart()
  loadEvaluationResults()
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
})

const loadEvaluationResults = async () => {
  try {
    // 这里可以从API加载真实的评估结果
    // const response = await api.get(`backend/workflows/${props.workflow.id}/evaluation/`)
    // evaluationResults.value = response.results
  } catch (error) {
    console.error('加载评估结果失败:', error)
  }
}
</script>

<style scoped>
.evaluation-step {
  padding: 24px;
}

.step-header {
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 16px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.metric-card {
  text-align: center;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 8px;
}

.metric-label {
  color: #666;
  font-size: 14px;
}

.chart-container {
  width: 100%;
  height: 400px;
}

.evaluation-actions {
  display: flex;
  justify-content: flex-end;
}

.step-actions {
  display: flex;
  justify-content: flex-end;
  padding-top: 24px;
  border-top: 1px solid #e0e0e0;
}
</style>
