<template>
  <div class="evaluation-step">
    <div class="text-h6 q-mb-md">结果评估</div>
    <p class="text-grey-6 q-mb-lg">查看训练结果并进行模型评估</p>

    <!-- 训练结果概览 -->
    <q-card flat bordered class="q-mb-md">
      <q-card-section>
        <div class="text-subtitle1 q-mb-md">训练结果概览</div>
        
        <div class="row q-gutter-md">
          <div class="col-12 col-md-3">
            <q-card class="metric-card text-center">
              <q-card-section>
                <div class="text-h6 text-positive">{{ bestMetrics.accuracy || 'N/A' }}</div>
                <div class="text-caption text-grey-6">最佳准确率</div>
              </q-card-section>
            </q-card>
          </div>
          
          <div class="col-12 col-md-3">
            <q-card class="metric-card text-center">
              <q-card-section>
                <div class="text-h6 text-info">{{ bestMetrics.loss || 'N/A' }}</div>
                <div class="text-caption text-grey-6">最低损失</div>
              </q-card-section>
            </q-card>
          </div>
          
          <div class="col-12 col-md-3" v-if="bestMetrics.map50">
            <q-card class="metric-card text-center">
              <q-card-section>
                <div class="text-h6 text-warning">{{ bestMetrics.map50 }}</div>
                <div class="text-caption text-grey-6">mAP@0.5</div>
              </q-card-section>
            </q-card>
          </div>
          
          <div class="col-12 col-md-3">
            <q-card class="metric-card text-center">
              <q-card-section>
                <div class="text-h6 text-purple">{{ trainingDuration }}</div>
                <div class="text-caption text-grey-6">训练时长</div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- 模型评估 -->
    <q-card flat bordered class="q-mb-md">
      <q-card-section>
        <div class="text-subtitle1 q-mb-md">模型评估</div>
        
        <div class="row q-gutter-md">
          <div class="col-12 col-md-6">
            <q-btn
              outline
              color="primary"
              label="运行模型评估"
              icon="assessment"
              @click="runEvaluation"
              :loading="evaluating"
              :disable="!workflow.model_path"
            />
          </div>
          
          <div class="col-12 col-md-6" v-if="evaluationResult">
            <q-chip
              :color="evaluationResult.success ? 'positive' : 'negative'"
              text-color="white"
              :icon="evaluationResult.success ? 'check' : 'error'"
            >
              {{ evaluationResult.message }}
            </q-chip>
          </div>
        </div>

        <!-- 评估结果 -->
        <div v-if="evaluationData" class="q-mt-md">
          <q-separator class="q-mb-md" />
          
          <div class="row q-gutter-md">
            <div class="col-12 col-md-6">
              <div class="text-subtitle2 q-mb-sm">总体指标</div>
              <q-list dense>
                <q-item>
                  <q-item-section>
                    <q-item-label>总体准确率</q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <q-item-label>{{ (evaluationData.overall_accuracy * 100).toFixed(2) }}%</q-item-label>
                  </q-item-section>
                </q-item>
                
                <q-item>
                  <q-item-section>
                    <q-item-label>测试损失</q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <q-item-label>{{ evaluationData.test_loss?.toFixed(4) || 'N/A' }}</q-item-label>
                  </q-item-section>
                </q-item>
                
                <q-item>
                  <q-item-section>
                    <q-item-label>测试样本数</q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <q-item-label>{{ evaluationData.test_samples_count || 'N/A' }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
            
            <div class="col-12 col-md-6" v-if="evaluationData.class_accuracies">
              <div class="text-subtitle2 q-mb-sm">各类别准确率</div>
              <q-list dense style="max-height: 200px; overflow-y: auto;">
                <q-item 
                  v-for="(accuracy, className) in evaluationData.class_accuracies" 
                  :key="className"
                >
                  <q-item-section>
                    <q-item-label>{{ className }}</q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <q-item-label>{{ (accuracy * 100).toFixed(2) }}%</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- 模型部署 -->
    <q-card flat bordered class="q-mb-md">
      <q-card-section>
        <div class="text-subtitle1 q-mb-md">模型部署</div>
        
        <q-form @submit.prevent="deployModel" class="q-gutter-md">
          <div class="row q-gutter-md">
            <div class="col-12 col-md-6">
              <q-input
                v-model="deploymentConfig.model_name"
                label="模型名称"
                :rules="[val => !!val || '请输入模型名称']"
              />
            </div>
            
            <div class="col-12 col-md-6">
              <q-input
                v-model="deploymentConfig.model_version"
                label="模型版本"
                :rules="[val => !!val || '请输入模型版本']"
              />
            </div>
          </div>

          <div class="row q-gutter-md">
            <div class="col-12 col-md-6">
              <q-select
                v-model="deploymentConfig.deployment_type"
                label="部署类型"
                :options="deploymentOptions"
                option-value="value"
                option-label="label"
                emit-value
                map-options
              />
            </div>
            
            <div class="col-12 col-md-6">
              <q-input
                v-model="deploymentConfig.description"
                label="描述"
                type="textarea"
                rows="2"
              />
            </div>
          </div>

          <div class="row justify-end q-gutter-md">
            <q-btn
              color="primary"
              label="部署模型"
              type="submit"
              icon="cloud_upload"
              :loading="deploying"
              :disable="!workflow.model_path"
            />
          </div>
        </q-form>
      </q-card-section>
    </q-card>

    <!-- 操作按钮 -->
    <div class="row justify-between q-mt-lg">
      <q-btn
        flat
        color="primary"
        label="上一步"
        icon="arrow_back"
        @click="$emit('previous')"
      />
      
      <div class="q-gutter-md">
        <q-btn
          color="primary"
          label="保存评估结果"
          @click="handleSave"
          :loading="saving"
        />
        
        <q-btn
          color="positive"
          label="完成工作流"
          @click="handleComplete"
          :loading="completing"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useQuasar } from 'quasar'
import { api } from 'boot/axios'

const props = defineProps({
  workflow: {
    type: Object,
    required: true
  },
  stepData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['save', 'complete', 'previous'])

const $q = useQuasar()

// 响应式数据
const evaluating = ref(false)
const deploying = ref(false)
const saving = ref(false)
const completing = ref(false)

const evaluationResult = ref(null)
const evaluationData = ref(null)
const bestMetrics = ref({})

const deploymentConfig = ref({
  model_name: '',
  model_version: 'v1.0',
  deployment_type: 'api',
  description: ''
})

const deploymentOptions = [
  { label: 'API服务', value: 'api' },
  { label: '边缘设备', value: 'edge' },
  { label: '批量推理', value: 'batch' },
  { label: '仅存储', value: 'storage' }
]

// 计算属性
const trainingDuration = computed(() => {
  if (props.workflow.started_at && props.workflow.completed_at) {
    const start = new Date(props.workflow.started_at)
    const end = new Date(props.workflow.completed_at)
    const duration = Math.floor((end - start) / 1000 / 60) // 分钟
    
    if (duration < 60) {
      return `${duration}分钟`
    } else {
      const hours = Math.floor(duration / 60)
      const minutes = duration % 60
      return `${hours}小时${minutes}分钟`
    }
  }
  return 'N/A'
})

// 方法
const loadEvaluationData = async () => {
  try {
    const response = await api.get(`backend/workflows/${props.workflow.id}/evaluation/`)
    evaluationData.value = response
  } catch (error) {
    console.error('加载评估数据失败:', error)
  }
}

const runEvaluation = async () => {
  evaluating.value = true
  try {
    const response = await api.post(`backend/workflows/${props.workflow.id}/evaluation/`, {
      model_path: props.workflow.model_path,
      test_data_path: props.workflow.step1_data_config?.dataset_path
    })
    
    evaluationResult.value = {
      success: true,
      message: '评估完成'
    }
    
    evaluationData.value = response
    
    $q.notify({
      type: 'positive',
      message: '模型评估完成'
    })
  } catch (error) {
    console.error('模型评估失败:', error)
    evaluationResult.value = {
      success: false,
      message: '评估失败'
    }
    
    $q.notify({
      type: 'negative',
      message: '模型评估失败'
    })
  } finally {
    evaluating.value = false
  }
}

const deployModel = async () => {
  if (!deploymentConfig.value.model_name) {
    $q.notify({
      type: 'negative',
      message: '请输入模型名称'
    })
    return
  }

  deploying.value = true
  try {
    await api.post('backend/inference-models/', {
      workflow: props.workflow.id,
      model_name: deploymentConfig.value.model_name,
      model_version: deploymentConfig.value.model_version,
      model_path: props.workflow.model_path,
      deployment_config: {
        deployment_type: deploymentConfig.value.deployment_type,
        description: deploymentConfig.value.description
      },
      status: 'ready'
    })
    
    $q.notify({
      type: 'positive',
      message: '模型部署成功'
    })
  } catch (error) {
    console.error('模型部署失败:', error)
    $q.notify({
      type: 'negative',
      message: '模型部署失败'
    })
  } finally {
    deploying.value = false
  }
}

const handleSave = async () => {
  saving.value = true
  try {
    const stepData = {
      evaluation_data: evaluationData.value,
      deployment_config: deploymentConfig.value,
      completed_at: new Date().toISOString()
    }
    
    emit('save', 'step4_evaluation', stepData)
    
    $q.notify({
      type: 'positive',
      message: '评估结果已保存'
    })
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

const handleComplete = async () => {
  completing.value = true
  try {
    const stepData = {
      evaluation_data: evaluationData.value,
      deployment_config: deploymentConfig.value,
      completed_at: new Date().toISOString()
    }
    
    emit('complete', stepData)
    
    $q.notify({
      type: 'positive',
      message: '训练工作流已完成'
    })
  } catch (error) {
    console.error('完成工作流失败:', error)
  } finally {
    completing.value = false
  }
}

// 初始化数据
onMounted(() => {
  // 加载最佳指标
  if (props.workflow.best_metrics) {
    bestMetrics.value = props.workflow.best_metrics
  }
  
  // 加载评估数据
  loadEvaluationData()
  
  // 如果有已保存的数据，加载它
  if (props.stepData && Object.keys(props.stepData).length > 0) {
    if (props.stepData.deployment_config) {
      deploymentConfig.value = { ...deploymentConfig.value, ...props.stepData.deployment_config }
    }
    if (props.stepData.evaluation_data) {
      evaluationData.value = props.stepData.evaluation_data
    }
  }
  
  // 设置默认模型名称
  if (!deploymentConfig.value.model_name && props.workflow.name) {
    deploymentConfig.value.model_name = `${props.workflow.name}_model`
  }
})
</script>

<style scoped>
.evaluation-step {
  max-width: 800px;
}

.metric-card {
  min-height: 80px;
  display: flex;
  align-items: center;
}
</style>
