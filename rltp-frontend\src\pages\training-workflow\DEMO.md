# 训练工作流右侧任务列表演示

## 演示步骤

### 1. 启动应用
```bash
# 在项目根目录下
cd rltp-frontend
npm run dev
```

### 2. 访问训练工作流页面
1. 打开浏览器访问：`http://localhost:9002`
2. 导航到训练工作流管理页面
3. 创建一个新的训练工作流或选择现有的工作流

### 3. 查看右侧任务列表
- 页面右侧会显示"智能试训环境"面板
- 列表中会显示模拟的训练任务，包括：
  - 目标识别-YoloV8-2024-12-15 09:30:15 (训练中 75%)
  - 目标识别-YoloV5-2024-12-15 09:30:15 (训练中 45%)
  - 目标识别-YoloV8-2024-12-15 09:30:15 (已完成 100%)
  - 等等...

### 4. 测试步骤保存功能
1. **数据配置步骤**：
   - 填写数据集配置信息
   - 点击"保存并下一步"
   - 观察右侧列表是否刷新

2. **模型配置步骤**：
   - 选择模型架构和参数
   - 点击"保存并下一步"
   - 观察右侧列表更新

3. **训练配置步骤**：
   - 设置训练参数
   - 点击"启动训练"
   - 观察右侧列表中是否出现新的训练任务

4. **结果评估步骤**：
   - 查看训练结果
   - 点击"完成工作流"
   - 观察任务状态更新为已完成

### 5. 观察实时更新
- 训练中的任务每5秒会自动更新进度
- 进度条会逐渐增长
- 当进度达到100%时，状态会变为"已完成"

### 6. 测试任务交互
- 点击右侧列表中的任务卡片
- 如果任务有关联的工作流ID，会跳转到对应的工作流页面

## 预期效果

### 页面布局
```
┌─────────────────────────────────┬─────────────────────┐
│                                 │   智能试训环境      │
│        训练工作流步骤           │                     │
│                                 │  ┌─────────────────┐ │
│  ┌─────────────────────────────┐ │  │ 目标识别-YoloV8 │ │
│  │ 1. 数据配置 ✓               │ │  │ 训练中 75%      │ │
│  │ 2. 模型配置 ✓               │ │  │ 16 CPU          │ │
│  │ 3. 训练配置 ▶               │ │  │ 12-15 09:30     │ │
│  │ 4. 结果评估                 │ │  └─────────────────┘ │
│  └─────────────────────────────┘ │                     │
│                                 │  ┌─────────────────┐ │
│        当前步骤内容             │  │ 目标识别-YoloV5 │ │
│                                 │  │ 训练中 45%      │ │
│                                 │  │ 12 CPU          │ │
│                                 │  │ 12-15 09:00     │ │
│                                 │  └─────────────────┘ │
│                                 │                     │
│                                 │  ┌─────────────────┐ │
│                                 │  │ 目标识别-YoloV8 │ │
│                                 │  │ 已完成 100%     │ │
│                                 │  │ 16 CPU          │ │
│                                 │  │ 12-15 08:30     │ │
│                                 │  └─────────────────┘ │
└─────────────────────────────────┴─────────────────────┘
```

### 任务卡片样式
- **训练中任务**：橙色边框，显示进度条
- **已完成任务**：绿色图标，100%进度
- **其他状态**：灰色或蓝色图标

### 交互效果
- 鼠标悬停：卡片轻微上移
- 点击反馈：视觉反馈效果
- 自动刷新：进度条平滑更新

## 故障排除

### 1. 右侧面板不显示
- 检查 TrainingTaskPanel.vue 组件是否正确导入
- 检查控制台是否有JavaScript错误

### 2. 任务列表为空
- 检查API调用是否成功
- 查看网络请求是否返回数据
- 确认模拟数据是否正确加载

### 3. 保存后列表不更新
- 检查 taskPanel ref 是否正确设置
- 确认 loadTasks 方法是否被调用
- 查看控制台日志

### 4. 进度不更新
- 检查定时器是否正常启动
- 确认组件卸载时定时器是否正确清理

## 开发调试

### 1. 查看组件状态
```javascript
// 在浏览器控制台中
console.log('任务列表:', this.$refs.taskPanel?.tasks)
console.log('当前工作流:', this.workflow)
```

### 2. 手动触发刷新
```javascript
// 在浏览器控制台中
this.$refs.taskPanel?.loadTasks()
```

### 3. 模拟API响应
- 修改 TrainingTaskPanel.vue 中的 loadMockTasks 方法
- 添加更多测试数据

## 后续优化

1. **性能优化**：
   - 实现虚拟滚动（任务数量很多时）
   - 优化API调用频率

2. **用户体验**：
   - 添加加载动画
   - 优化错误处理

3. **功能扩展**：
   - 添加任务筛选
   - 支持任务操作（暂停、取消等）
