<template>
  <q-page class="q-pa-md">
    <!-- 页面标题 -->
    <div class="row items-center q-mb-lg">
      <div class="col">
        <h4 class="q-my-none">训练任务概览</h4>
        <p class="text-grey-6 q-mb-none">管理您的AI模型训练任务</p>
      </div>
      <div class="col-auto">
        <q-btn
          color="primary"
          icon="add"
          label="新建训练任务"
          @click="showCreateDialog = true"
        />
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row q-gutter-md q-mb-lg">
      <div class="col-12 col-md-3">
        <q-card class="stat-card">
          <q-card-section>
            <div class="text-h6">{{ totalStats.total_workflows }}</div>
            <div class="text-grey-6">总任务数</div>
          </q-card-section>
        </q-card>
      </div>
      <div class="col-12 col-md-3">
        <q-card class="stat-card">
          <q-card-section>
            <div class="text-h6 text-positive">{{ totalStats.completed_workflows }}</div>
            <div class="text-grey-6">已完成</div>
          </q-card-section>
        </q-card>
      </div>
      <div class="col-12 col-md-3">
        <q-card class="stat-card">
          <q-card-section>
            <div class="text-h6 text-warning">{{ totalStats.active_workflows }}</div>
            <div class="text-grey-6">进行中</div>
          </q-card-section>
        </q-card>
      </div>
      <div class="col-12 col-md-3">
        <q-card class="stat-card">
          <q-card-section>
            <div class="text-h6 text-info">{{ totalStats.inference_models }}</div>
            <div class="text-grey-6">推理模型</div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- 任务类型列表 -->
    <div class="row q-gutter-md">
      <div 
        v-for="(taskType, key) in taskTypes" 
        :key="key"
        class="col-12 col-md-6"
      >
        <q-card class="task-type-card">
          <q-card-section>
            <div class="row items-center q-mb-md">
              <div class="col">
                <h6 class="q-my-none">{{ taskType.display_name }}</h6>
                <div class="text-grey-6">
                  总计: {{ taskType.total_count }} | 
                  已完成: {{ taskType.completed_count }} | 
                  训练中: {{ taskType.training_count }}
                </div>
              </div>
              <div class="col-auto">
                <q-btn
                  flat
                  round
                  icon="add"
                  size="sm"
                  @click="createWorkflow(key)"
                >
                  <q-tooltip>新建{{ taskType.display_name }}任务</q-tooltip>
                </q-btn>
              </div>
            </div>

            <!-- 最近任务列表 -->
            <div v-if="taskType.recent_workflows.length > 0">
              <q-separator class="q-mb-md" />
              <div class="recent-workflows">
                <div 
                  v-for="workflow in taskType.recent_workflows" 
                  :key="workflow.id"
                  class="workflow-item q-pa-sm cursor-pointer"
                  @click="navigateToWorkflow(workflow)"
                >
                  <div class="row items-center">
                    <div class="col">
                      <div class="text-subtitle2">{{ workflow.name }}</div>
                      <div class="text-caption text-grey-6">
                        {{ workflow.current_step_display }} • 
                        {{ formatDate(workflow.updated_at) }}
                      </div>
                    </div>
                    <div class="col-auto">
                      <q-circular-progress
                        :value="workflow.progress_percentage"
                        size="30px"
                        :thickness="0.2"
                        color="primary"
                        track-color="grey-3"
                        class="q-mr-sm"
                      >
                        <div class="text-caption">{{ workflow.progress_percentage }}%</div>
                      </q-circular-progress>
                      <q-icon name="chevron_right" color="grey-5" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="text-center text-grey-6 q-py-md">
              暂无{{ taskType.display_name }}任务
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- 创建任务对话框 -->
    <q-dialog v-model="showCreateDialog">
      <q-card style="min-width: 400px">
        <q-card-section>
          <div class="text-h6">新建训练任务</div>
        </q-card-section>

        <q-card-section>
          <q-form @submit="createNewWorkflow" class="q-gutter-md">
            <q-input
              v-model="newWorkflow.name"
              label="任务名称"
              required
              :rules="[val => !!val || '请输入任务名称']"
            />
            
            <q-input
              v-model="newWorkflow.description"
              label="任务描述"
              type="textarea"
              rows="3"
            />
            
            <q-select
              v-model="newWorkflow.task_type"
              label="任务类型"
              :options="taskTypeOptions"
              option-value="value"
              option-label="label"
              emit-value
              map-options
              required
              :rules="[val => !!val || '请选择任务类型']"
            />
          </q-form>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" v-close-popup />
          <q-btn 
            color="primary" 
            label="创建" 
            @click="createNewWorkflow"
            :loading="creating"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import { api } from 'boot/axios'

const router = useRouter()
const $q = useQuasar()

// 响应式数据
const taskTypes = ref({})
const totalStats = ref({
  total_workflows: 0,
  completed_workflows: 0,
  active_workflows: 0,
  inference_models: 0
})

const showCreateDialog = ref(false)
const creating = ref(false)
const newWorkflow = ref({
  name: '',
  description: '',
  task_type: ''
})

const taskTypeOptions = [
  { label: '目标识别-YoloV8', value: 'object_detection_yolov8' },
  { label: '目标识别-YoloV5', value: 'object_detection_yolov5' },
  { label: '图像分类', value: 'image_classification' },
  { label: '语义分割', value: 'semantic_segmentation' },
  { label: '实例分割', value: 'instance_segmentation' }
]

// 方法
const loadOverviewData = async () => {
  try {
    const response = await api.get('backend/workflows/overview/')
    taskTypes.value = response.task_types
    totalStats.value = response.total_stats
  } catch (error) {
    console.error('加载概览数据失败:', error)
    $q.notify({
      type: 'negative',
      message: '加载数据失败'
    })
  }
}

const navigateToWorkflow = (workflow) => {
  // 根据当前步骤导航到对应页面
  const stepRoutes = {
    'draft': 'step1',
    'step1_data': 'step1',
    'step2_model': 'step2',
    'step3_training': 'step3',
    'step4_evaluation': 'step4',
    'completed': 'step4'
  }
  
  const step = stepRoutes[workflow.current_step] || 'step1'
  router.push(`/training-workflow/${workflow.id}/${step}`)
}

const createWorkflow = (taskType) => {
  newWorkflow.value.task_type = taskType
  showCreateDialog.value = true
}

const createNewWorkflow = async () => {
  if (!newWorkflow.value.name || !newWorkflow.value.task_type) {
    $q.notify({
      type: 'negative',
      message: '请填写必要信息'
    })
    return
  }

  creating.value = true
  try {
    const response = await api.post('backend/workflows/', newWorkflow.value)
    
    $q.notify({
      type: 'positive',
      message: '任务创建成功'
    })
    
    showCreateDialog.value = false
    newWorkflow.value = { name: '', description: '', task_type: '' }
    
    // 导航到新创建的任务
    router.push(`/training-workflow/${response.id}/step1`)
    
  } catch (error) {
    console.error('创建任务失败:', error)
    $q.notify({
      type: 'negative',
      message: '创建任务失败'
    })
  } finally {
    creating.value = false
  }
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 生命周期
onMounted(() => {
  loadOverviewData()
})
</script>

<style scoped>
.stat-card {
  text-align: center;
}

.task-type-card {
  height: 100%;
}

.workflow-item {
  border-radius: 8px;
  transition: background-color 0.2s;
}

.workflow-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.recent-workflows {
  max-height: 300px;
  overflow-y: auto;
}
</style>
