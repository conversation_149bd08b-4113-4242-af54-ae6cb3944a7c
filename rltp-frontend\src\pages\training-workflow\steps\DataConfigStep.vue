<template>
  <div class="data-config-step">
    <div class="step-header q-mb-lg">
      <h5 class="q-my-none">数据配置</h5>
      <p class="text-grey-6">配置训练数据集和数据处理参数</p>
    </div>

    <q-form @submit.prevent="handleSaveAndNext" class="data-form">
      <!-- 数据集选择 -->
      <q-card flat bordered class="q-mb-md">
        <q-card-section>
          <div class="text-h6 q-mb-md">数据集选择</div>
          
          <div class="row q-gutter-md">
            <div class="col-5">
              <q-select
                v-model="formData.dataset_id"
                :options="datasetOptions"
                label="选择数据集"
                outlined
                option-label="label"
                option-value="value"
                :rules="[val => !!val || '请选择数据集']"
              />
            </div>
            <div class="col-6">
              <q-input
                v-model="formData.dataset_path"
                label="数据集路径"
                outlined
                hint="数据集在服务器上的路径"
                :rules="[val => !!val || '请输入数据集路径']"
              />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 数据分割配置 -->
      <q-card flat bordered class="q-mb-md">
        <q-card-section>
          <div class="text-h6 q-mb-md">数据分割配置</div>
          
          <div class="row q-gutter-md">
            <div class="col-3">
              <q-input
                v-model.number="formData.train_split"
                label="训练集比例"
                type="number"
                outlined
                min="0.1"
                max="0.9"
                step="0.1"
                :rules="[val => val > 0 && val < 1 || '请输入0-1之间的数值']"
              />
            </div>
            <div class="col-3">
              <q-input
                v-model.number="formData.val_split"
                label="验证集比例"
                type="number"
                outlined
                min="0.1"
                max="0.9"
                step="0.1"
                :rules="[val => val > 0 && val < 1 || '请输入0-1之间的数值']"
              />
            </div>
            <div class="col-3">
              <q-input
                v-model.number="formData.test_split"
                label="测试集比例"
                type="number"
                outlined
                min="0.1"
                max="0.9"
                step="0.1"
                :rules="[val => val > 0 && val < 1 || '请输入0-1之间的数值']"
              />
            </div>
          </div>

          <div class="q-mt-md">
            <q-banner v-if="splitSum !== 1" class="bg-orange-1 text-orange-8">
              <template v-slot:avatar>
                <q-icon name="warning" />
              </template>
              当前分割比例总和为 {{ splitSum.toFixed(1) }}，应该等于 1.0
            </q-banner>
          </div>
        </q-card-section>
      </q-card>

      <!-- 图像处理配置 -->
      <q-card flat bordered class="q-mb-md">
        <q-card-section>
          <div class="text-h6 q-mb-md">图像处理配置</div>
          
          <div class="row q-gutter-md">
            <div class="col-3">
              <q-input
                v-model.number="formData.image_size"
                label="图像尺寸"
                type="number"
                outlined
                min="224"
                max="1024"
                step="32"
                suffix="px"
              />
            </div>
            <div class="col-3">
              <q-input
                v-model.number="formData.batch_size"
                label="批次大小"
                type="number"
                outlined
                min="1"
                max="64"
              />
            </div>
            <div class="col-3">
              <q-toggle
                v-model="formData.data_augmentation"
                label="启用数据增强"
              />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 数据增强选项 -->
      <q-card flat bordered class="q-mb-md" v-if="formData.data_augmentation">
        <q-card-section>
          <div class="text-h6 q-mb-md">数据增强选项</div>
          
          <div class="row q-gutter-md">
            <div class="col-2">
              <q-toggle
                v-model="formData.augmentation_options.rotation"
                label="旋转"
              />
            </div>
            <div class="col-2">
              <q-toggle
                v-model="formData.augmentation_options.flip"
                label="翻转"
              />
            </div>
            <div class="col-2">
              <q-toggle
                v-model="formData.augmentation_options.scale"
                label="缩放"
              />
            </div>
            <div class="col-2">
              <q-toggle
                v-model="formData.augmentation_options.brightness"
                label="亮度调整"
              />
            </div>
            <div class="col-2">
              <q-toggle
                v-model="formData.augmentation_options.contrast"
                label="对比度调整"
              />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 数据验证 -->
      <q-card flat bordered class="q-mb-md">
        <q-card-section>
          <div class="text-h6 q-mb-md">数据验证</div>
          
          <div class="validation-section">
            <q-btn
              color="secondary"
              label="验证数据集"
              @click="validateDataset"
              :loading="validating"
              class="q-mr-md"
            />
            
            <div v-if="validationResult" class="q-mt-md">
              <q-banner 
                :class="validationResult.success ? 'bg-green-1 text-green-8' : 'bg-red-1 text-red-8'"
              >
                <template v-slot:avatar>
                  <q-icon :name="validationResult.success ? 'check_circle' : 'error'" />
                </template>
                {{ validationResult.message }}
              </q-banner>
              
              <div v-if="validationResult.details" class="q-mt-md">
                <div class="text-subtitle2">数据集详情：</div>
                <ul>
                  <li>训练样本数：{{ validationResult.details.train_samples }}</li>
                  <li>验证样本数：{{ validationResult.details.val_samples }}</li>
                  <li>测试样本数：{{ validationResult.details.test_samples }}</li>
                  <li>类别数量：{{ validationResult.details.num_classes }}</li>
                </ul>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 操作按钮 -->
      <div class="step-actions q-mt-lg">
        <q-btn
          color="primary"
          label="保存并下一步"
          type="submit"
          :loading="saving"
          :disable="!isFormValid"
        />
      </div>
    </q-form>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useQuasar } from 'quasar'
import { api } from 'boot/axios'

const props = defineProps({
  workflow: {
    type: Object,
    required: true
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['save', 'next'])

const $q = useQuasar()

// 响应式数据
const formData = ref({
  dataset_id: 1, // 设置默认值
  dataset_path: '/data/default_dataset', // 设置默认值
  train_split: 0.7,
  val_split: 0.2,
  test_split: 0.1,
  image_size: 640,
  batch_size: 16,
  data_augmentation: true,
  augmentation_options: {
    rotation: true,
    flip: true,
    scale: true,
    brightness: false,
    contrast: false
  }
})

const datasetOptions = ref([
  { label: '默认数据集', value: 1 },
  { label: 'COCO数据集', value: 2 },
  { label: '自定义数据集', value: 3 }
])
const saving = ref(false)
const validating = ref(false)
const validationResult = ref(null)

// 计算属性
const isFormValid = computed(() => {
  // 简化验证逻辑，只要有基本数据就可以保存
  return formData.value.dataset_id || formData.value.dataset_path || true
})

const splitSum = computed(() => {
  return formData.value.train_split + formData.value.val_split + formData.value.test_split
})

// 监听props变化
watch(() => props.data, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(formData.value, newData)
  }
}, { immediate: true })

// 方法
const validateDataset = async () => {
  validating.value = true
  try {
    // 模拟数据验证
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    validationResult.value = {
      success: true,
      message: '数据集验证通过',
      details: {
        train_samples: Math.floor(1000 * formData.value.train_split),
        val_samples: Math.floor(1000 * formData.value.val_split),
        test_samples: Math.floor(1000 * formData.value.test_split),
        num_classes: 10
      }
    }
  } catch (error) {
    validationResult.value = {
      success: false,
      message: '数据集验证失败'
    }
  } finally {
    validating.value = false
  }
}

const handleSaveAndNext = async () => {
  console.log('DataConfigStep: 点击保存并下一步')
  console.log('表单数据:', formData.value)
  console.log('表单是否有效:', isFormValid.value)
  
  if (!isFormValid.value) {
    console.log('表单验证失败')
    $q.notify({
      type: 'negative',
      message: '请完善必填信息'
    })
    return
  }

  saving.value = true
  try {
    console.log('发送save事件:', 'step1_data', formData.value)
    emit('save', 'step1_data', formData.value)
    
    console.log('发送next事件:', formData.value)
    emit('next', formData.value)
    
    $q.notify({
      type: 'positive',
      message: '步骤1数据已保存'
    })
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

// 生命周期
onMounted(() => {
  // 加载数据集选项
  loadDatasetOptions()
})

const loadDatasetOptions = async () => {
  try {
    // 这里可以从API加载真实的数据集选项
    // const response = await api.get('backend/datasets/')
    // datasetOptions.value = response.results
  } catch (error) {
    console.error('加载数据集选项失败:', error)
  }
}
</script>

<style scoped>
.data-config-step {
  padding: 24px;
}

.step-header {
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 16px;
}

.data-form .q-card {
  margin-bottom: 16px;
}

.validation-section {
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.step-actions {
  display: flex;
  justify-content: flex-end;
  padding-top: 24px;
  border-top: 1px solid #e0e0e0;
}
</style>
