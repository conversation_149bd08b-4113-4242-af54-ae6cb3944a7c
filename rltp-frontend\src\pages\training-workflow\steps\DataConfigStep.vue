<template>
  <div class="data-config-step">
    <div class="text-h6 q-mb-md">数据配置</div>
    <p class="text-grey-6 q-mb-lg">配置训练所需的数据集和数据处理参数</p>

    <q-form @submit.prevent="handleSave" class="q-gutter-md">
      <!-- 数据集选择 -->
      <q-card flat bordered>
        <q-card-section>
          <div class="text-subtitle1 q-mb-md">数据集配置</div>
          
          <div class="row q-gutter-md">
            <div class="col-12 col-md-6">
              <q-select
                v-model="formData.dataset_id"
                label="选择数据集"
                :options="datasetOptions"
                option-value="id"
                option-label="name"
                emit-value
                map-options
                clearable
                :rules="[val => !!val || '请选择数据集']"
              >
                <template v-slot:option="scope">
                  <q-item v-bind="scope.itemProps">
                    <q-item-section>
                      <q-item-label>{{ scope.opt.name }}</q-item-label>
                      <q-item-label caption>{{ scope.opt.description }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </template>
              </q-select>
            </div>
            
            <div class="col-12 col-md-6">
              <q-input
                v-model="formData.dataset_path"
                label="数据集路径"
                placeholder="/path/to/dataset"
                :rules="[val => !!val || '请输入数据集路径']"
              />
            </div>
          </div>

          <div class="row q-gutter-md q-mt-md">
            <div class="col-12 col-md-4">
              <q-input
                v-model.number="formData.train_split"
                label="训练集比例"
                type="number"
                min="0.1"
                max="0.9"
                step="0.1"
                suffix="%"
                :rules="[val => val >= 0.1 && val <= 0.9 || '请输入0.1-0.9之间的值']"
              />
            </div>
            
            <div class="col-12 col-md-4">
              <q-input
                v-model.number="formData.val_split"
                label="验证集比例"
                type="number"
                min="0.05"
                max="0.5"
                step="0.05"
                suffix="%"
                :rules="[val => val >= 0.05 && val <= 0.5 || '请输入0.05-0.5之间的值']"
              />
            </div>
            
            <div class="col-12 col-md-4">
              <q-input
                v-model.number="formData.test_split"
                label="测试集比例"
                type="number"
                min="0.05"
                max="0.5"
                step="0.05"
                suffix="%"
                readonly
                :model-value="(1 - formData.train_split - formData.val_split).toFixed(2)"
              />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 数据预处理 -->
      <q-card flat bordered>
        <q-card-section>
          <div class="text-subtitle1 q-mb-md">数据预处理</div>
          
          <div class="row q-gutter-md">
            <div class="col-12 col-md-6">
              <q-input
                v-model.number="formData.image_size"
                label="图像尺寸"
                type="number"
                min="224"
                max="1024"
                step="32"
                suffix="px"
                :rules="[val => val >= 224 && val <= 1024 || '请输入224-1024之间的值']"
              />
            </div>
            
            <div class="col-12 col-md-6">
              <q-input
                v-model.number="formData.batch_size"
                label="批次大小"
                type="number"
                min="1"
                max="128"
                :rules="[val => val >= 1 && val <= 128 || '请输入1-128之间的值']"
              />
            </div>
          </div>

          <div class="q-mt-md">
            <q-checkbox
              v-model="formData.data_augmentation"
              label="启用数据增强"
            />
          </div>

          <div v-if="formData.data_augmentation" class="q-mt-md">
            <div class="text-subtitle2 q-mb-sm">数据增强选项</div>
            <div class="row q-gutter-sm">
              <q-checkbox v-model="formData.augmentation_options.rotation" label="旋转" />
              <q-checkbox v-model="formData.augmentation_options.flip" label="翻转" />
              <q-checkbox v-model="formData.augmentation_options.scale" label="缩放" />
              <q-checkbox v-model="formData.augmentation_options.brightness" label="亮度调整" />
              <q-checkbox v-model="formData.augmentation_options.contrast" label="对比度调整" />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 数据验证 -->
      <q-card flat bordered>
        <q-card-section>
          <div class="text-subtitle1 q-mb-md">数据验证</div>
          
          <div class="row items-center q-gutter-md">
            <div class="col">
              <q-btn
                outline
                color="primary"
                label="验证数据集"
                icon="check_circle"
                @click="validateDataset"
                :loading="validating"
              />
            </div>
            
            <div class="col-auto" v-if="validationResult">
              <q-chip
                :color="validationResult.valid ? 'positive' : 'negative'"
                text-color="white"
                :icon="validationResult.valid ? 'check' : 'error'"
              >
                {{ validationResult.message }}
              </q-chip>
            </div>
          </div>

          <div v-if="validationResult && validationResult.details" class="q-mt-md">
            <q-list dense>
              <q-item v-for="detail in validationResult.details" :key="detail.key">
                <q-item-section>
                  <q-item-label>{{ detail.label }}</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-item-label>{{ detail.value }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </div>
        </q-card-section>
      </q-card>

      <!-- 操作按钮 -->
      <div class="row justify-end q-gutter-md q-mt-lg">
        <q-btn
          color="primary"
          label="保存配置"
          type="submit"
          :loading="saving"
        />
        
        <q-btn
          color="primary"
          label="保存并下一步"
          @click="handleSaveAndNext"
          :loading="saving"
          :disable="!isFormValid"
        />
      </div>
    </q-form>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useQuasar } from 'quasar'
import { api } from 'boot/axios'

const props = defineProps({
  workflow: {
    type: Object,
    required: true
  },
  stepData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['save', 'next'])

const $q = useQuasar()

// 响应式数据
const formData = ref({
  dataset_id: null,
  dataset_path: '',
  train_split: 0.7,
  val_split: 0.2,
  test_split: 0.1,
  image_size: 640,
  batch_size: 16,
  data_augmentation: true,
  augmentation_options: {
    rotation: true,
    flip: true,
    scale: true,
    brightness: false,
    contrast: false
  }
})

const datasetOptions = ref([])
const saving = ref(false)
const validating = ref(false)
const validationResult = ref(null)

// 计算属性
const isFormValid = computed(() => {
  return formData.value.dataset_id && 
         formData.value.dataset_path &&
         formData.value.train_split + formData.value.val_split < 1
})

// 方法
const loadDatasets = async () => {
  try {
    const response = await api.get('backend/datasets/')
    datasetOptions.value = response.results || response
  } catch (error) {
    console.error('加载数据集列表失败:', error)
  }
}

const validateDataset = async () => {
  if (!formData.value.dataset_path) {
    $q.notify({
      type: 'negative',
      message: '请先输入数据集路径'
    })
    return
  }

  validating.value = true
  try {
    const response = await api.post('backend/datasets/validate/', {
      path: formData.value.dataset_path,
      task_type: props.workflow.task_type
    })
    
    validationResult.value = response
    
    if (response.valid) {
      $q.notify({
        type: 'positive',
        message: '数据集验证通过'
      })
    } else {
      $q.notify({
        type: 'negative',
        message: '数据集验证失败'
      })
    }
  } catch (error) {
    console.error('数据集验证失败:', error)
    validationResult.value = {
      valid: false,
      message: '验证失败'
    }
  } finally {
    validating.value = false
  }
}

const handleSave = async () => {
  saving.value = true
  try {
    emit('save', 'step1_data', formData.value)
    $q.notify({
      type: 'positive',
      message: '数据配置已保存'
    })
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

const handleSaveAndNext = async () => {
  if (!isFormValid.value) {
    $q.notify({
      type: 'negative',
      message: '请完善必填信息'
    })
    return
  }

  saving.value = true
  try {
    emit('save', 'step1_data', formData.value)
    emit('next', formData.value)
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

// 监听测试集比例变化
watch([() => formData.value.train_split, () => formData.value.val_split], () => {
  formData.value.test_split = 1 - formData.value.train_split - formData.value.val_split
})

// 初始化数据
onMounted(() => {
  loadDatasets()
  
  // 如果有已保存的数据，加载它
  if (props.stepData && Object.keys(props.stepData).length > 0) {
    formData.value = { ...formData.value, ...props.stepData }
  }
})
</script>

<style scoped>
.data-config-step {
  max-width: 800px;
}
</style>
