#!/usr/bin/env python
import os
import sys
import django
import requests
import json

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'reinforce_platform.settings')
django.setup()

# 测试API
def test_training_task_api():
    base_url = 'http://127.0.0.1:8000/backend'
    
    # 测试创建训练任务
    print("测试创建训练任务...")
    create_data = {
        'name': '测试任务-YoloV8-2024-08-16',
        'task_type': 'object_detection_yolov8',
        'status': 'configuring',
        'workflow_id': 1,
        'current_step': 'step1_data',
        'progress': 20,
        'dataset_name': '目标识别数据集',
        'train_samples': 1000,
        'validation_samples': 200
    }
    
    try:
        response = requests.post(f'{base_url}/training/tasks/', json=create_data)
        print(f"创建任务响应状态: {response.status_code}")
        if response.status_code == 201:
            task_data = response.json()
            print(f"创建成功，任务ID: {task_data['id']}")
            task_id = task_data['id']
        else:
            print(f"创建失败: {response.text}")
            return
    except Exception as e:
        print(f"创建任务失败: {e}")
        return
    
    # 测试获取任务列表
    print("\n测试获取任务列表...")
    try:
        response = requests.get(f'{base_url}/training/tasks/')
        print(f"获取列表响应状态: {response.status_code}")
        if response.status_code == 200:
            tasks = response.json()
            print(f"获取成功，任务数量: {len(tasks)}")
            for task in tasks:
                print(f"- {task['name']} (状态: {task['status']}, 进度: {task['progress']}%)")
        else:
            print(f"获取失败: {response.text}")
    except Exception as e:
        print(f"获取任务列表失败: {e}")
    
    # 测试更新任务
    print(f"\n测试更新任务 {task_id}...")
    update_data = {
        'current_step': 'step2_model',
        'progress': 40,
        'status': 'training'
    }
    
    try:
        response = requests.patch(f'{base_url}/training/tasks/{task_id}/', json=update_data)
        print(f"更新任务响应状态: {response.status_code}")
        if response.status_code == 200:
            updated_task = response.json()
            print(f"更新成功，当前步骤: {updated_task['current_step']}, 进度: {updated_task['progress']}%")
        else:
            print(f"更新失败: {response.text}")
    except Exception as e:
        print(f"更新任务失败: {e}")

if __name__ == '__main__':
    test_training_task_api()
