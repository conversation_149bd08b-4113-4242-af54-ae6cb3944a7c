/*
 * @Author: Szc
 * @Date: 2025-08-08 14:30:27
 * @LastEditors: Szc
 * @LastEditTime: 2025-08-14 09:10:18
 * @Description: 👉深度学习👈
 */
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useModelFormStore = defineStore('modelForm', () => {
  // 第一步数据 - 数据集信息
  const stepOneData = ref({
    datasetName: '',
    trainPercent: 70,
    validationPercent: 30,
    trainSamples: '90个',
    validationSamples: '10个',
    rewardCount: '15个'
  })

  // 第二步数据 - 模型参数
  const stepTwoData = ref({
    params: {
      epochs: '5',
      batchSize: '5',
      optimizer: 'Adam',
      learningRate: '1e-5',
      warmupSteps: '1000',
      logInterval: '100',
      evalInterval: '10'
    },
    resources: {
      npuCount: '1',
      cpuCount: '16'
    }
  })

  // 更新第一步数据
  function updateStepOneData(data) {
    stepOneData.value = { ...stepOneData.value, ...data }
  }

  // 更新第二步数据
  function updateStepTwoData(data) {
    stepTwoData.value = { ...stepTwoData.value, ...data }
  }

  return {
    stepOneData,
    stepTwoData,
    updateStepOneData,
    updateStepTwoData
  }
}) 