/*
 * @Author: Szc
 * @Date: 2025-08-08 14:30:27
 * @LastEditors: Szc
 * @LastEditTime: 2025-08-14 09:10:18
 * @Description: 👉深度学习👈
 */
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useModelFormStore = defineStore('modelForm', () => {
  // 第一步数据 - 数据集信息
  const stepOneData = ref({
    datasetName: '',
    trainPercent: 70,
    validationPercent: 30,
    trainSamples: '90个',
    validationSamples: '10个',
    rewardCount: '15个'
  })

  // 第二步数据 - 模型参数
  const stepTwoData = ref({
    params: {
      epochs: '5',
      batchSize: '5',
      optimizer: 'Adam',
      learningRate: '1e-5',
      warmupSteps: '1000',
      logInterval: '100',
      evalInterval: '10'
    },
    resources: {
      npuCount: '1',
      cpuCount: '16'
    }
  })

  // 第三步数据 - 训练配置
  const stepThreeData = ref({
    epochs: 100,
    batchSize: 16,
    learningRate: 0.01
  })

  // 第四步数据 - 评估结果
  const stepFourData = ref({
    evaluationResults: [],
    modelMetrics: {},
    selectedModelId: '',
    comparedModels: []
  })

  // 第五步数据 - 部署配置
  const stepFiveData = ref({
    deploymentConfig: {
      modelName: '',
      deploymentType: 'api_service'
    },
    testResults: []
  })

  // 更新第一步数据
  function updateStepOneData(data) {
    stepOneData.value = { ...stepOneData.value, ...data }
  }

  // 更新第二步数据
  function updateStepTwoData(data) {
    stepTwoData.value = { ...stepTwoData.value, ...data }
  }

  // 更新第三步数据
  function updateStepThreeData(data) {
    stepThreeData.value = { ...stepThreeData.value, ...data }
  }

  // 更新第四步数据
  function updateStepFourData(data) {
    stepFourData.value = { ...stepFourData.value, ...data }
  }

  // 更新第五步数据
  function updateStepFiveData(data) {
    stepFiveData.value = { ...stepFiveData.value, ...data }
  }

  return {
    stepOneData,
    stepTwoData,
    stepThreeData,
    stepFourData,
    stepFiveData,
    updateStepOneData,
    updateStepTwoData,
    updateStepThreeData,
    updateStepFourData,
    updateStepFiveData
  }
}) 