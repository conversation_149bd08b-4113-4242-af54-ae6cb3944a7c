#!/usr/bin/env python3
"""
测试OM推理功能的脚本
"""
import os
import sys
import argparse

# 添加utils目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'utils'))

def test_om_inference():
    """测试OM推理功能"""
    print("🧪 测试OM推理功能")
    print("=" * 50)
    
    # 检查必要的文件是否存在
    script_path = os.path.join(os.path.dirname(__file__), 'utils', 'model_info_extractor_copy.py')
    if not os.path.exists(script_path):
        print(f"❌ 脚本文件不存在: {script_path}")
        return False
    
    print(f"✅ 脚本文件存在: {script_path}")
    
    # 测试导入
    try:
        from model_info_extractor_copy import infer_model_om, batch_infer_model_om, AIS_BENCH_AVAILABLE
        print("✅ 成功导入OM推理函数")
        
        if AIS_BENCH_AVAILABLE:
            print("✅ ais_bench可用，可以进行OM推理")
        else:
            print("⚠️  ais_bench不可用，OM推理将回退到PyTorch模式")
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    
    return True

def show_usage_examples():
    """显示使用示例"""
    print("\n📖 OM推理使用示例")
    print("=" * 50)
    
    print("\n1. 单张图片OM推理:")
    print("python utils/model_info_extractor_copy.py om_infer \\")
    print("    --om_model /path/to/model.om \\")
    print("    --pt_model /path/to/model.pt \\")
    print("    --image /path/to/image.jpg \\")
    print("    --save_path /workspace/result.jpg \\")
    print("    --conf 0.25 \\")
    print("    --device_id 0")
    
    print("\n2. 批量OM推理:")
    print("python utils/model_info_extractor_copy.py om_batch_infer \\")
    print("    --om_model /path/to/model.om \\")
    print("    --pt_model /path/to/model.pt \\")
    print("    --images '/path/to/img1.jpg,/path/to/img2.jpg' \\")
    print("    --output_dir /workspace/batch_results \\")
    print("    --conf 0.25 \\")
    print("    --device_id 0")
    
    print("\n3. 模型转换（PT -> ONNX -> OM）:")
    print("python utils/model_info_extractor_copy.py convert \\")
    print("    --pt /path/to/model.pt \\")
    print("    --chip_name 910B3 \\")
    print("    --mount_path /root/siton-data-xxx")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="测试OM推理功能")
    parser.add_argument('--show_examples', action='store_true', help='显示使用示例')
    
    args = parser.parse_args()
    
    print("🚀 OM推理功能测试工具")
    print("=" * 60)
    
    # 运行测试
    success = test_om_inference()
    
    if args.show_examples or success:
        show_usage_examples()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 测试完成 - OM推理功能可用")
    else:
        print("❌ 测试失败 - 请检查环境配置")
    
    print("\n💡 使用建议:")
    print("1. 确保在华为昇腾NPU环境中运行")
    print("2. 安装ais_bench: pip install ais_bench")
    print("3. 准备好.om和.pt模型文件")
    print("4. 使用--show_examples查看详细用法")

if __name__ == "__main__":
    main()
