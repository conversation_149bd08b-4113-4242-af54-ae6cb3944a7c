#!/usr/bin/env python3
import argparse
import os
import sys
import argparse
import subprocess
import base64
import tempfile
from pathlib import Path
import cv2
import numpy as np
import torch
from tqdm import tqdm
import json
import shutil

# 尝试导入ais_bench，如果失败则提供替代方案
try:
    from ais_bench.infer.interface import InferSession
    AIS_BENCH_AVAILABLE = True
except ImportError:
    print("⚠️  ais_bench未安装，OM推理功能将不可用")
    print("💡 如需使用OM推理，请在华为昇腾环境中安装: pip install ais_bench")
    AIS_BENCH_AVAILABLE = False

    # 提供一个占位符类
    class InferSession:
        def __init__(self, *args, **kwargs):
            raise ImportError("ais_bench not available. Please install ais_bench for OM inference.")

        def infer(self, *args, **kwargs):
            raise ImportError("ais_bench not available. Please install ais_bench for OM inference.")


def load_image_for_om(img_path, img_size=640):
    """
    加载并预处理单张图片用于OM推理
    Args:
        img_path: 图片路径
        img_size: 目标图片尺寸
    Returns:
        tuple: (preprocessed_img, original_img, ratio, pad)
    """
    # 读取图片
    img0 = cv2.imread(img_path)
    if img0 is None:
        raise ValueError(f"无法读取图片: {img_path}")

    # 计算缩放比例
    h0, w0 = img0.shape[:2]  # 原始高度和宽度
    r = img_size / max(h0, w0)  # 缩放比例
    if r != 1:  # 如果需要缩放
        img0 = cv2.resize(img0, (int(w0 * r), int(h0 * r)), interpolation=cv2.INTER_LINEAR)

    # 填充到正方形
    h, w = img0.shape[:2]
    top = (img_size - h) // 2
    bottom = img_size - h - top
    left = (img_size - w) // 2
    right = img_size - w - left

    img = cv2.copyMakeBorder(img0, top, bottom, left, right, cv2.BORDER_CONSTANT, value=(114, 114, 114))

    # 转换为RGB并归一化
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    img = img.transpose(2, 0, 1)  # HWC to CHW
    img = np.ascontiguousarray(img, dtype=np.float32)
    img /= 255.0  # 归一化到0-1

    # 添加batch维度
    img = np.expand_dims(img, axis=0)

    ratio = r
    pad = (left, top)

    return img, img0, ratio, pad


def scale_coords_om(img1_shape, coords, img0_shape, ratio_pad=None):
    """
    将坐标从img1_shape缩放到img0_shape
    """
    if ratio_pad is None:  # calculate from img0_shape
        gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])  # gain  = old / new
        pad = (img1_shape[1] - img0_shape[1] * gain) / 2, (img1_shape[0] - img0_shape[0] * gain) / 2  # wh padding
    else:
        gain = ratio_pad[0]
        pad = ratio_pad[1]

    coords[:, [0, 2]] -= pad[0]  # x padding
    coords[:, [1, 3]] -= pad[1]  # y padding
    coords[:, :4] /= gain
    coords[:, 0] = torch.clamp(coords[:, 0], 0, img0_shape[1])  # x1
    coords[:, 1] = torch.clamp(coords[:, 1], 0, img0_shape[0])  # y1
    coords[:, 2] = torch.clamp(coords[:, 2], 0, img0_shape[1])  # x2
    coords[:, 3] = torch.clamp(coords[:, 3], 0, img0_shape[0])  # y2
    return coords


def non_max_suppression_om(prediction, conf_thres=0.25, iou_thres=0.45, max_det=300):
    """
    OM推理的非极大值抑制
    """
    # 确保输入是torch tensor
    if isinstance(prediction, np.ndarray):
        prediction = torch.from_numpy(prediction)

    # 如果是4维，去掉batch维度
    if prediction.dim() == 4:
        prediction = prediction.squeeze(0)

    # 置信度过滤
    conf_mask = prediction[:, 4] > conf_thres
    prediction = prediction[conf_mask]

    if not prediction.size(0):
        return [torch.empty((0, 6))]

    # 计算类别置信度
    prediction[:, 5:] *= prediction[:, 4:5]  # conf = obj_conf * cls_conf

    # Box (center x, center y, width, height) to (x1, y1, x2, y2)
    box = prediction[:, :4].clone()
    box[:, 0] = prediction[:, 0] - prediction[:, 2] / 2  # x1
    box[:, 1] = prediction[:, 1] - prediction[:, 3] / 2  # y1
    box[:, 2] = prediction[:, 0] + prediction[:, 2] / 2  # x2
    box[:, 3] = prediction[:, 1] + prediction[:, 3] / 2  # y2
    prediction[:, :4] = box

    # 获取最大类别置信度和索引
    conf, j = prediction[:, 5:].max(1, keepdim=True)
    prediction = torch.cat((prediction[:, :4], conf, j.float()), 1)[conf.view(-1) > conf_thres]

    # 按置信度排序
    prediction = prediction[prediction[:, 4].argsort(descending=True)]

    # NMS
    keep = torch.ops.torchvision.nms(prediction[:, :4], prediction[:, 4], iou_thres)
    if keep.shape[0] > max_det:
        keep = keep[:max_det]

    return [prediction[keep]]


# # 添加当前目录到Python路径
# ultralytics_dir = '/root/siton-data-c16a16a2cdc44452a1c4267121b485aa/data/ultralytics_v8'
# sys.path.insert(0, str(ultralytics_dir))
# # 设置环境变量
# os.environ["PYTHONPATH"] = str(ultralytics_dir)


def convert_model(mount_path, model_path, chip_name="910B3"):
    """
    将模型转换为ONNX格式，然后转换为OM格式
    
    Args:
        mount_path: 数据挂载路径
        model_path: 模型文件路径
        chip_name: 芯片型号
    
    Returns:
        tuple: (onnx_path, om_path) 转换后的ONNX和OM模型路径
    """
    from ultralytics import YOLO
    
    # 获取模型名称（不带扩展名）
    model_name = os.path.splitext(os.path.basename(model_path))[0]
    
    print(f"开始转换模型: {model_path}")
    
    # 加载模型并导出为ONNX格式
    model = YOLO(model_path)
    onnx_model = model.export(format="onnx", dynamic=True, simplify=True, opset=11)
    print(f"ONNX模型已导出: {onnx_model}")
    
    # 安装必要的pip包
    data_dir = os.path.join(mount_path, 'data') if mount_path else os.path.join('/root/siton-data-b496463103254f46976c4ff88ea74bc9', 'data')
    pip_packages = [
        os.path.join(data_dir, "aclruntime-0.0.2-cp39-cp39-linux_aarch64.whl"),
        os.path.join(data_dir, "ais_bench-0.0.2-py3-none-any.whl")
    ]
    
    for package in pip_packages:
        try:
            print(f"正在安装: {package}")
            result = subprocess.run(f"pip install {package}", shell=True, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            print(f"安装成功: {package}")
        except subprocess.CalledProcessError as e:
            print(f"安装失败: {package}")
            print(f"错误信息: {e.stderr}")
            return None, None  # 如果安装失败，返回None
    
    # 设置参数
    batchsize = 1
    
    # 构建输出OM模型路径
    om_model = f"{model_name}_bs{batchsize}.om"
    
    # 获取onnx_model的上一层目录
    parent_dir = os.path.dirname(os.path.dirname(onnx_model))
    output_name = f"{os.path.join(parent_dir, model_name)}_bs{batchsize}"
    
    # 构建atc命令
    atc_cmd = f"atc --framework=5 --model={onnx_model} --input_format=NCHW --input_shape=\"images:{batchsize},3,640,640\" --output_type=FP16 --output={output_name} --log=error --soc_version=Ascend{chip_name}"
    
    print(f"执行ATC命令: {atc_cmd}")
    
    # 设置环境变量
    env_setup = 'source ~/.bashrc && source /usr/local/Ascend/ascend-toolkit/set_env.sh && source /usr/local/Ascend/ascend-toolkit/8.0.RC2.2/aarch64-linux/script/set_env.sh && export LD_LIBRARY_PATH=/usr/local/Ascend/driver/lib64/driver/:/usr/local/python3.9.2/lib/:$LD_LIBRARY_PATH'
    
    # 执行atc命令
    try:
        # 使用bash -c来确保在正确的shell环境中执行，并设置环境变量
        bash_cmd = f"bash -c '{env_setup} && {atc_cmd}'"
        print(f"完整命令: {bash_cmd}")
        result = subprocess.run(bash_cmd, shell=True, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # 更新om_model为完整路径
        om_model_path = f"{output_name}.om"
        print(f"ATC转换成功: {om_model_path}")
        print(result.stdout)
        return onnx_model, om_model_path
    except subprocess.CalledProcessError as e:
        print(f"ATC转换失败: {e}")
        print(f"错误信息: {e.stderr}")
        return onnx_model, None


def process_input_source(input_source):
    """
    处理输入源，支持文件路径和base64数据

    Args:
        input_source: 输入源，可以是文件路径或base64编码的图片数据

    Returns:
        tuple: (processed_path, is_temp_file) 处理后的文件路径和是否为临时文件的标志
    """
    # 检查是否为base64数据
    if input_source.startswith('data:image/') or (len(input_source) > 100 and not os.path.exists(input_source)):
        try:
            # 处理base64数据
            if input_source.startswith('data:image/'):
                # 移除data:image/...;base64,前缀
                header, encoded_data = input_source.split(',', 1)
                # 从header中提取文件格式
                format_info = header.split(';')[0].split('/')[-1]
                if format_info.lower() in ['jpeg', 'jpg']:
                    ext = '.jpg'
                elif format_info.lower() == 'png':
                    ext = '.png'
                else:
                    ext = '.jpg'  # 默认
            else:
                # 纯base64数据，默认为jpg
                encoded_data = input_source
                ext = '.jpg'

            # 解码base64数据
            image_data = base64.b64decode(encoded_data)

            # 创建临时文件
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=ext)
            temp_file.write(image_data)
            temp_file.close()

            print(f"Base64数据已转换为临时文件: {temp_file.name}")
            return temp_file.name, True

        except Exception as e:
            print(f"处理base64数据失败: {e}")
            raise ValueError(f"无效的base64图片数据: {e}")
    else:
        # 普通文件路径
        if not os.path.exists(input_source):
            raise FileNotFoundError(f"图片文件不存在: {input_source}")
        return input_source, False


def infer_model_om(om_model_path, pt_model_path, image_path, save_path="/workspace/", conf_thres=0.25, device_id=0):
    """
    使用OM模型进行单张图片推理

    Args:
        om_model_path: OM模型文件路径
        pt_model_path: PT模型文件路径（用于获取类别名称）
        image_path: 输入图片路径或base64数据
        save_path: 结果保存路径，默认为workspace
        conf_thres: 置信度阈值
        device_id: NPU设备ID

    Returns:
        str: 结果图片保存路径
    """
    # 检查ais_bench可用性
    if not AIS_BENCH_AVAILABLE:
        print("❌ ais_bench不可用，无法进行OM推理")
        print("💡 请在华为昇腾环境中安装: pip install ais_bench")
        print("🔄 回退到使用PyTorch模型进行推理")
        return infer_model(pt_model_path, image_path, save_path, conf_thres)

    # 加载PT模型获取类别名称
    from ultralytics import YOLO
    pt_model = YOLO(pt_model_path)
    names = pt_model.names

    # 初始化OM模型
    try:
        om_model = InferSession(int(device_id), om_model_path)
        print(f"✅ 成功加载OM模型: {om_model_path}")
    except Exception as e:
        print(f"❌ OM模型加载失败: {e}")
        print("💡 请检查模型路径和NPU环境")
        print("🔄 回退到使用PyTorch模型进行推理")
        return infer_model(pt_model_path, image_path, save_path, conf_thres)

    # 处理输入源
    processed_image_path, is_temp_file = process_input_source(image_path)

    try:
        print(f"使用OM模型对图片进行推理: {processed_image_path}")

        # 预处理图片
        img, img0, ratio, pad = load_image_for_om(processed_image_path, img_size=640)

        # OM模型推理
        preds = om_model.infer([img])

        # 后处理
        if isinstance(preds, list):
            preds = preds[0]
        preds = torch.from_numpy(preds) if isinstance(preds, np.ndarray) else preds

        # NMS
        preds = non_max_suppression_om(
            prediction=preds,
            conf_thres=conf_thres,
            iou_thres=0.45,
            max_det=300
        )

        # 处理检测结果
        result_img_path = processed_image_path
        detection_results = []

        for det in preds:  # 每张图片的检测结果
            if len(det):
                # 将坐标缩放回原始图片尺寸
                det[:, :4] = scale_coords_om(img.shape[2:], det[:, :4], img0.shape, ratio_pad=(ratio, pad))

                # 在图片上绘制检测框
                img_with_boxes = img0.copy()
                for *xyxy, conf, cls in det:
                    x1, y1, x2, y2 = map(int, xyxy)
                    class_name = names.get(int(cls), str(int(cls)))
                    confidence = float(conf)

                    # 绘制边界框
                    cv2.rectangle(img_with_boxes, (x1, y1), (x2, y2), (0, 255, 0), 2)

                    # 绘制标签
                    label = f"{class_name}: {confidence:.2f}"
                    label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
                    cv2.rectangle(img_with_boxes, (x1, y1 - label_size[1] - 10),
                                (x1 + label_size[0], y1), (0, 255, 0), -1)
                    cv2.putText(img_with_boxes, label, (x1, y1 - 5),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 2)

                    # 保存检测结果
                    detection_results.append({
                        'bbox': [float(x) for x in xyxy],
                        'confidence': confidence,
                        'class': int(cls),
                        'class_name': class_name
                    })

                # 保存结果图片
                if save_path:
                    os.makedirs(os.path.dirname(save_path), exist_ok=True)
                    cv2.imwrite(save_path, img_with_boxes)
                    result_img_path = save_path
                else:
                    # 保存到临时位置
                    result_dir = "/workspace/om_results"
                    os.makedirs(result_dir, exist_ok=True)
                    result_filename = f"om_result_{os.path.basename(processed_image_path)}"
                    result_img_path = os.path.join(result_dir, result_filename)
                    cv2.imwrite(result_img_path, img_with_boxes)

        print(f"OM推理完成，结果保存在: {result_img_path}")
        print(f"检测到 {len(detection_results)} 个目标")

        # 打印检测结果
        for i, result in enumerate(detection_results):
            print(f"目标 {i+1}: 类别={result['class_name']}, 置信度={result['confidence']:.2f}, 坐标={result['bbox']}")

        return result_img_path

    finally:
        # 清理临时文件
        if is_temp_file and os.path.exists(processed_image_path):
            try:
                os.unlink(processed_image_path)
                print(f"已清理临时文件: {processed_image_path}")
            except Exception as e:
                print(f"清理临时文件失败: {e}")


def infer_model(model_path, image_path, save_path="/workspace/", conf_thres=0.25):
    """
    使用YOLO模型进行单张图片推理

    Args:
        model_path: 模型文件路径
        image_path: 输入图片路径或base64数据
        save_path: 结果保存路径，默认为workspace
        conf_thres: 置信度阈值

    Returns:
        str: 结果图片保存路径
    """
    from ultralytics import YOLO

    # 暂时写死，使用最佳模型
    model_path = os.path.join('/root/siton-data-b496463103254f46976c4ff88ea74bc9', 'data', 'best.pt')
    print(f"加载模型: '{model_path}'")
    model = YOLO(model_path)

    # 处理输入源
    processed_image_path, is_temp_file = process_input_source(image_path)

    try:
        print(f"对图片进行推理: {processed_image_path}")
        results = model.predict(
            source=processed_image_path,
            conf=conf_thres,
            save=True,
            save_txt=True,
            save_conf=True,
            project="results",
            imgsz=1024,
            name=os.path.splitext(os.path.basename(processed_image_path))[0]
        )
    
        # 获取结果图片路径
        result_path = results[0].save_dir
        result_img = os.path.join(result_path, os.path.basename(processed_image_path))

        # 如果指定了保存路径，则复制结果
        if save_path:
            import shutil
            shutil.copy(result_img, save_path)
            result_img = save_path

        print(f"推理完成，结果保存在: {result_img}")

        # 打印检测结果
        for r in results:
            boxes = r.boxes
            print(f"检测到 {len(boxes)} 个目标")
            for i, box in enumerate(boxes):
                cls = int(box.cls[0])
                conf = float(box.conf[0])
                name = model.names[cls]
                print(f"目标 {i+1}: 类别={name}, 置信度={conf:.2f}, 坐标={box.xyxy[0].tolist()}")

        return result_img

    finally:
        # 清理临时文件
        if is_temp_file and os.path.exists(processed_image_path):
            try:
                os.unlink(processed_image_path)
                print(f"已清理临时文件: {processed_image_path}")
            except Exception as e:
                print(f"清理临时文件失败: {e}")


def batch_infer_model_om(om_model_path, pt_model_path, image_paths_str, conf_thres=0.25, output_dir="/workspace/batch_results/", device_id=0):
    """
    使用OM模型进行批量推理

    Args:
        om_model_path: OM模型文件路径
        pt_model_path: PT模型文件路径（用于获取类别名称）
        image_paths_str: 输入图片路径列表（以逗号分隔的字符串）
        conf_thres: 置信度阈值
        output_dir: 结果保存目录，默认为workspace/batch_results/
        device_id: NPU设备ID

    Returns:
        list: 结果图片保存路径列表
    """
    # 检查ais_bench可用性
    if not AIS_BENCH_AVAILABLE:
        print("❌ ais_bench不可用，无法进行OM批量推理")
        print("💡 请在华为昇腾环境中安装: pip install ais_bench")
        print("🔄 回退到使用PyTorch模型进行批量推理")
        return batch_infer_model(pt_model_path, image_paths_str, conf_thres, output_dir)

    # 加载PT模型获取类别名称
    from ultralytics import YOLO
    pt_model = YOLO(pt_model_path)
    names = pt_model.names

    # 初始化OM模型
    try:
        om_model = InferSession(int(device_id), om_model_path)
        print(f"✅ 成功加载OM模型: {om_model_path}")
    except Exception as e:
        print(f"❌ OM模型加载失败: {e}")
        print("💡 请检查模型路径和NPU环境")
        print("🔄 回退到使用PyTorch模型进行批量推理")
        return batch_infer_model(pt_model_path, image_paths_str, conf_thres, output_dir)

    # 解析图片路径列表
    image_paths = [path.strip() for path in image_paths_str.split(',') if path.strip()]

    if not image_paths:
        print("错误: 没有提供有效的图片路径")
        return []

    print(f"开始使用OM模型批量推理 {len(image_paths)} 张图片")

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    results_paths = []
    temp_files_to_cleanup = []

    for i, image_path in enumerate(image_paths):
        print(f"\n=== 使用OM模型处理图片 {i+1}/{len(image_paths)}: {image_path[:50]}{'...' if len(image_path) > 50 else ''} ===")

        try:
            # 处理输入源
            processed_image_path, is_temp_file = process_input_source(image_path)

            if is_temp_file:
                temp_files_to_cleanup.append(processed_image_path)

            print(f"使用OM模型对图片进行推理: {processed_image_path}")

            # 为每张图片创建独立的输出目录
            image_output_dir = os.path.join(output_dir, f"image_{i+1}")
            os.makedirs(image_output_dir, exist_ok=True)

            # 预处理图片
            img, img0, ratio, pad = load_image_for_om(processed_image_path, img_size=640)

            # OM模型推理
            preds = om_model.infer([img])

            # 后处理
            if isinstance(preds, list):
                preds = preds[0]
            preds = torch.from_numpy(preds) if isinstance(preds, np.ndarray) else preds

            # NMS
            preds = non_max_suppression_om(
                prediction=preds,
                conf_thres=conf_thres,
                iou_thres=0.45,
                max_det=300
            )

            # 处理检测结果
            detection_results = []
            img_with_boxes = img0.copy()

            for det in preds:  # 每张图片的检测结果
                if len(det):
                    # 将坐标缩放回原始图片尺寸
                    det[:, :4] = scale_coords_om(img.shape[2:], det[:, :4], img0.shape, ratio_pad=(ratio, pad))

                    # 在图片上绘制检测框
                    for *xyxy, conf, cls in det:
                        x1, y1, x2, y2 = map(int, xyxy)
                        class_name = names.get(int(cls), str(int(cls)))
                        confidence = float(conf)

                        # 绘制边界框
                        cv2.rectangle(img_with_boxes, (x1, y1), (x2, y2), (0, 255, 0), 2)

                        # 绘制标签
                        label = f"{class_name}: {confidence:.2f}"
                        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
                        cv2.rectangle(img_with_boxes, (x1, y1 - label_size[1] - 10),
                                    (x1 + label_size[0], y1), (0, 255, 0), -1)
                        cv2.putText(img_with_boxes, label, (x1, y1 - 5),
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 2)

                        # 保存检测结果
                        detection_results.append({
                            'bbox': [float(x) for x in xyxy],
                            'confidence': confidence,
                            'class': int(cls),
                            'class_name': class_name
                        })

            # 保存结果图片
            final_result_path = os.path.join(image_output_dir, f"om_result_{i+1}.jpg")
            cv2.imwrite(final_result_path, img_with_boxes)

            print(f"OM推理完成，结果保存在: {final_result_path}")
            print(f"BATCH_INFERENCE_RESULT_PATH:{final_result_path}")
            results_paths.append(final_result_path)

            # 保存检测结果到JSON
            json_path = os.path.join(image_output_dir, f"om_result_{i+1}.json")
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(detection_results, f, ensure_ascii=False, indent=2)

            # 打印检测结果
            print(f"检测到 {len(detection_results)} 个目标")
            for j, result in enumerate(detection_results):
                print(f"目标 {j+1}: 类别={result['class_name']}, 置信度={result['confidence']:.2f}, 坐标={result['bbox']}")

        except Exception as e:
            print(f"处理图片 {i+1} 时发生错误: {e}")
            results_paths.append(None)

    # 清理临时文件
    for temp_file in temp_files_to_cleanup:
        try:
            if os.path.exists(temp_file):
                os.unlink(temp_file)
                print(f"已清理临时文件: {temp_file}")
        except Exception as e:
            print(f"清理临时文件失败: {e}")

    print(f"\nOM批量推理完成，成功处理 {len([p for p in results_paths if p])} / {len(image_paths)} 张图片")
    return results_paths


def batch_infer_model(model_path, image_paths_str, conf_thres=0.25, output_dir="/workspace/batch_results/"):
    """
    使用YOLO模型进行批量推理

    Args:
        model_path: 模型文件路径
        image_paths_str: 输入图片路径列表（以逗号分隔的字符串）
        output_dir: 结果保存目录，默认为workspace/batch_results/
        conf_thres: 置信度阈值

    Returns:
        list: 结果图片保存路径列表
    """
    from ultralytics import YOLO
    import os
    import shutil

    # 暂时写死，使用最佳模型
    model_path = os.path.join('/root/siton-data-b496463103254f46976c4ff88ea74bc9', 'data', 'best.pt')
    print(f"加载模型: '{model_path}'")
    model = YOLO(model_path)

    # 解析图片路径列表
    image_paths = [path.strip() for path in image_paths_str.split(',') if path.strip()]

    if not image_paths:
        print("错误: 没有提供有效的图片路径")
        return []

    print(f"开始批量推理 {len(image_paths)} 张图片")

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    results_paths = [] 
    temp_files_to_cleanup = []

    for i, image_path in enumerate(image_paths):
        print(f"\n=== 处理图片 {i+1}/{len(image_paths)}: {image_path[:50]}{'...' if len(image_path) > 50 else ''} ===")

        try:
            # 处理输入源
            processed_image_path, is_temp_file = process_input_source(image_path)

            if is_temp_file:
                temp_files_to_cleanup.append(processed_image_path)

            print(f"对图片进行推理: {processed_image_path}")

            # 为每张图片创建独立的输出目录
            image_output_dir = os.path.join(output_dir, f"image_{i+1}")
            os.makedirs(image_output_dir, exist_ok=True)

            results = model.predict(
                source=processed_image_path,
                conf=conf_thres,
                save=True,
                save_txt=True,
                save_conf=True,
                project=image_output_dir,
                imgsz=1024,
                name="result"
            )

            # 获取结果图片路径
            result_path = results[0].save_dir
            result_img = os.path.join(result_path, os.path.basename(processed_image_path))

            # 复制结果到输出目录
            final_result_path = os.path.join(image_output_dir, f"result_{i+1}.jpg")
            if os.path.exists(result_img):
                shutil.copy(result_img, final_result_path)
                print(f"推理完成，结果保存在: {final_result_path}")
                print(f"BATCH_INFERENCE_RESULT_PATH:{final_result_path}")
                results_paths.append(final_result_path)
            else:
                print(f"警告: 结果图片不存在: {result_img}")
                results_paths.append(None)

            # 打印检测结果
            for r in results:
                boxes = r.boxes
                print(f"检测到 {len(boxes)} 个目标")
                for j, box in enumerate(boxes):
                    cls = int(box.cls[0])
                    conf = float(box.conf[0])
                    name = model.names[cls]
                    print(f"目标 {j+1}: 类别={name}, 置信度={conf:.2f}, 坐标={box.xyxy[0].tolist()}")

        except Exception as e:
            print(f"处理图片 {i+1} 时发生错误: {e}")
            results_paths.append(None)

    # 清理临时文件
    for temp_file in temp_files_to_cleanup:
        try:
            if os.path.exists(temp_file):
                os.unlink(temp_file)
                print(f"已清理临时文件: {temp_file}")
        except Exception as e:
            print(f"清理临时文件失败: {e}")

    print(f"\n批量推理完成，成功处理 {len([p for p in results_paths if p])} / {len(image_paths)} 张图片")
    return results_paths


def main():
    parser = argparse.ArgumentParser(description="YOLOv8模型转换与推理工具")
    subparsers = parser.add_subparsers(dest="command", help="选择命令")
    
    # 转换模型命令
    convert_parser = subparsers.add_parser("convert", help="转换模型格式")
    convert_parser.add_argument('--pt', required=True, help='输入模型路径(.pt文件)')
    convert_parser.add_argument('--chip_name', default="910B3", help='芯片型号')
    convert_parser.add_argument('--mount_path', help='数据挂载路径')
    convert_parser.add_argument('--ultralytics_mount_path', help='ultralytics源码路径')
    
    # 推理命令
    infer_parser = subparsers.add_parser("infer", help="使用模型进行单张图片推理")
    infer_parser.add_argument('--model', required=True, help='模型路径(.pt, .onnx或其他支持的格式)')
    infer_parser.add_argument('--image', required=True, help='输入图片路径')
    infer_parser.add_argument('--save_path', help='结果保存路径')
    # 添加数据和代码路径参数
    infer_parser.add_argument('--mount_path', help='数据挂载路径')
    infer_parser.add_argument('--ultralytics_mount_path', help='ultralytics源码路径')
    infer_parser.add_argument('--conf', type=float, default=0.25, help='置信度阈值')

    # OM推理命令
    om_infer_parser = subparsers.add_parser("om_infer", help="使用OM模型进行单张图片推理")
    om_infer_parser.add_argument('--om_model', required=True, help='OM模型路径(.om文件)')
    om_infer_parser.add_argument('--pt_model', required=True, help='PT模型路径(.pt文件，用于获取类别名称)')
    om_infer_parser.add_argument('--image', required=True, help='输入图片路径')
    om_infer_parser.add_argument('--save_path', help='结果保存路径')
    om_infer_parser.add_argument('--device_id', type=int, default=0, help='NPU设备ID')
    om_infer_parser.add_argument('--mount_path', help='数据挂载路径')
    om_infer_parser.add_argument('--ultralytics_mount_path', help='ultralytics源码路径')
    om_infer_parser.add_argument('--conf', type=float, default=0.25, help='置信度阈值')

    # 批量推理命令
    batch_infer_parser = subparsers.add_parser("batch_infer", help="使用模型进行批量推理")
    batch_infer_parser.add_argument('--model', required=True, help='模型路径(.pt, .onnx或其他支持的格式)')
    batch_infer_parser.add_argument('--images', required=True, help='输入图片路径列表（以逗号分隔）')
    batch_infer_parser.add_argument('--output_dir', help='结果保存目录')
    # 添加数据和代码路径参数
    batch_infer_parser.add_argument('--mount_path', help='数据挂载路径')
    batch_infer_parser.add_argument('--ultralytics_mount_path', help='ultralytics源码路径')
    batch_infer_parser.add_argument('--conf', type=float, default=0.25, help='置信度阈值')

    # OM批量推理命令
    om_batch_infer_parser = subparsers.add_parser("om_batch_infer", help="使用OM模型进行批量推理")
    om_batch_infer_parser.add_argument('--om_model', required=True, help='OM模型路径(.om文件)')
    om_batch_infer_parser.add_argument('--pt_model', required=True, help='PT模型路径(.pt文件，用于获取类别名称)')
    om_batch_infer_parser.add_argument('--images', required=True, help='输入图片路径列表（以逗号分隔）')
    om_batch_infer_parser.add_argument('--output_dir', help='结果保存目录')
    om_batch_infer_parser.add_argument('--device_id', type=int, default=0, help='NPU设备ID')
    om_batch_infer_parser.add_argument('--mount_path', help='数据挂载路径')
    om_batch_infer_parser.add_argument('--ultralytics_mount_path', help='ultralytics源码路径')
    om_batch_infer_parser.add_argument('--conf', type=float, default=0.25, help='置信度阈值')

    args = parser.parse_args()


    # 如果指定了ultralytics_dir，则更新Python路径
    if args.ultralytics_mount_path:
        sys.path.insert(0, str(args.ultralytics_mount_path))
        os.environ["PYTHONPATH"] = str(args.ultralytics_mount_path)
    
    # 如果指定了mount_path，则更新模型路径
    # if args.mount_path:
    #     args.model = os.path.join(args.mount_path, 'data', os.path.basename(args.model))
    
    if args.command == "convert":
        convert_model(args.mount_path, args.pt, args.chip_name)
    elif args.command == "infer":
        infer_model(args.model, args.image, args.save_path, args.conf)
    elif args.command == "om_infer":
        infer_model_om(args.om_model, args.pt_model, args.image, args.save_path, args.conf, args.device_id)
    elif args.command == "batch_infer":
        if args.output_dir:
            batch_infer_model(args.model, args.images, args.conf, args.output_dir)
        else:
            batch_infer_model(args.model, args.images, args.conf)
    elif args.command == "om_batch_infer":
        if args.output_dir:
            batch_infer_model_om(args.om_model, args.pt_model, args.images, args.conf, args.output_dir, args.device_id)
        else:
            batch_infer_model_om(args.om_model, args.pt_model, args.images, args.conf, device_id=args.device_id)
    else:
        parser.print_help()


if __name__ == '__main__':
    main()