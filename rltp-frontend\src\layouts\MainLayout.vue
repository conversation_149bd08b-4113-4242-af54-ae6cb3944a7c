<template>
  <div class="q-mx-auto container">

    <div class="full-height layout">
      <!-- <header style="overflow: hidden;">
        <div class="flex itemes-left header-c">
          <div class="btnContainer">
            <template v-for="(item, index) in menuItems" :key="item.id">

              <q-btn v-if="item.type === 'btn'" class="styleBtn" :class="item.active ? 'active' : ''"
                :label="item.label" @click="handleBtnClick(item)" />

          
              <q-btn v-else class="styleBtn" :class="item.active ? 'active' : ''" :label="item.label">
                <q-menu fit class="myMenu" transition-show="flip-left" transition-hide="flip-right">
                  <q-list class="downList">
                    <q-item v-for="subItem in item.submenu" :key="subItem.value" clickable v-close-popup
                      @click="handleSubmenuClick(item, subItem.value)">
                      <q-item-section class="font14">{{ subItem.label }}</q-item-section>
                    </q-item>
                  </q-list>
                </q-menu>
              </q-btn>
              <ArrowAnimate v-if="index != menuItems.length - 1" class="arrowAnimate"></ArrowAnimate>
            </template>
</div>
</div>

</header> -->

      <main>
        <router-view />
      </main>
    </div>

    <div class="titleBG no-select">
      <img class="imgC no-select" src="../assets/images/header.png" alt="">
      <div class="titleCenter labelColor no-select">
        智能试训环境
      </div>
    </div>

    <div class="rightTop">
      <UserCenter></UserCenter>
    </div>

    <HCII class="HCII"></HCII>

    <!-- 这里别使用v-show,v-show是display:none控制的，无法检测到高度就无法定位。
     为什么v-if可以？因为触发动态添加，子组件先mounted父组件才会mounted 
     至于缓存问题，建议记录会话-->

    <DrageAbleIm></DrageAbleIm>



  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import ArrowAnimate from '../components/ArrowAnimate.vue'
import HCII from '../components/HCII.vue'
import DrageAbleIm from '../pages/dragIM/dragAbleIm.vue'
import UserCenter from './UserCenter.vue'
import { useAiStore } from '../stores/aiImStore'
import { storeToRefs } from 'pinia'

const AiStore = useAiStore()
const { showAiWindow } = storeToRefs(AiStore)


const router = useRouter()
const route = useRoute()

const menuItems = ref([
  {
    id: 1,
    label: '数据导入处理',
    type: 'btn',
    active: true,
    to: '/intelligent-algorithm' // 可选路由跳转
  },
  {
    id: 2,
    label: '智能模型训练',
    type: 'menu',
    active: false,
    submenu: [
      { label: '仿真管理', value: 'system/simulation' },
    ]
  },
  {
    id: 3,
    label: '智能模型试验',
    type: 'btn',
    active: false,
    to: '/artifact'
  },
  {
    id: 4,
    label: '资源存储管理',
    type: 'menu',
    active: false,
    submenu: [
      { label: '用户管理', value: 'system/users' },
      { label: '人机智能交互', value: 'ai-interaction' },
      { label: '服务器运维管理', value: 'system/server' },
    ]
  },
  {
    id: 5,
    label: '模型在线升级',
    type: 'menu',
    active: false,
    submenu: [
      { label: '模型管理', value: 'system/model' },
      // { label: '软件管理', value: 'software' },
      { label: '数据集管理', value: 'system/dataset' },
      { label: '配置环境管理', value: 'system/config' },
      // { label: '测试', value: 'system/test' }
    ]
  }
])

const updateActiveState = () => {
  menuItems.value.forEach(item => {
    // 处理主按钮
    if (item.to === route.path) {
      item.active = true
      return
    }
    // 处理下拉菜单项
    if (item.submenu) {
      item.active = item.submenu.some(subItem =>
        `/${subItem.value}` === route.path
      )
    } else {
      item.active = false
    }
  })
}

// 初始化时执行一次
onMounted(() => {
  updateActiveState()
})

// 监听路由变化
watch(() => route.path, updateActiveState)

// 处理按钮点击
const handleBtnClick = (clickedItem) => {
  menuItems.value.forEach(item => {
    item.active = item.id === clickedItem.id
  })
  console.log(menuItems.value)

  if (clickedItem.to) {
    router.push(clickedItem.to)
  }
}


const handleSubmenuClick = (parentItem, value) => {
  menuItems.value.forEach(item => {
    item.active = item.id === parentItem.id
  })

  console.log('Selected:', value)

  if (value) {
    let path = '/' + value
    router.push({ path })
  }
}

</script>

<style lang="scss" scoped>
$header-height: .65rem;
$nav-width: 3.75rem;
$nav-collapsed-width: .8rem;

.container {
  padding: .5rem .5rem 0;
  // max-width: 1920px;
  // max-width: 2560px;
  min-width: 1280px;
  // height: calc(100vh - .125rem);
  min-height: 600px;
  max-height: 1600px; // 针对 32寸 
  position: relative;

  // background: #131520;
  .titleBG {
    position: absolute;
    z-index: 1;
    top: 0;
    left: 50%;
    transform: translateX(-50%);

    .titleCenter {
      position: absolute;
      left: 50%;
      top: 15%;
      font-size: .45rem;
      transform: translateX(-50%);
      font-family: MicrosoftYaHei-Bold;
      font-weight: bold;
      text-shadow: 0rem .075rem .075rem rgba(4, 34, 66, 0.45);
    }

    .imgC {
      height: 1.4875rem;
    }
  }

  .rightTop {
    position: absolute;
    top: .1875rem;
    right: .125rem;
    z-index: 100;
  }

  .header {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: $header-height;
    background: #fff;
  }


  .company {
    font-size: 2.125rem;
    line-height: 2.5rem;
    letter-spacing: 0.00735em;
    font-weight: bold;

  }

  .project {
    font-size: 1.825rem;
    line-height: 2.5rem;
    letter-spacing: 0.00735em;
    font-style: italic;
  }

  .layout {
    transform: translateZ(0);
  }

  header {
    .user-center {
      transform: translateY(3.5px);
    }
  }

  aside {
    top: calc($header-height + 40px);
    width: $nav-width;
    transition: width 0.3s ease;

    &.collapsed {
      width: $nav-collapsed-width;
      overflow: hidden;
    }

    .sidebar-header {
      position: relative;

      .collapse-btn {
        position: absolute;
        right: .1875rem;
        top: .25rem;
        z-index: 10;
        background: white;
        box-shadow: 0 0 .0625rem rgba(0, 0, 0, 0.1);
      }
    }
  }

  main {
    top: calc($header-height + 40px);
    left: $nav-width;
    right: -0.25rem;
    padding-bottom: 1rem;
    transition: left 0.3s ease;

    &.expanded {
      left: $nav-collapsed-width;
    }
  }
}

.header-c {
  display: flex;
  justify-content: center;
  margin-top: 1.125rem;
}

.btnContainer {
  // width: 11.25rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

// 定制化按钮
.styleBtn {
  position: relative;
  z-index: 10;
  min-width: 1.875rem;
  border: .0125rem solid #4ab4ff;
  background: radial-gradient(#326ca4, #171e2c) !important;
  color: #4ab4ff !important;

  &::after {
    content: '';
    position: absolute;
    z-index: -1;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 50%;
    background: #151722;
  }
}

.active {
  position: relative;
  z-index: 10;
  width: 1.875rem;
  border: .0125rem solid #ffb618;
  background: radial-gradient(#57441f, #171e2c) !important;
  color: #ffb618 !important;

  &::after {
    content: '';
    position: absolute;
    z-index: -1;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 50%;
    background: #151722;
  }
}

.myMenu {
  .q-list {
    .q-item {}
  }
}

.container main {
  padding-bottom: 0 !important;
}

.downList {
  // background-color:rgba(0, 0, 0);
  overflow: hidden !important;
}

.font14 {
  font-size: .175rem !important;
  // color:$slideText;
}

.arrowAnimate {
  margin-left: .125rem;
  margin-right: .3rem;
}

.HCII {
  position: fixed;
  right: .3125rem;
  bottom: .3125rem;
}

/* 核心样式：禁用选中效果 */
.no-select {
  /* 标准属性 */
  user-select: none;
  /* 火狐浏览器 */
  -moz-user-select: none;
  /* Safari 和老版 Chrome */
  -webkit-user-select: none;
  /* IE/Edge */
  -ms-user-select: none;

  /* 禁止拖动图片 */
  -webkit-user-drag: none;
  user-drag: none;
}
</style>
