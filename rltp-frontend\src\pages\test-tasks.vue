<template>
  <div class="q-pa-md">
    <h4>训练任务测试页面</h4>
    
    <div v-if="loading">
      <q-spinner color="primary" size="3em" />
      <div>加载中...</div>
    </div>
    
    <div v-else-if="error">
      <div class="text-negative">{{ error }}</div>
    </div>
    
    <div v-else>
      <h5>原始数据：</h5>
      <pre>{{ JSON.stringify(rawData, null, 2) }}</pre>
      
      <h5>处理后的数据：</h5>
      <pre>{{ JSON.stringify(processedData, null, 2) }}</pre>
      
      <h5>任务卡片：</h5>
      <div v-for="task in processedData" :key="task.id" class="q-mb-md">
        <q-card class="q-pa-md">
          <div><strong>名称:</strong> {{ task.name }}</div>
          <div><strong>状态:</strong> {{ task.status }} ({{ getStatus<PERSON>abel(task.status) }})</div>
          <div><strong>步骤:</strong> {{ getStepDisplay(task.current_step) }}</div>
          <div><strong>进度:</strong> {{ task.progress }}%</div>
          <div><strong>时长:</strong> {{ convertSecondsToHMS(task.running_time) }}</div>
        </q-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { api } from 'boot/axios'
import { trainingStatus } from 'assets/const'
import { convertSecondsToHMS } from 'assets/utils'

const loading = ref(false)
const error = ref('')
const rawData = ref(null)
const processedData = ref([])

// 状态转换函数
function getStatusNumber(status) {
  const statusMap = {
    'configuring': 0,  // 队列中
    'training': 2,     // 运行中
    'evaluating': 2,   // 运行中
    'completed': 9,    // 完成
    'failed': 7,       // 失败
    'cancelled': 4     // 终止
  }
  return statusMap[status] || 0
}

// 步骤显示函数
function getStepDisplay(step) {
  const stepMap = {
    'step1_data': '数据准备',
    'step2_model': '模型配置',
    'step3_training': '模型训练',
    'step4_evaluation': '模型评估',
    'step5_deployment': '模型部署'
  }
  return stepMap[step] || '未知步骤'
}

// 获取状态标签
function getStatusLabel(statusNumber) {
  return trainingStatus[statusNumber]?.label || '未知状态'
}

async function fetchTasks() {
  loading.value = true
  error.value = ''
  
  try {
    const response = await api.get('/backend/training/tasks/')
    console.log('API响应:', response)
    
    rawData.value = response.data
    
    if (response && response.data) {
      const taskData = response.data.results || []
      
      processedData.value = taskData.map(task => ({
        id: task.id,
        name: task.name,
        status: getStatusNumber(task.status),
        start_time: task.started_at || task.created_at,
        running_time: task.duration ? parseInt(task.duration) : 0,
        creater_name: typeof task.created_by === 'number' ? `用户${task.created_by}` : (task.created_by?.username || '系统'),
        task_id: `task_${task.id}`,
        actor_num: Math.max(task.cpu_usage || 0, 4),
        actor_per_cpu: 2,
        learner_num: 2,
        learner_per_cpu: 4,
        progress: task.progress || 0,
        current_step: task.current_step,
        task_type: task.task_type,
        step_display: task.step_display
      }))
    }
  } catch (err) {
    console.error('获取任务失败:', err)
    error.value = '获取任务失败: ' + (err.response?.data?.message || err.message)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchTasks()
})
</script>
