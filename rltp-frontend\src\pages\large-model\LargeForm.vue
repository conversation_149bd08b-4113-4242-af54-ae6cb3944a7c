<!--
 * @Author: <PERSON>zc
 * @Date: 2025-01-01 10:00:00
 * @LastEditors: Szc
 * @LastEditTime: 2025-01-01 10:00:00
 * @Description: 👉大模型表单主容器👈
-->
<template>
    <div class="main-Form">
        <!-- 按钮区 -->
        <div class="Top">
            <LargeTitleBtn ref="largeTitleBtnRef"></LargeTitleBtn>
            <q-btn class="doReturn" @click="returnToOverview">
                <img class="returnIcon" src="../../assets/images/icon_fh.png" alt="">
                <div class="labelColor">返回模型概览</div>
            </q-btn>
        </div>

        <!-- 根据当前步骤显示对应表单 -->
        <LargeStepOne v-if="currentStep === 1" @next-step="handleNextStep" @prev-step="handlePrevStep"></LargeStepOne>
        <LargeStepTwo v-if="currentStep === 2" @next-step="handleNextStep" @prev-step="handlePrevStep"></LargeStepTwo>
        <LargeStepThree v-if="currentStep === 3" @next-step="handleNextStep" @prev-step="handlePrevStep"></LargeStepThree>
        <LargeStepFour v-if="currentStep === 4" @next-step="handleNextStep" @prev-step="handlePrevStep"></LargeStepFour>
        <LargeStepFive v-if="currentStep === 5" @next-step="handleNextStep" @prev-step="handlePrevStep"></LargeStepFive>
    </div>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import LargeTitleBtn from './components/LargeTitleBtn.vue'
import LargeStepOne from './LargeStepOne.vue'
import LargeStepTwo from './LargeStepTwo.vue'
import LargeStepThree from './LargeStepThree.vue'
import LargeStepFour from './LargeStepFour.vue'
import LargeStepFive from './LargeStepFive.vue'

const router = useRouter()

// 大模型TitleBtn组件引用
const largeTitleBtnRef = ref(null)

// 当前步骤计算属性，与大模型TitleBtn同步
const currentStep = computed(() => {
    return largeTitleBtnRef.value?.currentStep || 1
})

// 处理下一步
function handleNextStep() {
    if (largeTitleBtnRef.value) {
        largeTitleBtnRef.value.nextStep()
        console.log('大模型-当前步骤:', largeTitleBtnRef.value.currentStep)
        
        // 根据当前步骤执行相应逻辑
        const currentStep = largeTitleBtnRef.value.currentStep
        if (currentStep > 1) {
            console.log('执行大模型步骤', currentStep, '的相关逻辑')
            
            // 如果切换到包含图表的步骤，确保DOM完全渲染
            if (currentStep === 3 || currentStep === 4) {
                nextTick(() => {
                    console.log('大模型图表步骤DOM已准备就绪')
                })
            }
        }
    }
}

// 处理上一步
function handlePrevStep() {
    if (largeTitleBtnRef.value) {
        largeTitleBtnRef.value.prevStep()
        console.log('大模型-当前步骤:', largeTitleBtnRef.value.currentStep)
    }
}

// 直接跳转到指定步骤的方法
function goToStep(step) {
    if (largeTitleBtnRef.value) {
        largeTitleBtnRef.value.goToStep(step)
        console.log('大模型-跳转到步骤:', step)
    }
}

// 返回概览页面
function returnToOverview() {
    router.push('/ai-model/largeHome') // 跳转到深度学习入口页
}
</script>

<style lang="scss" scoped>
.main-Form {
    background-color: #131520;
    height: calc(100vh - 1rem); // 减去顶部padding和margin
    display: flex;
    flex-direction: column;
}

.Top {
    margin-top: 1rem;
    margin-bottom: .125rem;
    position: relative;
    
    .doReturn{
        position: absolute;
        left: 0;
        top: 0;
        display: flex;
        
        .returnIcon{
            width: .375rem;
            height: .375rem;
            margin-right: .125rem;
        }
        
        .labelColor {
            color: #4ab4ff;
        }
    }
}

// 通用样式
.labelColor {
    color: #4ab4ff;
}
</style>