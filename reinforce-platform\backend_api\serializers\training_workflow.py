from rest_framework import serializers
from backend_api.models.training_workflow import TrainingWorkflow, TrainingMetrics, ModelEvaluation, InferenceModel


class TrainingWorkflowSerializer(serializers.ModelSerializer):
    """训练工作流序列化器"""
    
    task_type_display = serializers.CharField(source='get_task_type_display', read_only=True)
    current_step_display = serializers.CharField(source='get_current_step_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    progress_percentage = serializers.SerializerMethodField()
    can_proceed = serializers.SerializerMethodField()
    next_step = serializers.SerializerMethodField()
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    
    class Meta:
        model = TrainingWorkflow
        fields = [
            'id', 'name', 'description', 'task_type', 'task_type_display',
            'current_step', 'current_step_display', 'status', 'status_display',
            'created_at', 'updated_at', 'started_at', 'completed_at',
            'step1_data_config', 'step2_model_config', 'step3_training_config',
            'step4_evaluation_config', 'step5_deployment_config',
            'training_task_id', 'model_path', 'best_metrics',
            'progress_percentage', 'can_proceed', 'next_step',
            'created_by', 'created_by_name'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'created_by']
    
    def get_progress_percentage(self, obj):
        return obj.get_progress_percentage()
    
    def get_can_proceed(self, obj):
        return obj.can_proceed_to_next_step()
    
    def get_next_step(self, obj):
        return obj.get_next_step()


class TrainingWorkflowCreateSerializer(serializers.ModelSerializer):
    """创建训练工作流序列化器"""
    
    class Meta:
        model = TrainingWorkflow
        fields = ['name', 'description', 'task_type']
    
    def create(self, validated_data):
        # 自动设置创建者
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class TrainingWorkflowListSerializer(serializers.ModelSerializer):
    """训练工作流列表序列化器"""
    
    task_type_display = serializers.CharField(source='get_task_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    progress_percentage = serializers.SerializerMethodField()
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    
    class Meta:
        model = TrainingWorkflow
        fields = [
            'id', 'name', 'description', 'task_type', 'task_type_display',
            'current_step', 'status', 'status_display',
            'created_at', 'updated_at', 'progress_percentage',
            'created_by_name'
        ]
    
    def get_progress_percentage(self, obj):
        return obj.get_progress_percentage()


class WorkflowStepDataSerializer(serializers.Serializer):
    """工作流步骤数据序列化器"""
    
    data = serializers.JSONField()
    
    def validate_data(self, value):
        if not isinstance(value, dict):
            raise serializers.ValidationError("数据必须是JSON对象格式")
        return value


class TrainingMetricsSerializer(serializers.ModelSerializer):
    """训练指标序列化器"""
    
    class Meta:
        model = TrainingMetrics
        fields = [
            'id', 'workflow', 'epoch', 'step',
            'train_loss', 'val_loss', 'train_accuracy', 'val_accuracy',
            'precision', 'recall', 'map50', 'map50_95',
            'learning_rate', 'gpu_memory', 'training_time',
            'additional_metrics', 'recorded_at'
        ]
        read_only_fields = ['id', 'recorded_at']


class TrainingMetricsCreateSerializer(serializers.ModelSerializer):
    """创建训练指标序列化器"""
    
    class Meta:
        model = TrainingMetrics
        fields = [
            'epoch', 'step', 'train_loss', 'val_loss',
            'train_accuracy', 'val_accuracy', 'precision', 'recall',
            'map50', 'map50_95', 'learning_rate', 'gpu_memory',
            'training_time', 'additional_metrics'
        ]


class ModelEvaluationSerializer(serializers.ModelSerializer):
    """模型评估序列化器"""
    
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = ModelEvaluation
        fields = [
            'id', 'workflow', 'evaluation_name', 'model_path', 'test_dataset_path',
            'overall_accuracy', 'precision', 'recall', 'f1_score',
            'map50', 'map50_95', 'inference_time_ms', 'fps', 'model_size_mb',
            'class_metrics', 'confusion_matrix', 'evaluation_report',
            'status', 'status_display', 'started_at', 'completed_at', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class ModelEvaluationCreateSerializer(serializers.ModelSerializer):
    """创建模型评估序列化器"""
    
    class Meta:
        model = ModelEvaluation
        fields = [
            'evaluation_name', 'model_path', 'test_dataset_path'
        ]


class InferenceModelSerializer(serializers.ModelSerializer):
    """推理模型序列化器"""
    
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    success_rate = serializers.SerializerMethodField()
    
    class Meta:
        model = InferenceModel
        fields = [
            'id', 'workflow', 'model_name', 'model_version', 'model_path',
            'deployment_config', 'status', 'status_display',
            'inference_time_ms', 'throughput_qps', 'model_size_mb',
            'total_requests', 'successful_requests', 'success_rate',
            'deployed_at', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_success_rate(self, obj):
        if obj.total_requests > 0:
            return round((obj.successful_requests / obj.total_requests) * 100, 2)
        return 0.0


class InferenceModelCreateSerializer(serializers.ModelSerializer):
    """创建推理模型序列化器"""
    
    class Meta:
        model = InferenceModel
        fields = [
            'model_name', 'model_version', 'model_path', 'deployment_config'
        ]


class WorkflowOverviewSerializer(serializers.Serializer):
    """工作流概览序列化器"""
    
    total_workflows = serializers.IntegerField()
    active_workflows = serializers.IntegerField()
    completed_workflows = serializers.IntegerField()
    failed_workflows = serializers.IntegerField()
    
    task_types = serializers.ListField(
        child=serializers.DictField()
    )
    
    recent_workflows = TrainingWorkflowListSerializer(many=True)
    
    training_stats = serializers.DictField()


class TrainingStartSerializer(serializers.Serializer):
    """启动训练序列化器"""
    
    workflow_id = serializers.IntegerField()
    training_config = serializers.JSONField()
    
    def validate_workflow_id(self, value):
        try:
            workflow = TrainingWorkflow.objects.get(id=value)
            if workflow.status == 'training':
                raise serializers.ValidationError("该工作流已在训练中")
            return value
        except TrainingWorkflow.DoesNotExist:
            raise serializers.ValidationError("工作流不存在")


class TrainingStatusSerializer(serializers.Serializer):
    """训练状态序列化器"""
    
    workflow_id = serializers.IntegerField()
    status = serializers.CharField()
    progress = serializers.FloatField()
    current_epoch = serializers.IntegerField()
    total_epochs = serializers.IntegerField()
    estimated_time_remaining = serializers.IntegerField()  # 秒
    latest_metrics = TrainingMetricsSerializer(required=False)
