from rest_framework import serializers
from backend_api.models.training_workflow import (
    TrainingWorkflow, 
    TrainingMetricsRecord, 
    ModelEvaluation, 
    InferenceModel
)


class TrainingWorkflowListSerializer(serializers.ModelSerializer):
    """训练工作流列表序列化器"""
    
    task_type_display = serializers.CharField(source='get_task_type_display', read_only=True)
    current_step_display = serializers.CharField(source='get_current_step_display', read_only=True)
    progress_percentage = serializers.SerializerMethodField()
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    
    class Meta:
        model = TrainingWorkflow
        fields = [
            'id', 'name', 'description', 'task_type', 'task_type_display',
            'current_step', 'current_step_display', 'status', 'progress_percentage',
            'created_at', 'updated_at', 'created_by_name'
        ]
    
    def get_progress_percentage(self, obj):
        return obj.get_step_progress()


class TrainingWorkflowDetailSerializer(serializers.ModelSerializer):
    """训练工作流详情序列化器"""
    
    task_type_display = serializers.CharField(source='get_task_type_display', read_only=True)
    current_step_display = serializers.CharField(source='get_current_step_display', read_only=True)
    progress_percentage = serializers.SerializerMethodField()
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    can_proceed = serializers.SerializerMethodField()
    
    class Meta:
        model = TrainingWorkflow
        fields = [
            'id', 'name', 'description', 'task_type', 'task_type_display',
            'current_step', 'current_step_display', 'status', 'progress_percentage',
            'created_at', 'updated_at', 'started_at', 'completed_at',
            'created_by_name', 'can_proceed',
            'step1_data_config', 'step2_model_config', 
            'step3_training_config', 'step4_evaluation_config',
            'training_task_id', 'model_path', 'best_metrics'
        ]
    
    def get_progress_percentage(self, obj):
        return obj.get_step_progress()
    
    def get_can_proceed(self, obj):
        return obj.can_proceed_to_next_step()


class TrainingWorkflowCreateSerializer(serializers.ModelSerializer):
    """创建训练工作流序列化器"""
    
    class Meta:
        model = TrainingWorkflow
        fields = ['name', 'description', 'task_type']
    
    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class TrainingWorkflowUpdateSerializer(serializers.ModelSerializer):
    """更新训练工作流序列化器"""
    
    class Meta:
        model = TrainingWorkflow
        fields = [
            'name', 'description', 'current_step', 'status',
            'step1_data_config', 'step2_model_config', 
            'step3_training_config', 'step4_evaluation_config',
            'training_task_id', 'model_path', 'best_metrics',
            'started_at', 'completed_at'
        ]
    
    def update(self, instance, validated_data):
        # 如果更新了配置，自动更新时间戳
        if any(key.endswith('_config') for key in validated_data.keys()):
            from django.utils import timezone
            instance.updated_at = timezone.now()
        
        return super().update(instance, validated_data)


class StepDataSerializer(serializers.Serializer):
    """步骤数据序列化器"""
    
    step = serializers.CharField()
    data = serializers.JSONField()
    
    def validate_step(self, value):
        valid_steps = ['step1_data', 'step2_model', 'step3_training', 'step4_evaluation']
        if value not in valid_steps:
            raise serializers.ValidationError(f"Invalid step. Must be one of: {valid_steps}")
        return value


class TrainingMetricsRecordSerializer(serializers.ModelSerializer):
    """训练指标记录序列化器"""
    
    class Meta:
        model = TrainingMetricsRecord
        fields = [
            'id', 'workflow', 'training_task_id', 'epoch',
            'train_loss', 'val_loss', 'train_accuracy', 'val_accuracy',
            'precision', 'recall', 'map50', 'map50_95',
            'learning_rate', 'gpu_memory', 'training_time',
            'additional_metrics', 'recorded_at'
        ]
        read_only_fields = ['id', 'recorded_at']


class TrainingMetricsCreateSerializer(serializers.ModelSerializer):
    """创建训练指标序列化器"""
    
    class Meta:
        model = TrainingMetricsRecord
        fields = [
            'workflow', 'training_task_id', 'epoch',
            'train_loss', 'val_loss', 'train_accuracy', 'val_accuracy',
            'precision', 'recall', 'map50', 'map50_95',
            'learning_rate', 'gpu_memory', 'training_time',
            'additional_metrics'
        ]


class ModelEvaluationSerializer(serializers.ModelSerializer):
    """模型评估序列化器"""
    
    workflow_name = serializers.CharField(source='workflow.name', read_only=True)
    
    class Meta:
        model = ModelEvaluation
        fields = [
            'id', 'workflow', 'workflow_name',
            'overall_accuracy', 'class_accuracies', 'confusion_matrix',
            'test_loss', 'test_samples_count',
            'evaluation_report', 'evaluation_images',
            'evaluated_at'
        ]
        read_only_fields = ['id', 'evaluated_at']


class InferenceModelSerializer(serializers.ModelSerializer):
    """推理模型序列化器"""
    
    workflow_name = serializers.CharField(source='workflow.name', read_only=True)
    task_type = serializers.CharField(source='workflow.task_type', read_only=True)
    task_type_display = serializers.CharField(source='workflow.get_task_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = InferenceModel
        fields = [
            'id', 'workflow', 'workflow_name', 'task_type', 'task_type_display',
            'model_name', 'model_version', 'model_path', 'model_size',
            'status', 'status_display',
            'inference_time', 'memory_usage',
            'deployment_config', 'api_endpoint',
            'created_at', 'deployed_at'
        ]
        read_only_fields = ['id', 'created_at']


class InferenceModelListSerializer(serializers.ModelSerializer):
    """推理模型列表序列化器"""
    
    workflow_name = serializers.CharField(source='workflow.name', read_only=True)
    task_type_display = serializers.CharField(source='workflow.get_task_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = InferenceModel
        fields = [
            'id', 'workflow', 'workflow_name', 'task_type_display',
            'model_name', 'model_version', 'status', 'status_display',
            'inference_time', 'memory_usage', 'created_at', 'deployed_at'
        ]


class WorkflowStepNavigationSerializer(serializers.Serializer):
    """工作流步骤导航序列化器"""
    
    workflow_id = serializers.IntegerField()
    current_step = serializers.CharField()
    next_step = serializers.CharField(required=False)
    step_data = serializers.JSONField(required=False)
    
    def validate_current_step(self, value):
        valid_steps = ['draft', 'step1_data', 'step2_model', 'step3_training', 'step4_evaluation', 'completed']
        if value not in valid_steps:
            raise serializers.ValidationError(f"Invalid step. Must be one of: {valid_steps}")
        return value
