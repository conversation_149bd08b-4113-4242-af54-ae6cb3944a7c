<template>
  <div class="model-config-step">
    <div class="text-h6 q-mb-md">模型配置</div>
    <p class="text-grey-6 q-mb-lg">选择和配置训练模型的架构和参数</p>

    <q-form @submit.prevent="handleSave" class="q-gutter-md">
      <!-- 模型选择 -->
      <q-card flat bordered>
        <q-card-section>
          <div class="text-subtitle1 q-mb-md">模型架构</div>
          
          <div class="row q-gutter-md">
            <div class="col-12 col-md-6">
              <q-select
                v-model="formData.model_architecture"
                label="选择模型架构"
                :options="modelOptions"
                option-value="value"
                option-label="label"
                emit-value
                map-options
                :rules="[val => !!val || '请选择模型架构']"
              />
            </div>
            
            <div class="col-12 col-md-6">
              <q-select
                v-model="formData.pretrained_model"
                label="预训练模型"
                :options="pretrainedOptions"
                option-value="value"
                option-label="label"
                emit-value
                map-options
                clearable
              />
            </div>
          </div>

          <div class="row q-gutter-md q-mt-md">
            <div class="col-12 col-md-6">
              <q-input
                v-model.number="formData.num_classes"
                label="类别数量"
                type="number"
                min="1"
                max="1000"
                :rules="[val => val >= 1 && val <= 1000 || '请输入1-1000之间的值']"
              />
            </div>
            
            <div class="col-12 col-md-6">
              <q-input
                v-model.number="formData.input_size"
                label="输入尺寸"
                type="number"
                min="224"
                max="1024"
                step="32"
                :rules="[val => val >= 224 && val <= 1024 || '请输入224-1024之间的值']"
              />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 模型参数 -->
      <q-card flat bordered>
        <q-card-section>
          <div class="text-subtitle1 q-mb-md">模型参数</div>
          
          <div class="row q-gutter-md">
            <div class="col-12 col-md-4">
              <q-input
                v-model.number="formData.learning_rate"
                label="学习率"
                type="number"
                min="0.0001"
                max="1"
                step="0.0001"
                :rules="[val => val >= 0.0001 && val <= 1 || '请输入0.0001-1之间的值']"
              />
            </div>
            
            <div class="col-12 col-md-4">
              <q-input
                v-model.number="formData.batch_size"
                label="批次大小"
                type="number"
                min="1"
                max="128"
                :rules="[val => val >= 1 && val <= 128 || '请输入1-128之间的值']"
              />
            </div>
            
            <div class="col-12 col-md-4">
              <q-input
                v-model.number="formData.epochs"
                label="训练轮次"
                type="number"
                min="1"
                max="1000"
                :rules="[val => val >= 1 && val <= 1000 || '请输入1-1000之间的值']"
              />
            </div>
          </div>

          <div class="row q-gutter-md q-mt-md">
            <div class="col-12 col-md-6">
              <q-select
                v-model="formData.optimizer"
                label="优化器"
                :options="optimizerOptions"
                option-value="value"
                option-label="label"
                emit-value
                map-options
              />
            </div>
            
            <div class="col-12 col-md-6">
              <q-select
                v-model="formData.loss_function"
                label="损失函数"
                :options="lossOptions"
                option-value="value"
                option-label="label"
                emit-value
                map-options
              />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 操作按钮 -->
      <div class="row justify-between q-mt-lg">
        <q-btn
          flat
          color="primary"
          label="上一步"
          icon="arrow_back"
          @click="$emit('previous')"
        />
        
        <div class="q-gutter-md">
          <q-btn
            color="primary"
            label="保存配置"
            type="submit"
            :loading="saving"
          />
          
          <q-btn
            color="primary"
            label="保存并下一步"
            @click="handleSaveAndNext"
            :loading="saving"
            :disable="!isFormValid"
          />
        </div>
      </div>
    </q-form>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useQuasar } from 'quasar'

const props = defineProps({
  workflow: {
    type: Object,
    required: true
  },
  stepData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['save', 'next', 'previous'])

const $q = useQuasar()

// 响应式数据
const formData = ref({
  model_architecture: '',
  pretrained_model: '',
  num_classes: 80,
  input_size: 640,
  learning_rate: 0.001,
  batch_size: 16,
  epochs: 100,
  optimizer: 'adam',
  loss_function: 'cross_entropy'
})

const saving = ref(false)

// 选项数据
const modelOptions = [
  { label: 'YOLOv8n', value: 'yolov8n' },
  { label: 'YOLOv8s', value: 'yolov8s' },
  { label: 'YOLOv8m', value: 'yolov8m' },
  { label: 'YOLOv8l', value: 'yolov8l' },
  { label: 'YOLOv8x', value: 'yolov8x' },
  { label: 'ResNet50', value: 'resnet50' },
  { label: 'ResNet101', value: 'resnet101' },
  { label: 'EfficientNet-B0', value: 'efficientnet_b0' }
]

const pretrainedOptions = [
  { label: 'COCO预训练', value: 'coco' },
  { label: 'ImageNet预训练', value: 'imagenet' },
  { label: '无预训练', value: 'none' }
]

const optimizerOptions = [
  { label: 'Adam', value: 'adam' },
  { label: 'SGD', value: 'sgd' },
  { label: 'AdamW', value: 'adamw' },
  { label: 'RMSprop', value: 'rmsprop' }
]

const lossOptions = [
  { label: '交叉熵损失', value: 'cross_entropy' },
  { label: 'Focal Loss', value: 'focal_loss' },
  { label: 'MSE Loss', value: 'mse_loss' },
  { label: 'Smooth L1 Loss', value: 'smooth_l1' }
]

// 计算属性
const isFormValid = computed(() => {
  return formData.value.model_architecture && 
         formData.value.num_classes > 0 &&
         formData.value.learning_rate > 0 &&
         formData.value.batch_size > 0 &&
         formData.value.epochs > 0
})

// 方法
const handleSave = async () => {
  saving.value = true
  try {
    emit('save', 'step2_model', formData.value)
    $q.notify({
      type: 'positive',
      message: '模型配置已保存'
    })
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

const handleSaveAndNext = async () => {
  if (!isFormValid.value) {
    $q.notify({
      type: 'negative',
      message: '请完善必填信息'
    })
    return
  }

  saving.value = true
  try {
    emit('save', 'step2_model', formData.value)
    emit('next', formData.value)
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

// 初始化数据
onMounted(() => {
  // 如果有已保存的数据，加载它
  if (props.stepData && Object.keys(props.stepData).length > 0) {
    formData.value = { ...formData.value, ...props.stepData }
  }
})
</script>

<style scoped>
.model-config-step {
  max-width: 800px;
}
</style>
