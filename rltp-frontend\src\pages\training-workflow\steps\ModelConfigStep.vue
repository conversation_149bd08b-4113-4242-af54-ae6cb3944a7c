<template>
  <div class="model-config-step">
    <div class="step-header q-mb-lg">
      <h5 class="q-my-none">模型配置</h5>
      <p class="text-grey-6">选择和配置训练模型参数</p>
    </div>

    <q-form @submit.prevent="handleSaveAndNext" class="model-form">
      <!-- 模型选择 -->
      <q-card flat bordered class="q-mb-md">
        <q-card-section>
          <div class="text-h6 q-mb-md">模型选择</div>
          
          <div class="row q-gutter-md">
            <div class="col-5">
              <q-select
                v-model="formData.model_type"
                :options="modelTypeOptions"
                label="模型类型"
                outlined
                option-label="label"
                option-value="value"
                :rules="[val => !!val || '请选择模型类型']"
              />
            </div>
            <div class="col-6">
              <q-select
                v-model="formData.model_size"
                :options="modelSizeOptions"
                label="模型大小"
                outlined
                option-label="label"
                option-value="value"
              />
            </div>
          </div>

          <div class="row q-gutter-md q-mt-md">
            <div class="col-11">
              <q-input
                v-model="formData.pretrained_model_path"
                label="预训练模型路径"
                outlined
                hint="可选：使用预训练模型进行迁移学习"
              />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 模型参数 -->
      <q-card flat bordered class="q-mb-md">
        <q-card-section>
          <div class="text-h6 q-mb-md">模型参数</div>
          
          <div class="row q-gutter-md">
            <div class="col-3">
              <q-input
                v-model.number="formData.num_classes"
                label="类别数量"
                type="number"
                outlined
                min="1"
                max="1000"
                :rules="[val => val > 0 || '请输入有效的类别数量']"
              />
            </div>
            <div class="col-3">
              <q-input
                v-model.number="formData.input_size"
                label="输入尺寸"
                type="number"
                outlined
                min="224"
                max="1024"
                step="32"
                suffix="px"
              />
            </div>
            <div class="col-3">
              <q-select
                v-model="formData.activation"
                :options="activationOptions"
                label="激活函数"
                outlined
              />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 网络结构配置 -->
      <q-card flat bordered class="q-mb-md">
        <q-card-section>
          <div class="text-h6 q-mb-md">网络结构配置</div>
          
          <div class="row q-gutter-md">
            <div class="col-3">
              <q-input
                v-model.number="formData.depth_multiple"
                label="深度倍数"
                type="number"
                outlined
                min="0.1"
                max="2.0"
                step="0.1"
              />
            </div>
            <div class="col-3">
              <q-input
                v-model.number="formData.width_multiple"
                label="宽度倍数"
                type="number"
                outlined
                min="0.1"
                max="2.0"
                step="0.1"
              />
            </div>
            <div class="col-3">
              <q-toggle
                v-model="formData.use_attention"
                label="使用注意力机制"
              />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 损失函数配置 -->
      <q-card flat bordered class="q-mb-md">
        <q-card-section>
          <div class="text-h6 q-mb-md">损失函数配置</div>
          
          <div class="row q-gutter-md">
            <div class="col-4">
              <q-select
                v-model="formData.loss_function"
                :options="lossFunctionOptions"
                label="损失函数"
                outlined
              />
            </div>
            <div class="col-3">
              <q-input
                v-model.number="formData.focal_loss_alpha"
                label="Focal Loss Alpha"
                type="number"
                outlined
                min="0.1"
                max="1.0"
                step="0.1"
                v-if="formData.loss_function === 'focal_loss'"
              />
            </div>
            <div class="col-3">
              <q-input
                v-model.number="formData.focal_loss_gamma"
                label="Focal Loss Gamma"
                type="number"
                outlined
                min="0.5"
                max="5.0"
                step="0.5"
                v-if="formData.loss_function === 'focal_loss'"
              />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 模型验证 -->
      <q-card flat bordered class="q-mb-md">
        <q-card-section>
          <div class="text-h6 q-mb-md">模型验证</div>
          
          <div class="validation-section">
            <q-btn
              color="secondary"
              label="验证模型配置"
              @click="validateModel"
              :loading="validating"
              class="q-mr-md"
            />
            
            <div v-if="validationResult" class="q-mt-md">
              <q-banner 
                :class="validationResult.success ? 'bg-green-1 text-green-8' : 'bg-red-1 text-red-8'"
              >
                <template v-slot:avatar>
                  <q-icon :name="validationResult.success ? 'check_circle' : 'error'" />
                </template>
                {{ validationResult.message }}
              </q-banner>
              
              <div v-if="validationResult.details" class="q-mt-md">
                <div class="text-subtitle2">模型详情：</div>
                <ul>
                  <li>预计参数量：{{ validationResult.details.estimated_params }}</li>
                  <li>预计模型大小：{{ validationResult.details.estimated_size }}</li>
                  <li>预计内存占用：{{ validationResult.details.estimated_memory }}</li>
                </ul>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 操作按钮 -->
      <div class="step-actions q-mt-lg">
        <q-btn
          flat
          label="上一步"
          @click="$emit('previous')"
          class="q-mr-md"
        />
        
        <q-btn
          color="primary"
          label="保存并下一步"
          type="submit"
          :loading="saving"
          :disable="!isFormValid"
        />
      </div>
    </q-form>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useQuasar } from 'quasar'

const props = defineProps({
  workflow: {
    type: Object,
    required: true
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['save', 'next', 'previous'])

const $q = useQuasar()

// 响应式数据
const formData = ref({
  model_type: 'yolov8n', // 设置默认值
  model_size: 'nano',
  pretrained_model_path: '',
  num_classes: 80, // 设置默认值
  input_size: 640,
  activation: 'silu',
  depth_multiple: 0.33,
  width_multiple: 0.25,
  use_attention: false,
  loss_function: 'cross_entropy',
  focal_loss_alpha: 0.25,
  focal_loss_gamma: 2.0
})

const modelTypeOptions = ref([
  { label: 'YOLOv8 Nano', value: 'yolov8n' },
  { label: 'YOLOv8 Small', value: 'yolov8s' },
  { label: 'YOLOv8 Medium', value: 'yolov8m' },
  { label: 'YOLOv8 Large', value: 'yolov8l' },
  { label: 'YOLOv8 XLarge', value: 'yolov8x' }
])

const modelSizeOptions = ref([
  { label: 'Nano (最快)', value: 'nano' },
  { label: 'Small (平衡)', value: 'small' },
  { label: 'Medium (中等)', value: 'medium' },
  { label: 'Large (精确)', value: 'large' },
  { label: 'XLarge (最精确)', value: 'xlarge' }
])

const activationOptions = ref([
  { label: 'SiLU', value: 'silu' },
  { label: 'ReLU', value: 'relu' },
  { label: 'LeakyReLU', value: 'leaky_relu' },
  { label: 'Mish', value: 'mish' }
])

const lossFunctionOptions = ref([
  { label: '交叉熵损失', value: 'cross_entropy' },
  { label: 'Focal Loss', value: 'focal_loss' },
  { label: 'BCE Loss', value: 'bce_loss' }
])

const saving = ref(false)
const validating = ref(false)
const validationResult = ref(null)

// 计算属性
const isFormValid = computed(() => {
  return formData.value.model_type && 
         formData.value.num_classes > 0 && 
         formData.value.input_size > 0
})

// 监听props变化
watch(() => props.data, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(formData.value, newData)
  }
}, { immediate: true })

// 方法
const validateModel = async () => {
  validating.value = true
  try {
    // 模拟模型验证
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    validationResult.value = {
      success: true,
      message: '模型配置验证通过',
      details: {
        estimated_params: '7.2M',
        estimated_size: '14.4MB',
        estimated_memory: '2.1GB'
      }
    }
  } catch (error) {
    validationResult.value = {
      success: false,
      message: '模型配置验证失败'
    }
  } finally {
    validating.value = false
  }
}

const handleSaveAndNext = async () => {
  console.log('ModelConfigStep: 点击保存并下一步')
  console.log('表单数据:', formData.value)
  
  if (!isFormValid.value) {
    $q.notify({
      type: 'negative',
      message: '请完善必填信息'
    })
    return
  }

  saving.value = true
  try {
    console.log('发送save事件:', 'step2_model', formData.value)
    emit('save', 'step2_model', formData.value)
    
    console.log('发送next事件:', formData.value)
    emit('next', formData.value)
    
    $q.notify({
      type: 'positive',
      message: '步骤2数据已保存'
    })
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.model-config-step {
  padding: 24px;
}

.step-header {
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 16px;
}

.model-form .q-card {
  margin-bottom: 16px;
}

.validation-section {
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.step-actions {
  display: flex;
  justify-content: flex-end;
  padding-top: 24px;
  border-top: 1px solid #e0e0e0;
}
</style>
