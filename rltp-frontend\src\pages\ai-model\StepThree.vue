<template>
  <div class="content">
    <div class="top">
      <div class="left">
        <!-- 基础信息 -->
                 <div class="info-section">

           <div class="section-tabs">
             <q-btn 
               flat 
               :class="{ 'tab-active': activeTab === 'basic' }" 
               class="tab-btn"
               label="基础信息" 
               @click="switchTab('basic')"
             />
             <q-btn 
               flat 
               :class="{ 'tab-active': activeTab === 'log' }" 
               class="tab-btn"
               label="日志详情" 
               @click="switchTab('log')"
             />
           </div>

           <!-- 基础信息表格 -->
           <div v-if="activeTab === 'basic'">
             <div class="table-header">
               <div class="header-cell">类别</div>
               <div class="header-cell"></div>
               <div class="header-cell">基础数值</div>
             </div>

                         <div class="info-table">
              <div v-for="(row, index) in tableData" :key="index" class="table-row">
                <!-- 训练控制按钮行（完全替换任务状态行） -->
                <template v-if="row.type === 'training-controls'">
                  <div class="training-controls-row">
                    <div class="training-controls">
                      <q-btn
                        class="control-btn roundBox"
                        color="primary"
                        label="开始训练"
                        @click="startTraining"
                        :disabled="taskStatus === 'running' || taskStatus === 'pausing' || taskStatus === 'resuming'"
                      />
                      <q-btn
                        class="control-btn roundBox"
                        color="warning"
                        label="暂停训练"
                        @click="pauseTraining"
                        :disabled="taskStatus !== 'running' || !hasTrainingData"
                      />
                      <q-btn
                        class="control-btn roundBox"
                        color="positive"
                        label="继续训练"
                        @click="resumeTraining"
                        :disabled="taskStatus !== 'paused'"
                      />
                      <q-btn
                        class="control-btn roundBox"
                        color="negative"
                        label="停止训练"
                        @click="stopTraining"
                        :disabled="taskStatus === 'completed' || taskStatus === 'failed' || taskStatus === 'cancelled' || taskStatus === 'pending'"
                      />
                    </div>
                  </div>
                </template>
                <!-- 普通行 -->
                <template v-else>
                  <div class="cell">{{ row.label }}</div>
                  <div class="cell" :class="row.valueClass">
                    <template v-if="row.type === 'status'">
                      <img v-if="row.value === '运行成功'" src="../../assets/images/icon_cg.png" alt="成功" class="status-icon">
                      <img v-else-if="row.value === '运行失败'" src="" alt="失败" class="status-icon">
                      {{ row.value }}
                    </template>
                    <template v-else>
                      {{ row.value }}
                    </template>
                  </div>
                  <div class="cell blue-text">
                    {{ row.extra || '' }}
                  </div>
                </template>
              </div>
            </div>
           </div>

           <!-- 日志详情 -->
           <div v-if="activeTab === 'log'" class="log-section">
             <div class="log-content" ref="logContainer">
               <div v-if="loadingLogs && trainingLogs.length === 0" class="log-loading">
                 <q-spinner-dots size="2em" color="primary" />
                 <div>正在加载日志...</div>
               </div>
               
               <div v-else-if="trainingLogs.length === 0" class="log-empty">
                 <div>暂无训练日志</div>
                 <div class="log-hint">请先开始训练</div>
               </div>
               
               <div v-else class="log-list">
                 <div 
                   v-for="(log, index) in trainingLogs" 
                   :key="index" 
                   class="log-item"
                   :class="getLogLevelClass(log.level)"
                 >
                   <span class="log-timestamp">{{ formatLogTime(log.timestamp) }}</span>
                   <span class="log-level">[{{ log.level }}]</span>
                   <span class="log-message">{{ log.message }}</span>
                 </div>
               </div>
             </div>
           </div>
         </div>
      </div>

      <div class="right">
        <!-- 图表区域 -->
        <div class="charts-section">
          <!-- 损失曲线统计1 -->
          <div class="chart-container">
            <div class="chart-header">
              <img class="arrow-icon" src="../../assets/images/icon_dz.png" alt="">
              <span class="chart-title">损失函数曲线统计</span>
            </div>
            <div class="chart-content">
              <div ref="lossChart1Ref" class="chart"></div>
            </div>
          </div>

          <!-- 损失曲线统计2 -->
          <div class="chart-container">
            <div class="chart-header">
              <img class="arrow-icon" src="../../assets/images/icon_dz.png" alt="">
              <span class="chart-title">集群资源利用率</span>
            </div>
            <div class="chart-content">
                    <div ref="resourceChartRef" class="chart"></div>
            </div>
          </div>
        </div>

        <!-- 下一步按钮 -->
        <div class="bottom">
          <div class="next">
            <q-btn class="prevBtn roundBox" color="grey-7" label="上一步" @click="prevStep" />
            <div class="button-group">
              <q-btn class="saveBtn roundBox" color="green" label="保存" @click="saveCurrentStep" :loading="saving" />
              <q-btn class="nextBtn roundBox" color="primary" label="下一步" @click="nextStepOnly" />
            </div>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, defineEmits, reactive, nextTick, computed } from 'vue'
import * as echarts from 'echarts'
import { useModelFormStore } from '../../stores/modelFormStore'
import { api } from 'boot/axios'
import { usePlugin } from 'composables/plugin.js'
// 定义事件
const emit = defineEmits(['next-step', 'prev-step'])

// 保存状态
const saving = ref(false)

// 获取模型表单存储
const modelFormStore = useModelFormStore()

// 引入通知插件
const { notify, localStorage: quasarLocalStorage } = usePlugin()

// Tab状态
const activeTab = ref('basic')

// 从store获取模型参数数据
const modelParams = computed(() => modelFormStore.stepTwoData.params)
const resourceParams = computed(() => modelFormStore.stepTwoData.resources)

// 检查是否有真实的训练数据（不是初始示例数据）
const hasTrainingData = computed(() => {
  // 检查损失数据中是否有非null的值，且不是初始示例数据
  const hasRealTrainLoss = chartData.loss.trainLoss.some(value =>
    value !== null && value !== undefined &&
    !isInitialExampleData()
  )
  const hasRealValLoss = chartData.loss.valLoss.some(value =>
    value !== null && value !== undefined &&
    !isInitialExampleData()
  )

  return hasRealTrainLoss || hasRealValLoss
})

// 检查当前是否是初始示例数据
function isInitialExampleData() {
  // 如果X轴是[1,2,3,4,5]且训练损失是[0.8,0.7,0.6,0.5,0.45]，则认为是示例数据
  const isExampleEpochs = JSON.stringify(chartData.loss.epochs) === JSON.stringify([1, 2, 3, 4, 5])
  const isExampleTrainLoss = JSON.stringify(chartData.loss.trainLoss) === JSON.stringify([0.8, 0.7, 0.6, 0.5, 0.45])

  return isExampleEpochs && isExampleTrainLoss
}

// 任务状态映射
const statusMap = {
  'not_started': '后台运行中',
  'unknown': '未知',
  'pending': '等待中',
  'running': '运行中',
  'completed': '训练完成',
  'failed': '失败',
  'cancelled': '已取消',
  'cancelling': '取消中',
  'paused': '已暂停',
  'pausing': '暂停中',
  'resuming': '恢复中'
}

// 任务状态样式映射
const statusClassMap = {
  'not_started': 'not-started-status',
  'unknown': 'unknown-status',
  'pending': 'pending-status',
  'running': 'running-status',
  'completed': 'success-status',
  'failed': 'failed-status',
  'cancelled': 'cancelled-status',
  'cancelling': 'cancelling-status',
  'paused': 'paused-status',
  'pausing': 'pausing-status',
  'resuming': 'resuming-status'
}

// 表格数据
const tableData = ref([
  { label: '模型类型', value: '目标识别', extra: '', valueClass: '', type: '' },
  { label: '微调模型', value: 'YOLOv8', extra: '', valueClass: '', type: '' },
  { label: '训练参数设备', value: '', extra: '', valueClass: '', type: '' },
  { label: '', value: '轮次', extra: modelParams.value.epochs, valueClass: '', type: '' },
  { label: '', value: '批大小', extra: modelParams.value.batchSize, valueClass: '', type: '' },
  { label: '', value: 'NPU数量', extra: resourceParams.value.npuCount, valueClass: '', type: '' },
  { label: '', value: 'CPU数量', extra: resourceParams.value.cpuCount, valueClass: '', type: '' },
  { label: '', value: '学习率', extra: modelParams.value.learningRate, valueClass: '', type: '' },
  { label: '', value: '热启动步数', extra: modelParams.value.warmupSteps, valueClass: '', type: '' },
  { label: '', value: 'log打印间隔', extra: modelParams.value.logInterval, valueClass: '', type: '' },
  { label: '', value: '评估、保存间隔', extra: modelParams.value.evalInterval, valueClass: '', type: '' },
  { label: '', value: '断点训练权重', extra: '', valueClass: '', type: '' },
  { label: '', value: '预训练权重', extra: '', valueClass: '', type: '' },
  { label: '数据集', value: modelFormStore.stepOneData.datasetName || '目标识别数据集 (可见光)', extra: '', valueClass: '', type: '' },
  { label: '', value: '', extra: '', valueClass: '', type: 'training-controls' }
])

// 训练任务ID
const trainingTaskId = ref(null)

// 当前任务状态
const taskStatus = ref('not_started')

// 训练状态轮询定时器
let statusPollingInterval = null

// SSE连接
let metricsEventSource = null
let logEventSource = null

// 日志相关状态
const trainingLogs = ref([])
const loadingLogs = ref(false)
const logContainer = ref(null)



// 图表引用
const lossChart1Ref = ref(null)
const resourceChartRef = ref(null)
let lossChart1 = null
let resourceChart = null



// 使用响应式数据存储图表数据
const chartData = reactive({
  loss: {
    epochs: [1, 2, 3, 4, 5], // 初始显示5个数据点
    trainLoss: [0.8, 0.7, 0.6, 0.5, 0.45], // 初始示例数据
    valLoss: [0.9, 0.8, 0.7, 0.6, 0.55] // 初始示例数据
  },
  resource: {
    timestamps: ['10:00:00', '10:01:00', '10:02:00', '10:03:00', '10:04:00'],
    cpuUsage: [65, 70, 68, 72, 69],
    gpuUsage: [80, 85, 82, 88, 84],
    memoryUsage: [75, 78, 76, 80, 77]
  }
})
console.log("测试 》》",chartData.loss)

// 重置损失曲线为初始的5个示例数据（页面进入时显示）
function resetLossChartToDefault() {
  console.log('重置损失曲线为初始示例数据')

  // 设置初始的5个示例数据点
  chartData.loss.epochs = [1, 2, 3, 4, 5]
  chartData.loss.trainLoss = [0.8, 0.7, 0.6, 0.5, 0.45]
  chartData.loss.valLoss = [0.9, 0.8, 0.7, 0.6, 0.55]

  // 立即更新图表
  if (lossChart1) {
    lossChart1.setOption({
      xAxis: { data: chartData.loss.epochs },
      series: [
        { data: chartData.loss.trainLoss },
        { data: chartData.loss.valLoss }
      ]
    })
  }
}

// 初始化损失曲线的固定X轴数据（根据训练轮次参数）
function initLossChartData() {
  const epochs = modelParams.value.epochs || 5
  console.log('初始化损失曲线数据，轮次数:', epochs)

  // 根据左侧轮次参数设置X轴数据
  chartData.loss.epochs = Array.from({ length: epochs }, (_, i) => i + 1)
  chartData.loss.trainLoss = Array.from({ length: epochs }, () => null) // 初始为null，等待真实数据
  chartData.loss.valLoss = Array.from({ length: epochs }, () => null) // 初始为null，等待真实数据

  console.log('损失曲线X轴数据:', chartData.loss.epochs)
  console.log('清空示例数据，等待真实训练数据')

  // 立即更新图表以显示新的X轴
  if (lossChart1) {
    lossChart1.setOption({
      xAxis: { data: chartData.loss.epochs },
      series: [
        { data: chartData.loss.trainLoss },
        { data: chartData.loss.valLoss }
      ]
    })
  }
}

// 抽离损失图表的配置选项 - 完全复制DeepLearningPage.vue的配置
const getLossChartOption = () => {
  return {
    title: {
      text: '损失函数曲线',
      left: 'center',
      textStyle: {
        color: '#fff'
      }
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['训练损失', '验证损失'],
      bottom: 5,
      left: 'center',
      itemGap: 20,
      textStyle: {
        fontSize: window.screen.width <= 1536 ? 12 : 16, 
        color: '#fff', // 设置为白色（与你的主题一致）
      },
    },
    grid: {
      left: '10%',
      right: '4%',
      bottom: '20%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: chartData.loss.epochs,
      name: '迭代轮次',
      nameLocation: 'middle',
      nameGap: 30,
      axisLabel: {
        fontSize: window.screen.width <= 1536 ? 12: 16,  // 文字大小
      },
      nameTextStyle: {
        fontSize: window.screen.width <= 1536 ? 12: 16,  // 文字大小
      },
      axisLine: {
        show: true,
        lineStyle: {
          type: 'solid',
          color: '#fff'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '损失值',
      nameRotate: 0,  // 添加这行，设置名称为水平显示
      nameLocation: 'middle',
      nameGap: 50,
      axisLabel: {
        fontSize:window.screen.width <= 1536 ? 12: 16,  // 文字大小
      },
      nameTextStyle: {
        fontSize:window.screen.width <= 1536 ? 12: 16,  // 文字大小
      },
      axisLine: {
        show: true,
        lineStyle: {
          type: 'solid',
          color: '#fff'
        }
      }
    },
    series: [
      {
        name: '训练损失',
        type: 'line',
        data: chartData.loss.trainLoss,
        symbolSize: 8,
        sampling: 'lttb',
        itemStyle: {
          color: '#FF6B6B',
          shadowBlur: 10,
          shadowColor: 'rgba(255, 107, 107, 0.5)'
        },
        lineStyle: {
          width: 3,
          shadowBlur: 5,
          shadowColor: 'rgba(255, 107, 107, 0.3)'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(255, 107, 107, 0.8)' },
            { offset: 0.3, color: 'rgba(255, 107, 107, 0.4)' },
            { offset: 0.7, color: 'rgba(255, 107, 107, 0.2)' },
            { offset: 1, color: 'rgba(255, 107, 107, 0.05)' }
          ])
        }
      },
      {
        name: '验证损失',
        type: 'line',
        data: chartData.loss.valLoss,
        symbolSize: 8,
        sampling: 'lttb',
        itemStyle: {
          color: '#4ECDC4',
          shadowBlur: 10,
          shadowColor: 'rgba(78, 205, 196, 0.5)'
        },
        lineStyle: {
          width: 3,
          shadowBlur: 5,
          shadowColor: 'rgba(78, 205, 196, 0.3)'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(78, 205, 196, 0.8)' },
            { offset: 0.3, color: 'rgba(78, 205, 196, 0.4)' },
            { offset: 0.7, color: 'rgba(78, 205, 196, 0.2)' },
            { offset: 1, color: 'rgba(78, 205, 196, 0.05)' }
          ])
        }
      }
    ]
  }
}

// 抽离资源图表的配置选项 - 完全复制DeepLearningPage.vue的配置
const getResourceChartOption = () => {
  return {
    title: {
      text: '集群资源利用率',
      left: 'center',
      textStyle: {
        color: '#fff'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      data: ['CPU利用率', 'NPU利用率', '内存利用率'],
      bottom: 5,
      left: 'center',
      itemGap: 20,
      textStyle: {
        fontSize:window.screen.width <= 1536 ? 12: 16,
        color: '#fff', // 设置为白色（与你的主题一致）
      },
    },
    grid: {
      left: '10%',
      right: '4%',
      bottom: '20%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: chartData.resource.timestamps,
      name: '时间',
      nameLocation: 'middle',
      nameGap: 30,
      axisLabel: {
        formatter: function (value) {
          // 如果是完整的ISO时间戳，格式化为简洁的时间显示
          if (value && value.includes('T')) {
            const date = new Date(value)
            return date.toLocaleTimeString('zh-CN', {
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit'
            })
          }
          // 如果已经是简单格式，直接返回
          return value
        },
        fontSize:window.screen.width <= 1536 ? 12: 16,  // 文字大小
      },
      nameTextStyle: {
        fontSize:window.screen.width <= 1536 ? 12: 16,  // 文字大小
      },
      axisLine: {
        show: true,
        lineStyle: {
          type: 'solid',
          color: '#fff'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '利用率(%)',
      nameRotate: 0,  // 添加这行，设置名称为水平显示
      min: 0,
      max: 100,
      nameLocation: 'middle',
      nameGap: 50,
      axisLabel: {
        fontSize: window.screen.width <= 1536 ? 12:16,  // 文字大小
      },
      nameTextStyle: {
        fontSize: window.screen.width <= 1536 ? 12:16,  // 文字大小
      },
      axisLine: {
        show: true,
        lineStyle: {
          type: 'solid',
          color: '#fff'
        }
      }
    },
    series: [
      {
        name: 'CPU利用率',
        type: 'line',
        data: chartData.resource.cpuUsage,
        symbolSize: 7,
        sampling: 'lttb',
        itemStyle: {
          color: '#42E695',
          shadowBlur: 8,
          shadowColor: 'rgba(66, 230, 149, 0.4)'
        },
        lineStyle: {
          width: 3,
          shadowBlur: 4,
          shadowColor: 'rgba(66, 230, 149, 0.3)'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(66, 230, 149, 0.7)' },
            { offset: 0.4, color: 'rgba(66, 230, 149, 0.4)' },
            { offset: 0.8, color: 'rgba(66, 230, 149, 0.15)' },
            { offset: 1, color: 'rgba(66, 230, 149, 0.05)' }
          ])
        }
      },
      {
        name: 'NPU利用率',
        type: 'line',
        data: chartData.resource.gpuUsage,
        symbolSize: 7,
        sampling: 'lttb',
        itemStyle: {
          color: '#FFB347',
          shadowBlur: 8,
          shadowColor: 'rgba(255, 179, 71, 0.4)'
        },
        lineStyle: {
          width: 3,
          shadowBlur: 4,
          shadowColor: 'rgba(255, 179, 71, 0.3)'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(255, 179, 71, 0.7)' },
            { offset: 0.4, color: 'rgba(255, 179, 71, 0.4)' },
            { offset: 0.8, color: 'rgba(255, 179, 71, 0.15)' },
            { offset: 1, color: 'rgba(255, 179, 71, 0.05)' }
          ])
        }
      },
      {
        name: '内存利用率',
        type: 'line',
        data: chartData.resource.memoryUsage,
        symbolSize: 7,
        sampling: 'lttb',
        itemStyle: {
          color: '#A855F7',
          shadowBlur: 8,
          shadowColor: 'rgba(168, 85, 247, 0.4)'
        },
        lineStyle: {
          width: 3,
          shadowBlur: 4,
          shadowColor: 'rgba(168, 85, 247, 0.3)'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(168, 85, 247, 0.7)' },
            { offset: 0.4, color: 'rgba(168, 85, 247, 0.4)' },
            { offset: 0.8, color: 'rgba(168, 85, 247, 0.15)' },
            { offset: 1, color: 'rgba(168, 85, 247, 0.05)' }
          ])
        }
      }
    ]
  }
}

// 初始化损失曲线图1
function initLossChart1() {
  if (lossChart1Ref.value && lossChart1Ref.value.$el) {
    try {
      // 如果已存在图表实例，先销毁
      if (lossChart1) {
        lossChart1.dispose()
      }
      lossChart1 = echarts.init(lossChart1Ref.value)
      lossChart1.setOption(getLossChartOption())
    } catch (error) {
      console.error('初始化损失图表失败:', error)
      // 延迟重试
      setTimeout(() => {
        initLossChart1()
      }, 100)
    }
  } else if (lossChart1Ref.value) {
    try {
      // 如果已存在图表实例，先销毁
      if (lossChart1) {
        lossChart1.dispose()
      }
      lossChart1 = echarts.init(lossChart1Ref.value)
      lossChart1.setOption(getLossChartOption())
    } catch (error) {
      console.error('初始化损失图表失败:', error)
      // 延迟重试
      setTimeout(() => {
        initLossChart1()
      }, 100)
    }
  }
}

// 初始化资源利用率图
function initResourceChart() {
  if (resourceChartRef.value && resourceChartRef.value.$el) {
    try {
      // 如果已存在图表实例，先销毁
      if (resourceChart) {
        resourceChart.dispose()
      }
      resourceChart = echarts.init(resourceChartRef.value)
      resourceChart.setOption(getResourceChartOption())
    } catch (error) {
      console.error('初始化资源图表失败:', error)
      // 延迟重试
      setTimeout(() => {
        initResourceChart()
      }, 100)
    }
  } else if (resourceChartRef.value) {
    try {
      // 如果已存在图表实例，先销毁
      if (resourceChart) {
        resourceChart.dispose()
      }
      resourceChart = echarts.init(resourceChartRef.value)
      resourceChart.setOption(getResourceChartOption())
    } catch (error) {
      console.error('初始化资源图表失败:', error)
      // 延迟重试
      setTimeout(() => {
        initResourceChart()
      }, 100)
    }
  }
}

// 窗口大小改变时重置图表大小
function resizeCharts() {
  lossChart1?.resize()
  resourceChart?.resize()
}



// 保存当前步骤数据
const saveCurrentStep = async () => {
  try {
    saving.value = true

    // 收集当前步骤的训练配置数据
    const stepData = {
      epochs: 100, // 可以从表单获取
      batchSize: 16,
      learningRate: 0.01,
      // 添加其他训练参数
    }

    // 调用后端API保存数据（使用原来下一步的接口）
    try {
      await api.post('/backend/workflows/1/steps/step3_training/', {
        data: stepData
      })
    } catch (apiError) {
      console.warn('后端API调用失败，但继续保存到本地存储:', apiError)
    }

    // 更新训练任务状态
    try {
      await api.patch('/backend/training/tasks/1/', {
        current_step: 'step3_training',
        progress: 60,
        status: 'training',
        training_config: stepData
      })
    } catch (apiError) {
      console.warn('训练任务更新失败，但数据已保存到本地存储:', apiError)
    }

    notify('步骤3数据已保存，开始训练', 'positive')

  } catch (error) {
    console.error('保存步骤3数据失败:', error)
    notify('保存失败，请重试', 'negative')
  } finally {
    saving.value = false
  }
}

// 仅跳转下一步，不保存数据
const nextStepOnly = () => {
  emit('next-step')
}

// 下一步按钮点击事件（仅跳转，不保存数据）
function nextStep() {
  // 仅触发下一步事件，不调用任何后端接口
  emit('next-step')
}

// 上一步按钮点击事件  
function prevStep() {
  emit('prev-step')
}

// 添加开始训练方法
async function startTraining() {
  try {
    // 构建请求参数
    const requestData = {
      algorithm: {
        version: "v8",
        modelPath: "yolov8n.pt"
      },
      training: {
        dataset: {
          id: 1,
          name: modelFormStore.stepOneData.datasetName || "2"
        },
        validationRatio: modelFormStore.stepOneData.validationPercent / 100 || 0.3
      },
      resources: {
        cpuCount: resourceParams.value.cpuCount,
        npuCount: resourceParams.value.npuCount,
        storageSize: "20"
      },
      parameters: {
        learningRate: modelParams.value.learningRate,
        epochs: modelParams.value.epochs,
        maxGradNorm: "1",
        maxSamples: "100000",
        gradAccumulation: "1e-5",
        batchSize: modelParams.value.batchSize,
        learningRateStrategy: "余弦衰减",
        computeType: "bf16"
      },
      otherParams: {
        optimizer: modelParams.value.optimizer,
        momentum: "0.9",
        weightDecay: "1e-4",
        epsilon: "1e-8",
        dropout: "0.1",
        labelSmoothing: "0.1",
        useGradientClipping: true,
        useMixedPrecision: true,
        earlyStopping: 5,
        checkpointFreq: parseInt(modelParams.value.evalInterval) || 10,
        warmupSteps: parseInt(modelParams.value.warmupSteps) || 1000,
        logFreq: parseInt(modelParams.value.logInterval) || 100,
        activation: "ReLU",
        initialization: "He初始化",
        normalization: "BatchNorm",
        attentionHeads: 8
      }
    }
    
    // 发送请求前，先设置为等待中状态
    updateTaskStatus('pending')
    
    // 初始化损失曲线的固定X轴数据
    initLossChartData()
    
    // 清空资源图表数据，准备接收动态数据
    chartData.resource.timestamps = []
    chartData.resource.cpuUsage = []
    chartData.resource.gpuUsage = []
    chartData.resource.memoryUsage = []
    
    console.log('发送训练请求:', requestData)
    
    // 调用API
    try {
      const response = await api.post('backend/training/start', requestData)
      console.log('训练接口响应:', response)
      
      // 检查响应结构
      console.log('响应类型:', typeof response)
      console.log('响应内容:', JSON.stringify(response))
      
      // 提取trainingId和status
      let trainingId = null
      let status = null
      
      // 根据响应结构提取数据
      if (response && typeof response === 'object') {
        // 如果response直接包含trainingId和status
        if (response.trainingId !== undefined) {
          trainingId = response.trainingId
        } else if (response.id !== undefined) {
          trainingId = response.id
        }
        
        // 获取状态
        if (response.status !== undefined) {
          status = response.status
          console.log('从响应中获取到状态:', status)
        }
      }
      
      if (trainingId) {
        trainingTaskId.value = trainingId
        
        // 使用API返回的状态
        if (status) {
          console.log('更新状态为:', status)
          updateTaskStatus(status)
          notify(`训练任务已提交，状态: ${statusMap[status] || status}`, 'positive')
        } else {
          notify('训练任务已提交', 'positive')
        }
        
        // 开始轮询任务状态
        startStatusPolling()
      } else {
        throw new Error('训练任务创建失败，未获取到任务ID')
      }
    } catch (error) {
      console.error('训练API调用失败:', error)
      notify('训练任务提交失败: ' + (error.message || '未知错误'), 'negative')
    }
  } catch (error) {
    console.error('准备训练请求失败:', error)
    notify('准备训练请求失败: ' + (error.message || '未知错误'), 'negative')
  }
}

// 开始轮询任务状态
function startStatusPolling() {
  // 清除可能存在的旧定时器
  if (statusPollingInterval) {
    clearInterval(statusPollingInterval)
  }
  
  // 设置SSE连接获取实时指标
  setupMetricsStream()
  
  /* 保留原有轮询逻辑作为备用
  // 每5秒查询一次任务状态
  statusPollingInterval = setInterval(async () => {
    if (!trainingTaskId.value) {
      clearInterval(statusPollingInterval)
      return
    }
    
    try {
      // 参考DeepLearningPage.vue中的fetchTrainingMetrics方法
      console.log('获取训练ID:', trainingTaskId.value)
      const response = await api.get(`backend/training/${trainingTaskId.value}/metrics`)
      console.log('获取到的训练数据:', response)
      
      // 更新训练状态
      if (response && response.status) {
        console.log('后台返回的状态:', response.status)
        updateTaskStatus(response.status)
        
        // 根据状态判断是否需要通知
        if (response.status === 'not_started') {
          notify(`训练任务${statusMap[response.status] || response.status}`, 'info')
        } else if (response.status === 'running') {
          // 运行中状态无需特别通知
        } else if (response.status === 'completed') {
          clearInterval(statusPollingInterval)
          notify(`训练任务${statusMap[response.status] || response.status}`, 'positive')
        } else if (['failed', 'cancelled', 'unknown'].includes(response.status)) {
          clearInterval(statusPollingInterval)
          notify(`训练任务${statusMap[response.status] || response.status}`, 'negative')
        }
      }
      
      // 更新损失曲线图表数据
      if (response && response.loss) {
        console.log('更新损失曲线数据:', response.loss)
        
        // 更新图表数据
        chartData.loss.epochs = response.loss.epochs || chartData.loss.epochs
        chartData.loss.trainLoss = response.loss.train_loss || chartData.loss.trainLoss
        chartData.loss.valLoss = response.loss.val_loss || chartData.loss.valLoss
        
        // 更新图表
        if (lossChart1) {
          lossChart1.setOption({
            xAxis: { data: chartData.loss.epochs },
            series: [
              { data: chartData.loss.trainLoss },
              { data: chartData.loss.valLoss }
            ]
          })
        }
      }
      
      // 更新资源使用率图表数据
      if (response && response.resources) {
        console.log('更新资源使用率数据:', response.resources)
        
        // 更新图表数据
        chartData.resource.timestamps = response.resources.timestamps || chartData.resource.timestamps
        chartData.resource.cpuUsage = response.resources.cpuUsage || chartData.resource.cpuUsage
        chartData.resource.gpuUsage = response.resources.gpuUsage || chartData.resource.gpuUsage
        chartData.resource.memoryUsage = response.resources.memoryUsage || chartData.resource.memoryUsage
        
        // 更新图表
        if (resourceChart) {
          resourceChart.setOption({
            xAxis: { data: chartData.resource.timestamps },
            series: [
              { data: chartData.resource.cpuUsage },
              { data: chartData.resource.gpuUsage },
              { data: chartData.resource.memoryUsage }
            ]
          })
        }
      }
    } catch (error) {
      console.error('获取任务状态失败:', error)
    }
  }, 5000)
  */
}

// 设置SSE连接获取实时指标
function setupMetricsStream() {
  if (!trainingTaskId.value) {
    console.error('无法建立SSE连接: 缺少训练任务ID')
    return
  }
  
  try {
    // 关闭可能存在的旧连接
    if (metricsEventSource) {
      metricsEventSource.close()
    }
    
    console.log('建立SSE连接获取实时指标, 任务ID:', trainingTaskId.value)
    
    // 获取token - 使用Quasar LocalStorage确保格式一致
    let token = quasarLocalStorage.getItem('token')

    // 如果Quasar LocalStorage没有token，尝试从原生localStorage获取
    if (!token) {
      token = localStorage.getItem('token') || sessionStorage.getItem('token')
    }

    // 处理token格式
    if (token) {
      console.log('原始token格式:', token.substring(0, 15) + '...')

      // 确保token格式正确（移除可能的Bearer前缀，因为后端会重新添加）
      if (token.startsWith('Bearer ')) {
        token = token.substring(7).trim()
      }

      console.log('处理后的token格式:', token.substring(0, 15) + '...')
    }
    
    // 创建SSE连接
    const apiUrl = `http://127.0.0.1:8000/backend/training/${trainingTaskId.value}/metrics-stream`
    console.log('SSE连接URL:', apiUrl)
    console.log('Token是否存在:', !!token)
    console.log('Token长度:', token ? token.length : 0)

    if (!token) {
      console.error('无法获取token，SSE连接将失败')
      notify('无法获取认证token，请重新登录', 'negative')
      return
    }

    // 创建EventSource实例
    // 由于EventSource不支持自定义请求头，使用URL参数传递token
    const urlWithAuth = `${apiUrl}?token=${encodeURIComponent(token)}`
    console.log('完整SSE URL:', urlWithAuth.substring(0, 100) + '...')
    metricsEventSource = new EventSource(urlWithAuth)
    
    // 连接建立时的处理
    metricsEventSource.onopen = () => {
      console.log('SSE指标流连接已建立')
    }
    
    // 处理连接确认事件
    metricsEventSource.addEventListener('connected', (event) => {
      try {
        const data = JSON.parse(event.data)
        console.log('SSE连接已确认:', data)
      } catch (error) {
        console.error('解析连接确认数据失败:', error)
      }
    })
    
    // 处理指标数据事件
    metricsEventSource.addEventListener('metrics', (event) => {
      try {
        console.log('收到SSE指标消息:', event.data)
        const metricsData = JSON.parse(event.data)
        
        // 更新训练状态
        if (metricsData.status) {
          console.log('后台返回的状态:', metricsData.status)
          updateTaskStatus(metricsData.status)
          
          // 根据状态判断是否需要通知
          if (metricsData.status === 'not_started') {
            notify(`训练任务${statusMap[metricsData.status] || metricsData.status}`, 'info')
          } else if (metricsData.status === 'completed') {
            notify(`训练任务${statusMap[metricsData.status] || metricsData.status}`, 'positive')
          } else if (['failed', 'cancelled', 'unknown'].includes(metricsData.status)) {
            notify(`训练任务${statusMap[metricsData.status] || metricsData.status}`, 'negative')
          }
        }
        
        // 更新损失曲线图表数据
        if (metricsData.loss) {
          console.log('更新损失曲线数据:', metricsData.loss)

          // 损失曲线使用固定X轴，按轮次更新对应位置的数据
          if (metricsData.loss.current_epoch !== undefined) {
            const epochIndex = metricsData.loss.current_epoch - 1 // 转换为数组索引
            if (epochIndex >= 0 && epochIndex < chartData.loss.epochs.length) {
              if (metricsData.loss.train_loss !== undefined) {
                chartData.loss.trainLoss[epochIndex] = metricsData.loss.train_loss
                console.log(`更新第${metricsData.loss.current_epoch}轮训练损失:`, metricsData.loss.train_loss)
              }
              if (metricsData.loss.val_loss !== undefined) {
                chartData.loss.valLoss[epochIndex] = metricsData.loss.val_loss
                console.log(`更新第${metricsData.loss.current_epoch}轮验证损失:`, metricsData.loss.val_loss)
              }
              console.log('当前hasTrainingData状态:', hasTrainingData.value)
            }
          } else if (metricsData.loss.train_loss && metricsData.loss.val_loss) {
            // 兼容旧格式：如果没有current_epoch，直接使用整个数组
            chartData.loss.trainLoss = metricsData.loss.train_loss
            chartData.loss.valLoss = metricsData.loss.val_loss
            console.log('使用完整损失数组更新，hasTrainingData状态:', hasTrainingData.value)
          }
          
          // 更新图表
          if (lossChart1) {
            lossChart1.setOption({
              series: [
                { data: chartData.loss.trainLoss },
                { data: chartData.loss.valLoss }
              ]
            })
          }
        }
        
        // 更新资源使用率图表数据
        if (metricsData.resources) {
          console.log('更新资源使用率数据:', metricsData.resources)
          
          // 资源利用率图表动态添加数据点
          if (metricsData.resources.timestamp && 
              metricsData.resources.cpuUsage !== undefined &&
              metricsData.resources.gpuUsage !== undefined &&
              metricsData.resources.memoryUsage !== undefined) {
            
            // 格式化时间戳
            const timestamp = new Date(metricsData.resources.timestamp).toLocaleTimeString('zh-CN')
            
            // 添加新数据点
            chartData.resource.timestamps.push(timestamp)
            chartData.resource.cpuUsage.push(metricsData.resources.cpuUsage)
            chartData.resource.gpuUsage.push(metricsData.resources.gpuUsage)
            chartData.resource.memoryUsage.push(metricsData.resources.memoryUsage)
            
            // 限制最大数据点数量（保持最近100个点）
            const maxDataPoints = 100
            if (chartData.resource.timestamps.length > maxDataPoints) {
              chartData.resource.timestamps.shift()
              chartData.resource.cpuUsage.shift()
              chartData.resource.gpuUsage.shift()
              chartData.resource.memoryUsage.shift()
            }
          } else if (metricsData.resources.timestamps && metricsData.resources.cpuUsage) {
            // 兼容旧格式：如果是数组格式，直接使用
            chartData.resource.timestamps = metricsData.resources.timestamps
            chartData.resource.cpuUsage = metricsData.resources.cpuUsage
            chartData.resource.gpuUsage = metricsData.resources.gpuUsage || chartData.resource.gpuUsage
            chartData.resource.memoryUsage = metricsData.resources.memoryUsage || chartData.resource.memoryUsage
          }
          
          // 更新图表
          if (resourceChart) {
            resourceChart.setOption({
              xAxis: { data: chartData.resource.timestamps },
              series: [
                { data: chartData.resource.cpuUsage },
                { data: chartData.resource.gpuUsage },
                { data: chartData.resource.memoryUsage }
              ]
            })
          }
        }
      } catch (error) {
        console.error('解析SSE指标消息失败:', error, event.data)
      }
    })
    
    // 处理最终数据事件
    metricsEventSource.addEventListener('final', (event) => {
      try {
        console.log('收到最终数据:', event.data)
        const finalData = JSON.parse(event.data)
        
        // 更新图表数据
        updateChartsFromResponse(finalData)
        
        // 更新状态
        if (finalData.status) {
          updateTaskStatus(finalData.status)
        }
      } catch (error) {
        console.error('解析最终数据失败:', error)
      }
    })
    
    // 处理关闭事件
    metricsEventSource.addEventListener('close', (event) => {
      try {
        console.log('收到关闭事件:', event.data)
        const closeData = JSON.parse(event.data)

        // 更新任务状态
        if (closeData.status) {
          updateTaskStatus(closeData.status)
        }

        // 关闭所有连接
        closeAllConnections()

        // 显示状态通知
        if (closeData.status === 'completed') {
          notify('训练任务已完成', 'positive')
        } else if (closeData.status === 'failed') {
          notify('训练任务失败', 'negative')
        } else if (closeData.status === 'cancelled') {
          notify('训练任务已取消', 'warning')
        }
        
        // 关闭连接
        metricsEventSource.close()
        metricsEventSource = null
      } catch (error) {
        console.error('解析关闭事件失败:', error)
      }
    })
    
    // 处理心跳事件
    metricsEventSource.addEventListener('heartbeat', (event) => {
      try {
        const heartbeatData = JSON.parse(event.data)
        console.log('收到心跳:', heartbeatData.timestamp)
      } catch (error) {
        console.error('解析心跳数据失败:', error)
      }
    })
    
    // 处理错误事件
    metricsEventSource.addEventListener('error', (event) => {
      try {
        console.error('SSE错误事件:', event.data)
        const errorData = JSON.parse(event.data)
        notify('获取训练指标错误: ' + (errorData.error || '未知错误'), 'negative')
      } catch (error) {
        console.error('解析错误事件失败:', error)
      }
    })
    
    // 错误处理
    metricsEventSource.onerror = (error) => {
      console.error('SSE指标流连接错误:', error)
      
      // 尝试重新连接
      setTimeout(() => {
        if (metricsEventSource) {
          metricsEventSource.close()
          setupMetricsStream()
        }
      }, 5000)
    }
    
    // 存储EventSource实例以便在组件销毁时关闭
    window.metricsEventSource = metricsEventSource
    
  } catch (error) {
    console.error('设置SSE指标流失败:', error)
    notify('无法连接到实时指标流，将使用轮询方式获取数据', 'warning')
    
    // 如果SSE连接失败，回退到轮询方式
    fallbackToPolling()
  }
}

// 处理SSE事件
function handleSSEEvent(eventType, data) {
  try {
    console.log(`收到SSE ${eventType} 事件:`, data);
    const eventData = JSON.parse(data);
    
    switch (eventType) {
      case 'connected':
        console.log('SSE连接已确认:', eventData);
        break;
        
      case 'metrics':
        // 更新训练状态
        if (eventData.status) {
          console.log('后台返回的状态:', eventData.status);
          updateTaskStatus(eventData.status);
          
          // 根据状态判断是否需要通知
          if (eventData.status === 'not_started') {
            notify(`训练任务${statusMap[eventData.status] || eventData.status}`, 'info');
          } else if (eventData.status === 'completed') {
            notify(`训练任务${statusMap[eventData.status] || eventData.status}`, 'positive');
          } else if (['failed', 'cancelled', 'unknown'].includes(eventData.status)) {
            notify(`训练任务${statusMap[eventData.status] || eventData.status}`, 'negative');
          }
        }
        
        // 更新损失曲线图表数据
        if (eventData.loss) {
          console.log('更新损失曲线数据:', eventData.loss);
          
          // 损失曲线使用固定X轴，按轮次更新对应位置的数据
          if (eventData.loss.current_epoch !== undefined) {
            const epochIndex = eventData.loss.current_epoch - 1; // 转换为数组索引
            if (epochIndex >= 0 && epochIndex < chartData.loss.epochs.length) {
              if (eventData.loss.train_loss !== undefined) {
                chartData.loss.trainLoss[epochIndex] = eventData.loss.train_loss;
              }
              if (eventData.loss.val_loss !== undefined) {
                chartData.loss.valLoss[epochIndex] = eventData.loss.val_loss;
              }
            }
          } else if (eventData.loss.train_loss && eventData.loss.val_loss) {
            // 兼容旧格式：如果没有current_epoch，直接使用整个数组
            chartData.loss.trainLoss = eventData.loss.train_loss;
            chartData.loss.valLoss = eventData.loss.val_loss;
          }
          
          // 更新图表
          if (lossChart1) {
            lossChart1.setOption({
              series: [
                { data: chartData.loss.trainLoss },
                { data: chartData.loss.valLoss }
              ]
            });
          }
        }
        
        // 更新资源使用率图表数据
        if (eventData.resources) {
          console.log('更新资源使用率数据:', eventData.resources);
          
          // 资源利用率图表动态添加数据点
          if (eventData.resources.timestamp && 
              eventData.resources.cpuUsage !== undefined &&
              eventData.resources.gpuUsage !== undefined &&
              eventData.resources.memoryUsage !== undefined) {
            
            // 格式化时间戳
            const timestamp = new Date(eventData.resources.timestamp).toLocaleTimeString('zh-CN');
            
            // 添加新数据点
            chartData.resource.timestamps.push(timestamp);
            chartData.resource.cpuUsage.push(eventData.resources.cpuUsage);
            chartData.resource.gpuUsage.push(eventData.resources.gpuUsage);
            chartData.resource.memoryUsage.push(eventData.resources.memoryUsage);
            
            // 限制最大数据点数量（保持最近100个点）
            const maxDataPoints = 100;
            if (chartData.resource.timestamps.length > maxDataPoints) {
              chartData.resource.timestamps.shift();
              chartData.resource.cpuUsage.shift();
              chartData.resource.gpuUsage.shift();
              chartData.resource.memoryUsage.shift();
            }
          } else if (eventData.resources.timestamps && eventData.resources.cpuUsage) {
            // 兼容旧格式：如果是数组格式，直接使用
            chartData.resource.timestamps = eventData.resources.timestamps;
            chartData.resource.cpuUsage = eventData.resources.cpuUsage;
            chartData.resource.gpuUsage = eventData.resources.gpuUsage || chartData.resource.gpuUsage;
            chartData.resource.memoryUsage = eventData.resources.memoryUsage || chartData.resource.memoryUsage;
          }
          
          // 更新图表
          if (resourceChart) {
            resourceChart.setOption({
              xAxis: { data: chartData.resource.timestamps },
              series: [
                { data: chartData.resource.cpuUsage },
                { data: chartData.resource.gpuUsage },
                { data: chartData.resource.memoryUsage }
              ]
            });
          }
        }
        break;
        
      case 'final':
        console.log('收到最终数据:', eventData);
        
        // 更新图表数据
        updateChartsFromResponse(eventData);
        
        // 更新状态
        if (eventData.status) {
          updateTaskStatus(eventData.status);
        }
        break;
        
      case 'close':
        console.log('收到关闭事件:', eventData);

        // 更新任务状态
        if (eventData.status) {
          updateTaskStatus(eventData.status);
        }

        // 关闭所有连接
        closeAllConnections();

        // 显示状态通知
        if (eventData.status === 'completed') {
          notify('训练任务已完成', 'positive');
        } else if (eventData.status === 'failed') {
          notify('训练任务失败', 'negative');
        } else if (eventData.status === 'cancelled') {
          notify('训练任务已取消', 'warning');
        }
        break;
        
      case 'heartbeat':
        console.log('收到心跳:', eventData.timestamp);
        break;
        
      case 'error':
        console.error('SSE错误事件:', eventData);
        notify('获取训练指标错误: ' + (eventData.error || '未知错误'), 'negative');
        break;
        
      default:
        console.log(`未处理的事件类型: ${eventType}`, eventData);
    }
  } catch (error) {
    console.error(`解析SSE ${eventType} 事件失败:`, error, data);
  }
}

// 回退到轮询方式获取指标
function fallbackToPolling() {
  console.log('回退到轮询方式获取指标')
  
  // 每5秒查询一次任务状态
  statusPollingInterval = setInterval(async () => {
    if (!trainingTaskId.value) {
      clearInterval(statusPollingInterval)
      return
    }
    
    try {
      const response = await api.get(`backend/training/${trainingTaskId.value}/metrics`)
      console.log('轮询获取的训练数据:', response)
      
      // 更新训练状态
      if (response && response.status) {
        updateTaskStatus(response.status)
        
        // 根据状态判断是否需要停止轮询
        if (['completed', 'failed', 'cancelled'].includes(response.status)) {
          clearInterval(statusPollingInterval)

          // 关闭所有SSE连接
          closeAllConnections()

          notify(`训练任务${statusMap[response.status] || response.status}`,
            response.status === 'completed' ? 'positive' : 'negative')
        }
      }
      
      // 更新图表数据
      updateChartsFromResponse(response)
      
    } catch (error) {
      console.error('轮询获取任务状态失败:', error)
    }
  }, 5000)
}

// 关闭所有连接
function closeAllConnections() {
  console.log('关闭所有SSE连接和轮询')

  // 关闭指标SSE连接
  if (metricsEventSource) {
    console.log('关闭指标SSE连接')
    metricsEventSource.close()
    metricsEventSource = null
  }

  // 关闭日志SSE连接
  if (logEventSource) {
    console.log('关闭日志SSE连接')
    logEventSource.close()
    logEventSource = null
  }

  // 清除状态轮询定时器
  if (statusPollingInterval) {
    clearInterval(statusPollingInterval)
    statusPollingInterval = null
  }

  // 清除全局的SSE连接引用
  if (window.metricsEventSource) {
    window.metricsEventSource.close()
    window.metricsEventSource = null
  }
}

// 从响应更新图表数据
function updateChartsFromResponse(response) {
  // 更新损失曲线图表数据
  if (response && response.loss) {
    console.log('更新损失曲线数据:', response.loss)
    
    // 损失曲线使用固定X轴，按轮次更新对应位置的数据
    if (response.loss.current_epoch !== undefined) {
      const epochIndex = response.loss.current_epoch - 1 // 转换为数组索引
      if (epochIndex >= 0 && epochIndex < chartData.loss.epochs.length) {
        if (response.loss.train_loss !== undefined) {
          chartData.loss.trainLoss[epochIndex] = response.loss.train_loss
        }
        if (response.loss.val_loss !== undefined) {
          chartData.loss.valLoss[epochIndex] = response.loss.val_loss
        }
      }
    } else if (response.loss.train_loss && response.loss.val_loss) {
      // 兼容旧格式：如果没有current_epoch，直接使用整个数组
      chartData.loss.trainLoss = response.loss.train_loss
      chartData.loss.valLoss = response.loss.val_loss
    }
    
    // 更新图表
    if (lossChart1) {
      lossChart1.setOption({
        series: [
          { data: chartData.loss.trainLoss },
          { data: chartData.loss.valLoss }
        ]
      })
    }
  }
  
  // 更新资源使用率图表数据
  if (response && response.resources) {
    console.log('更新资源使用率数据:', response.resources)
    
    // 资源利用率图表动态添加数据点
    if (response.resources.timestamp && 
        response.resources.cpuUsage !== undefined &&
        response.resources.gpuUsage !== undefined &&
        response.resources.memoryUsage !== undefined) {
      
      // 格式化时间戳
      const timestamp = new Date(response.resources.timestamp).toLocaleTimeString('zh-CN')
      
      // 添加新数据点
      chartData.resource.timestamps.push(timestamp)
      chartData.resource.cpuUsage.push(response.resources.cpuUsage)
      chartData.resource.gpuUsage.push(response.resources.gpuUsage)
      chartData.resource.memoryUsage.push(response.resources.memoryUsage)
      
      // 限制最大数据点数量（保持最近100个点）
      const maxDataPoints = 100
      if (chartData.resource.timestamps.length > maxDataPoints) {
        chartData.resource.timestamps.shift()
        chartData.resource.cpuUsage.shift()
        chartData.resource.gpuUsage.shift()
        chartData.resource.memoryUsage.shift()
      }
    } else if (response.resources.timestamps && response.resources.cpuUsage) {
      // 兼容旧格式：如果是数组格式，直接使用
      chartData.resource.timestamps = response.resources.timestamps
      chartData.resource.cpuUsage = response.resources.cpuUsage
      chartData.resource.gpuUsage = response.resources.gpuUsage || chartData.resource.gpuUsage
      chartData.resource.memoryUsage = response.resources.memoryUsage || chartData.resource.memoryUsage
    }
    
    // 更新图表
    if (resourceChart) {
      resourceChart.setOption({
        xAxis: { data: chartData.resource.timestamps },
        series: [
          { data: chartData.resource.cpuUsage },
          { data: chartData.resource.gpuUsage },
          { data: chartData.resource.memoryUsage }
        ]
      })
    }
  }
}

// 更新任务状态
function updateTaskStatus(status) {
  console.log('更新任务状态:', status)

  // 更新状态变量（状态显示通过模板中的 statusMap[taskStatus] 自动更新）
  taskStatus.value = status

  console.log(`任务状态更新为: ${statusMap[status] || status}`)
}

// 停止训练
async function stopTraining() {
  try {
    // 检查是否有训练任务ID
    if (!trainingTaskId.value) {
      notify('当前没有正在进行的训练任务', 'warning')
      return
    }

    console.log('开始停止训练，任务ID:', trainingTaskId.value)

    // 1. 调用后端取消训练接口（保持SSE连接以接收状态更新）
    try {
      console.log('调用后端取消训练接口...')
      const response = await api.post(`backend/training/${trainingTaskId.value}/cancel`)

      if (response.success) {
        console.log('训练取消请求已提交:', response)

        // 更新任务状态显示
        updateTaskStatus('cancelling')

        notify('训练任务取消请求已提交', 'warning')

        // 状态更新由后端轮询控制，不需要前端手动设置
        console.log('训练取消请求已提交，等待后端处理完成...')
      } else {
        console.error('取消训练失败:', response)
        notify('取消训练失败: ' + (response.message || '未知错误'), 'negative')
      }
    } catch (apiError) {
      console.error('调用取消训练接口失败:', apiError)

      // 即使API调用失败，也要通知用户前端监控已停止
      notify('前端监控已停止，但后端训练可能仍在运行', 'warning')

      // 可以选择显示更详细的错误信息
      if (apiError.response?.data?.message) {
        notify('取消训练失败: ' + apiError.response.data.message, 'negative')
      }
    }

    console.log('停止训练操作完成')

  } catch (error) {
    console.error('停止训练过程中发生错误:', error)
    notify('停止训练失败: ' + (error.message || '未知错误'), 'negative')
  }
}

// 暂停训练
async function pauseTraining() {
  try {
    // 检查是否有训练任务ID
    if (!trainingTaskId.value) {
      notify('当前没有正在进行的训练任务', 'warning')
      return
    }

    // 检查任务状态，只有运行中的任务才能暂停
    if (taskStatus.value !== 'running') {
      notify(`任务状态为 ${statusMap[taskStatus.value]}，无法暂停`, 'warning')
      return
    }

    console.log('开始暂停训练，任务ID:', trainingTaskId.value)

    try {
      console.log('调用后端暂停训练接口...')
      const response = await api.post(`backend/training/${trainingTaskId.value}/pause`)

      if (response.success) {
        console.log('训练暂停请求已提交:', response)

        // 更新任务状态显示
        updateTaskStatus('pausing')

        notify('训练任务暂停请求已提交', 'warning')

        // 状态更新由后端轮询控制，不需要前端手动设置
        console.log('训练暂停请求已提交，等待后端处理完成...')
      } else {
        console.error('暂停训练失败:', response)
        notify('暂停训练失败: ' + (response.message || '未知错误'), 'negative')
      }
    } catch (apiError) {
      console.error('调用暂停训练接口失败:', apiError)

      if (apiError.response?.data?.message) {
        notify('暂停训练失败: ' + apiError.response.data.message, 'negative')
      } else {
        notify('暂停训练失败: ' + (apiError.message || '未知错误'), 'negative')
      }
    }

    console.log('暂停训练操作完成')

  } catch (error) {
    console.error('暂停训练过程中发生错误:', error)
    notify('暂停训练失败: ' + (error.message || '未知错误'), 'negative')
  }
}

// 继续训练
async function resumeTraining() {
  try {
    // 检查是否有训练任务ID
    if (!trainingTaskId.value) {
      notify('当前没有训练任务', 'warning')
      return
    }

    // 检查任务状态，只有暂停的任务才能继续
    if (taskStatus.value !== 'paused') {
      notify(`任务状态为 ${statusMap[taskStatus.value]}，无法继续训练`, 'warning')
      return
    }

    console.log('开始继续训练，任务ID:', trainingTaskId.value)

    try {
      console.log('调用后端继续训练接口...')
      const response = await api.post(`backend/training/${trainingTaskId.value}/resume`)

      if (response.success) {
        console.log('训练继续请求已提交:', response)

        // 更新任务状态显示
        updateTaskStatus('resuming')

        notify('训练任务继续请求已提交', 'info')

        // 状态更新由后端轮询控制，不需要前端手动设置
        console.log('训练继续请求已提交，等待后端处理完成...')
      } else {
        console.error('继续训练失败:', response)
        notify('继续训练失败: ' + (response.message || '未知错误'), 'negative')
      }
    } catch (apiError) {
      console.error('调用继续训练接口失败:', apiError)

      if (apiError.response?.data?.message) {
        notify('继续训练失败: ' + apiError.response.data.message, 'negative')
      } else {
        notify('继续训练失败: ' + (apiError.message || '未知错误'), 'negative')
      }
    }

    console.log('继续训练操作完成')

  } catch (error) {
    console.error('继续训练过程中发生错误:', error)
    notify('继续训练失败: ' + (error.message || '未知错误'), 'negative')
  }
}



// 切换标签页
function switchTab(tab) {
  activeTab.value = tab

  // 如果切换到日志页面，获取真实的训练日志
  if (tab === 'log') {
    if (trainingTaskId.value) {
      fetchTrainingLogs()
    }
    // 移除模拟数据，只显示真实日志或空状态
  }
}

// 获取训练日志
async function fetchTrainingLogs() {
  if (!trainingTaskId.value) {
    return
  }
  
  try {
    loadingLogs.value = true
    console.log('获取训练日志，任务ID:', trainingTaskId.value)
    
    // 清除现有日志
    trainingLogs.value = []
    
    // 获取token - 使用Quasar LocalStorage确保格式一致
    let token = quasarLocalStorage.getItem('token')

    // 如果Quasar LocalStorage没有token，尝试从原生localStorage获取
    if (!token) {
      token = localStorage.getItem('token') || sessionStorage.getItem('token')
    }

    // 处理token格式
    if (token) {
      // 确保token格式正确（移除可能的Bearer前缀，因为后端会重新添加）
      if (token.startsWith('Bearer ')) {
        token = token.substring(7).trim()
      }
    }
    
    // 使用固定的baseUrl
    const logStreamUrl = `http://127.0.0.1:8000/backend/training/${trainingTaskId.value}/log-stream`
    const urlWithAuth = token ? `${logStreamUrl}?token=${token}` : logStreamUrl
    console.log('日志流URL:', urlWithAuth)
    
    // 创建SSE连接
    const eventSource = new EventSource(urlWithAuth)

    // 设置通用消息监听器
    eventSource.onmessage = (event) => {
      try {
        console.log('收到SSE消息:', event.data)
        const logData = JSON.parse(event.data)

        // 处理日志数据
        const newLog = {
          timestamp: logData.timestamp || new Date().toISOString(),
          level: logData.level || 'INFO',
          message: logData.message || logData.text || logData
        }

        // 添加到日志列表
        trainingLogs.value.push(newLog)

        // 自动滚动到底部
        nextTick(() => {
          scrollToBottom()
        })
      } catch (error) {
        console.error('解析日志消息失败:', error, event.data)
      }
    }

    // 设置特定事件类型监听器
    eventSource.addEventListener('connected', (event) => {
      try {
        console.log('日志流连接已建立:', event.data)
        const data = JSON.parse(event.data)

        // 添加连接成功的日志
        const connectLog = {
          timestamp: new Date(data.timestamp * 1000).toISOString(),
          level: 'INFO',
          message: `日志流连接已建立 (任务ID: ${data.training_id})`
        }
        trainingLogs.value.push(connectLog)

        loadingLogs.value = false

        nextTick(() => {
          scrollToBottom()
        })
      } catch (error) {
        console.error('处理连接事件失败:', error)
      }
    })

    eventSource.addEventListener('logs', (event) => {
      try {
        console.log('收到日志更新:', event.data)
        const data = JSON.parse(event.data)

        if (data.logs && data.logs.trim()) {
          // 解析日志内容，按行分割
          const logLines = data.logs.split('\n').filter(line => line.trim())

          logLines.forEach(line => {
            const logEntry = {
              timestamp: new Date(data.timestamp * 1000).toISOString(),
              level: 'INFO',
              message: line.trim()
            }
            trainingLogs.value.push(logEntry)
          })

          // 自动滚动到底部
          nextTick(() => {
            scrollToBottom()
          })
        }
      } catch (error) {
        console.error('处理日志事件失败:', error)
      }
    })

    eventSource.addEventListener('final', (event) => {
      try {
        console.log('收到最终日志:', event.data)
        const data = JSON.parse(event.data)

        // 添加训练结束的日志
        const finalLog = {
          timestamp: new Date(data.timestamp * 1000).toISOString(),
          level: data.status === 'completed' ? 'SUCCESS' : 'WARNING',
          message: `训练已结束，状态: ${data.status}`
        }
        trainingLogs.value.push(finalLog)

        // 如果有最终日志内容，也添加进去
        if (data.logs && data.logs.trim()) {
          const logLines = data.logs.split('\n').filter(line => line.trim())
          logLines.forEach(line => {
            const logEntry = {
              timestamp: new Date(data.timestamp * 1000).toISOString(),
              level: 'INFO',
              message: line.trim()
            }
            trainingLogs.value.push(logEntry)
          })
        }

        // 关闭连接
        eventSource.close()
        loadingLogs.value = false

        nextTick(() => {
          scrollToBottom()
        })
      } catch (error) {
        console.error('处理最终日志事件失败:', error)
      }
    })

    eventSource.addEventListener('error', (event) => {
      try {
        console.error('收到错误事件:', event.data)
        const data = JSON.parse(event.data)

        const errorLog = {
          timestamp: new Date(data.timestamp * 1000).toISOString(),
          level: 'ERROR',
          message: `错误: ${data.error}`
        }
        trainingLogs.value.push(errorLog)

        nextTick(() => {
          scrollToBottom()
        })
      } catch (error) {
        console.error('处理错误事件失败:', error)
      }
    })

    eventSource.addEventListener('heartbeat', (event) => {
      try {
        console.log('收到心跳:', event.data)
        // 心跳事件不需要显示在日志中，只用于保持连接
      } catch (error) {
        console.error('处理心跳事件失败:', error)
      }
    })

    eventSource.onerror = (error) => {
      console.error('SSE连接错误:', error)

      // 关闭连接
      eventSource.close()

      // 如果没有日志，添加一条错误信息
      if (trainingLogs.value.length === 0) {
        const errorLog = {
          timestamp: new Date().toISOString(),
          level: 'ERROR',
          message: '无法连接到日志流，请检查网络连接或训练任务状态'
        }
        trainingLogs.value = [errorLog]
      }

      loadingLogs.value = false
    }
    
    // 存储EventSource实例以便在组件销毁时关闭
    logEventSource = eventSource
    
    // 如果5秒内没有收到任何日志，显示一些默认日志
    setTimeout(() => {
      if (trainingLogs.value.length === 0) {
        const defaultLogs = [
          { timestamp: new Date().toISOString(), level: 'INFO', message: '等待训练日志数据...' },
          { timestamp: new Date().toISOString(), level: 'INFO', message: '如果训练尚未开始，可能不会有日志输出' }
        ]
        trainingLogs.value = defaultLogs
        loadingLogs.value = false
      }
    }, 5000)
    
  } catch (error) {
    console.error('设置日志流失败:', error)
    
    // 错误时生成一条错误日志
    const errorLog = {
      timestamp: new Date().toISOString(),
      level: 'ERROR',
      message: `获取日志失败: ${error.message || '网络错误'}`
    }
    trainingLogs.value = [errorLog]
    loadingLogs.value = false
  }
}

// 滚动到日志底部
function scrollToBottom() {
  if (logContainer.value) {
    logContainer.value.scrollTop = logContainer.value.scrollHeight
  }
}

// 格式化日志时间
function formatLogTime(timestamp) {
  try {
    const date = new Date(timestamp)
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    })
  } catch (error) {
    return timestamp
  }
}

// 获取日志级别对应的CSS类
function getLogLevelClass(level) {
  const levelClassMap = {
    'ERROR': 'log-error',
    'WARN': 'log-warning',
    'WARNING': 'log-warning',
    'INFO': 'log-info',
    'DEBUG': 'log-debug'
  }
  return levelClassMap[level?.toUpperCase()] || 'log-info'
}

// 生命周期钩子
onMounted(() => {
  // 使用nextTick确保DOM完全渲染后再初始化图表
  nextTick(() => {
    // 初始化图表
    initLossChart1()
    initResourceChart()

    // 页面进入时显示初始的5个示例数据
    resetLossChartToDefault()
  })

  // 监听窗口大小变化
  window.addEventListener('resize', resizeCharts)
})

// 组件销毁前清理
onBeforeUnmount(() => {
  // 销毁图表实例
  if (lossChart1) {
    lossChart1.dispose()
    lossChart1 = null
  }
  if (resourceChart) {
    resourceChart.dispose()
    resourceChart = null
  }
  
  // 移除事件监听
  window.removeEventListener('resize', resizeCharts)
  
  // 清除状态轮询定时器
  if (statusPollingInterval) {
    clearInterval(statusPollingInterval)
  }
  
  // 关闭日志SSE连接
  if (logEventSource) {
    logEventSource.close()
    logEventSource = null
  }
  
  // 关闭指标SSE连接
  if (window.metricsEventSource) {
    window.metricsEventSource.close()
    window.metricsEventSource = null
  }
  
  if (metricsEventSource) {
    metricsEventSource.close()
    metricsEventSource = null
  }
})
</script>

<style lang="scss" scoped>
.content {
  display: flex;
  flex-direction: column;

  .top {
    display: flex;
    margin-bottom: .125rem;
    height: 9.875rem;

      .left {
       width: 40%;
       height: inherit;
       border: .025rem solid #707070;
       background-color: #181a24;
       padding: .3375rem;
       display: flex;
       flex-direction: column;
       margin-right: .125rem;
     }

     .right {
       width: 60%;
       height: inherit;
       border: .025rem solid #707070;
       background-color: #181a24;
       padding: .3375rem;
       display: flex;
       flex-direction: column;
     }
  }

  // 信息区域
  .info-section {
    height: 100%;
    display: flex;
    flex-direction: column;

         .section-tabs {
       display: flex;
       margin-bottom: .25rem;

       .tab-btn {
         padding: .125rem .25rem !important;
         background-color: #2a2d3a !important;
         color: #999 !important;
         font-size: .175rem !important;
         border-radius: 0 !important;
        //  border-right: .025rem solid #707070;
         min-height: auto !important;

         &.tab-active {
           background-color: #0b3e50 !important;
           color: white !important;
         }

         &:before {
           display: none !important;
         }
       }
     }

     .table-header {
       display: flex;
       background: linear-gradient(to right, #216f6e 30%, #003366 70%);
       margin-bottom: .125rem;

       .header-cell {
         flex: 1;
         padding: .125rem .25rem;
         color: white;
         font-size: .175rem;
         font-weight: bold;
       }
     }

        .info-table {
      flex: 1;
      overflow-y: auto;

       .table-row {
         display: flex;
         border-bottom: .0125rem solid #333;

         .cell {
           flex: 1;
           padding: .125rem .25rem;
           color: #999;
           font-size: .175rem;
        //    border-right: .0125rem solid #333;

           &:first-child {
             color: white;
           }

           &.blue-text {
             color: #4ab4ff;
           }

           &.blue-link {
             color: #4ab4ff;
             cursor: pointer;
             text-decoration: underline;
           }

           &.success-status {
             color: #4caf50;
             display: flex;
             align-items: center;

             .status-icon {
               width: .2rem;
               height: .2rem;
               margin-right: .0625rem;
             }
           }
         }
       }
     }
  }

  // 图表区域
  .charts-section {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: .25rem;

    .chart-container {
      flex: 1;
      display: flex;
      flex-direction: column;

      .chart-header {
        display: flex;
        align-items: center;
        margin-bottom: .125rem;

        .arrow-icon {
          width: .2rem;
          height: .2rem;
          margin-right: .125rem;
        }

        .chart-title {
          color: #4ab4ff;
          font-size: .2rem;
        }
      }

      .chart-content {
        flex: 1;
        border: .025rem solid #333;
        border-radius: .0625rem;
        padding: .125rem;

        .chart {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  // 底部按钮
  .bottom {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    margin-top:.25rem;

    .next {
      display: flex;
      justify-content: space-between;
      width: 100%;
      padding: 0 .25rem;
      gap: .25rem;

      .prevBtn {
        margin-right: auto;
      }

      .button-group {
        display: flex;
        gap: .25rem;
        margin-left: auto;
      }

      .nextBtn {
        margin-left: auto;
      }
    }
  }
}

// 通用样式
.labelColor {
  color: #4ab4ff;
}

.training-status {
  color: #ff9800 !important;
  display: flex;
  align-items: center;
}

.train-btn {
  margin-left: .25rem;
  font-size: .175rem;
  padding: .05rem .125rem;
  height: auto;
  min-height: auto;
}

// 训练控制按钮样式
.training-controls-row {
  padding: .15rem .2rem;
  flex:1;
  .training-controls {
    display: flex;
    gap: .125rem;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;

    .control-btn {
      font-size: .15rem !important;
      padding: .05rem .1rem !important;
      height: auto !important;
      min-height: auto !important;
      border-radius: .05rem !important;

      &:before {
        display: none !important;
      }
    }
  }
}

// 训练控制按钮在表格单元格中的样式
.training-controls-cell {
  .training-controls {
    display: flex;
    gap: .08rem;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-start;

    .control-btn {
      font-size: .12rem !important;
      padding: .04rem .08rem !important;
      height: auto !important;
      min-height: auto !important;
      border-radius: .04rem !important;

      &:before {
        display: none !important;
      }
    }
  }
}

// 任务状态样式
.pending-status {
  color: #ff9800 !important;
  display: flex;
  align-items: center;
}

.running-status {
  color: #2196F3 !important;
  display: flex;
  align-items: center;
}

.success-status {
  color: #4caf50 !important;
  display: flex;
  align-items: center;
}

.failed-status {
  color: #f44336 !important;
  display: flex;
  align-items: center;
}

.cancelled-status {
  color: #9e9e9e !important;
  display: flex;
  align-items: center;
}

.cancelling-status {
  color: #ff9800 !important;
  display: flex;
  align-items: center;
}

.paused-status {
  color: #ffc107 !important;
  display: flex;
  align-items: center;
}

.pausing-status {
  color: #ff9800 !important;
  display: flex;
  align-items: center;
}

.resuming-status {
  color: #2196f3 !important;
  display: flex;
  align-items: center;
}

.not-started-status {
  color: #9e9e9e !important;
  display: flex;
  align-items: center;
}

.unknown-status {
  color: #795548 !important;
  display: flex;
  align-items: center;
}

// 日志相关样式
.log-section {
  height: 90%;
  display: flex;
  flex-direction: column;
  
  .log-content {
    flex: 1;
    background-color: #1a1a1a;
    border: .0125rem solid #333;
    border-radius: .0625rem;
    padding: .25rem;
    overflow-y: auto;
    overflow-x: hidden;
    max-height: 25rem;
    
    .log-loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #4ab4ff;
      font-size: .2rem;
      gap: .25rem;
    }
    
    .log-empty {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #999;
      font-size: .2rem;
      gap: .125rem;
      
      .log-hint {
        font-size: .175rem;
        color: #666;
      }
    }
    
    .log-list {
      .log-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: .125rem;
        padding: .0625rem .125rem;
        border-radius: .0625rem;
        font-family: 'Courier New', monospace;
        font-size: .15rem;
        line-height: 1.4;
        
        &:hover {
          background-color: rgba(255, 255, 255, 0.05);
        }
        
        .log-timestamp {
          color: #666;
          margin-right: .25rem;
          white-space: nowrap;
          flex-shrink: 0;
        }
        
        .log-level {
          margin-right: .25rem;
          white-space: nowrap;
          flex-shrink: 0;
          font-weight: bold;
        }
        
        .log-message {
          flex: 1;
          word-wrap: break-word;
          white-space: pre-wrap;
        }
        
        &.log-info {
          .log-level {
            color: #4ab4ff;
          }
          .log-message {
            color: #ffffff;
          }
        }
        
        &.log-warning {
          .log-level {
            color: #ff9800;
          }
          .log-message {
            color: #ffcc80;
          }
        }
        
        &.log-error {
          .log-level {
            color: #f44336;
          }
          .log-message {
            color: #ffcdd2;
          }
        }
        
        &.log-debug {
          .log-level {
            color: #9e9e9e;
          }
          .log-message {
            color: #e0e0e0;
          }
        }
      }
    }
  }
}


</style>