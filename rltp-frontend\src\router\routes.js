const routes = [
  {
    path: '/login',
    meta: { notRequireAuth: true },
    component: () => import('src/pages/user/LoginPage.vue'),
  },
  // {
  //   path: '/user',
  //   component: () => import('layouts/MainLayout.vue'),
  //   children: [
  //     // {
  //     //   path: '',
  //     //   component: () => import('pages/user/IndexPage.vue'),
  //     // },
  //     {
  //       path: 'register',
  //       component: () => import('pages/user/RegisterPage.vue'),
  //     },
  //   ],
  // },
  {
    path: '/user/register',
    meta: { notRequireAuth: true },
    component: () => import('src/pages/user/RegisterPage.vue'),
  },
  {
    path: '/user/forgot-password',
    meta: { notRequireAuth: true },
    component: () => import('src/pages/user/ForgotPasswordPage.vue'),
  },
  {
    path: '/',
    redirect: '/intelligent-algorithm',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: 'intelligent-algorithm',
        component: () => import('pages/algorithm/IntelligentAlgorithmPage.vue'),
      },
      {
        path: 'overview',
        component: () => import('pages/overview/IndexPage.vue'),
      },
      {
        path: 'emulation',
        component: () => import('pages/emulation/IndexPage.vue'),
      },
      {
        path: 'storage',
        component: () => import('pages/storage/IndexPage.vue'),
      },
    ],
  },

  {
    path: '/training',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        component: () => import('pages/training/IndexPage.vue'),
      },
      {
        path: 'detail/:id(\\d+)',
        component: () => import('pages/training/DetailPage.vue'),
        props: true,
      },
    ],
  },

  {
    path: '/algorithm',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        component: () => import('pages/algorithm/IndexPage.vue'),
      },
      {
        path: 'create',
        component: () => import('pages/algorithm/DetailPage.vue'),
      },
      {
        path: 'detail/:id(\\d+)',
        component: () => import('pages/algorithm/DetailPage.vue'),
        props: true,
      },
    ],
  },

  {
    path: '/model',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        component: () => import('pages/model/IndexPage.vue'),
      },
      {
        path: 'detail/:id(\\d+)',
        component: () => import('pages/model/DetailPage.vue'),
        props: true,
      },
      {
        path: 'video/:id(\\d+)',
        component: () => import('pages/model/VideoPage.vue'),
        props: true,
      },
    ],
  },

  {
    path: '/ai-model',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      // 深度学习
      {
        path: 'intelligent',
        component: () => import('src/pages/ai-model/IntelligentModelPage.vue'),
      },
      {
        path: 'startTrain',
        component: () => import('src/pages/ai-model/StepForm.vue'),
      },
      // 强化学习👇
      {
        path: 'reinforcementStudy',
        component: () => import('src/pages/reinforcementLearing/HomePage.vue'),
      },
      // 大模型👇
      {
        path: 'largeHome',
        component: () => import('src/pages/large-model/HomePage.vue'),
      },
      {
        path: 'largeModelForm',
        component: () => import('src/pages/large-model/LargeForm.vue'),
      },
      {
        path: 'reinforcement',
        component: () => import('src/pages/ai-model/ReinforcementLearningPage.vue'),
      },
      {
        path: 'deep-learning',
        component: () => import('src/pages/ai-model/DeepLearningPage.vue'),
      },

      // 训练工作流路由
      {
        path: 'training-workflow',
        component: () => import('src/pages/training-workflow/WorkflowPage.vue'),
      },
      {
        path: 'training-workflow/:id/:step',
        component: () => import('src/pages/training-workflow/WorkflowStepPage.vue'),
        props: true
      },

    ],
  },

  {
    path: '/ai-interaction',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        component: () => import('pages/ai-interaction/IndexPage.vue'),
      },
    ],
  },

  {
    path: '/system',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        component: () => import('src/pages/system/IndexPage.vue'),
      },
      {
        path: 'users',
        component: () => import('src/pages/system/UserManagementPage.vue'),
      },
      {
        path: 'simulation',
        component: () => import('src/pages/system/SimulationManagementPage.vue'),
      },
      {
        path: 'model',
        component: () => import('src/pages/system/ModelManagementPage.vue'),
        name: 'model-management',
        meta: { title: '模型管理' },
      },
      {
        path: 'dataset',
        component: () => import('pages/system/DatasetManagementPage.vue'),
        name: 'dataset-management',
        meta: { title: '数据集管理' },
      },
      {
        path: 'config',
        component: () => import('pages/system/ConfigEnv.vue'),
        name: 'config-management',
        meta: { title: '配置环境管理' },
      },
      {
        path: 'server',
        component: () => import('pages/system/ServerManagement.vue'),
        name: 'server-management',
        meta: { title: '服务器运维管理' },
      },
       {
        path: 'test',
        component: () => import('pages/system/DataProcessOne.vue'),
        name: 'test-management',
        meta: { title: 'xx' },
      },
    ],
  },
  {
    path: '/:catchAll(.*)*',
    component: () => import('pages/ErrorNotFound.vue'),
  },
]

export default routes
