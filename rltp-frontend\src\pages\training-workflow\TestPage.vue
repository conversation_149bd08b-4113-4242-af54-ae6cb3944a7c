<template>
  <q-page class="q-pa-md">
    <div class="text-h4 q-mb-lg">训练工作流测试页面</div>
    
    <div class="row q-gutter-md">
      <!-- 左侧测试区域 -->
      <div class="col-8">
        <q-card>
          <q-card-section>
            <div class="text-h6 q-mb-md">测试步骤保存功能</div>
            
            <!-- 模拟步骤1 -->
            <q-card flat bordered class="q-mb-md">
              <q-card-section>
                <div class="text-subtitle1 q-mb-md">步骤1: 数据配置</div>
                <q-input 
                  v-model="step1Data.dataset_name" 
                  label="数据集名称" 
                  class="q-mb-md"
                />
                <q-input 
                  v-model="step1Data.dataset_path" 
                  label="数据集路径" 
                  class="q-mb-md"
                />
                <q-btn 
                  color="primary" 
                  label="保存步骤1并下一步" 
                  @click="saveStep1"
                  :loading="saving"
                />
              </q-card-section>
            </q-card>

            <!-- 模拟步骤2 -->
            <q-card flat bordered class="q-mb-md">
              <q-card-section>
                <div class="text-subtitle1 q-mb-md">步骤2: 模型配置</div>
                <q-select 
                  v-model="step2Data.model_type" 
                  :options="['YoloV8', 'YoloV5']"
                  label="模型类型" 
                  class="q-mb-md"
                />
                <q-input 
                  v-model="step2Data.epochs" 
                  label="训练轮数" 
                  type="number"
                  class="q-mb-md"
                />
                <q-btn 
                  color="primary" 
                  label="保存步骤2并下一步" 
                  @click="saveStep2"
                  :loading="saving"
                />
              </q-card-section>
            </q-card>

            <!-- 模拟步骤3 -->
            <q-card flat bordered class="q-mb-md">
              <q-card-section>
                <div class="text-subtitle1 q-mb-md">步骤3: 训练配置</div>
                <q-input 
                  v-model="step3Data.batch_size" 
                  label="批次大小" 
                  type="number"
                  class="q-mb-md"
                />
                <q-input 
                  v-model="step3Data.learning_rate" 
                  label="学习率" 
                  type="number"
                  step="0.001"
                  class="q-mb-md"
                />
                <q-btn 
                  color="primary" 
                  label="启动训练" 
                  @click="startTraining"
                  :loading="training"
                />
              </q-card-section>
            </q-card>

            <!-- 测试日志 -->
            <q-card flat bordered>
              <q-card-section>
                <div class="text-subtitle1 q-mb-md">测试日志</div>
                <div class="test-log">
                  <div v-for="(log, index) in logs" :key="index" class="log-item">
                    <span class="log-time">{{ log.time }}</span>
                    <span class="log-message">{{ log.message }}</span>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </q-card-section>
        </q-card>
      </div>

      <!-- 右侧任务列表 -->
      <div class="col-4">
        <TrainingTaskPanel 
          ref="taskPanel"
          @task-selected="handleTaskSelected" 
        />
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { ref } from 'vue'
import { useQuasar } from 'quasar'
import { api } from 'boot/axios'
import TrainingTaskPanel from './components/TrainingTaskPanel.vue'

const $q = useQuasar()

// 响应式数据
const saving = ref(false)
const training = ref(false)
const taskPanel = ref(null)
const logs = ref([])

const step1Data = ref({
  dataset_name: '测试数据集',
  dataset_path: '/data/test_dataset'
})

const step2Data = ref({
  model_type: 'YoloV8',
  epochs: 100
})

const step3Data = ref({
  batch_size: 16,
  learning_rate: 0.001
})

// 当前测试工作流ID
const testWorkflowId = ref(null)

// 方法
const addLog = (message) => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message: message
  })
  // 只保留最近20条日志
  if (logs.value.length > 20) {
    logs.value = logs.value.slice(0, 20)
  }
}

const createTestWorkflow = async () => {
  try {
    addLog('创建测试工作流...')
    
    const workflowData = {
      name: `测试工作流-${new Date().toLocaleString()}`,
      description: '用于测试步骤保存功能的工作流',
      task_type: 'object_detection_yolov8'
    }
    
    const response = await api.post('backend/workflows/', workflowData)
    testWorkflowId.value = response.id
    
    addLog(`测试工作流创建成功，ID: ${response.id}`)
    return response.id
    
  } catch (error) {
    addLog(`创建工作流失败: ${error.message}`)
    throw error
  }
}

const saveStepData = async (stepName, data) => {
  try {
    if (!testWorkflowId.value) {
      await createTestWorkflow()
    }
    
    addLog(`保存${stepName}数据...`)
    
    const response = await api.post(`backend/workflows/${testWorkflowId.value}/steps/${stepName}/`, {
      data: data
    })
    
    addLog(`${stepName}数据保存成功`)
    
    // 刷新右侧任务列表
    if (taskPanel.value && taskPanel.value.loadTasks) {
      await taskPanel.value.loadTasks()
      addLog('右侧任务列表已刷新')
    }
    
    return response
    
  } catch (error) {
    addLog(`保存${stepName}失败: ${error.message}`)
    throw error
  }
}

const saveStep1 = async () => {
  saving.value = true
  try {
    await saveStepData('step1_data', step1Data.value)
    $q.notify({
      type: 'positive',
      message: '步骤1数据已保存'
    })
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: '保存失败'
    })
  } finally {
    saving.value = false
  }
}

const saveStep2 = async () => {
  saving.value = true
  try {
    await saveStepData('step2_model', step2Data.value)
    $q.notify({
      type: 'positive',
      message: '步骤2数据已保存'
    })
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: '保存失败'
    })
  } finally {
    saving.value = false
  }
}

const startTraining = async () => {
  training.value = true
  try {
    await saveStepData('step3_training', step3Data.value)
    
    addLog('启动训练任务...')
    
    // 模拟启动训练
    setTimeout(() => {
      addLog('训练任务已启动')
      if (taskPanel.value && taskPanel.value.loadTasks) {
        taskPanel.value.loadTasks()
        addLog('任务列表已更新，显示训练中状态')
      }
    }, 1000)
    
    $q.notify({
      type: 'positive',
      message: '训练已启动'
    })
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: '启动训练失败'
    })
  } finally {
    training.value = false
  }
}

const handleTaskSelected = (task) => {
  addLog(`选择了任务: ${task.name}`)
}

// 初始化
addLog('测试页面已加载')
</script>

<style scoped>
.test-log {
  max-height: 300px;
  overflow-y: auto;
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
}

.log-item {
  display: flex;
  margin-bottom: 4px;
  font-size: 12px;
}

.log-time {
  color: #666;
  margin-right: 8px;
  min-width: 80px;
}

.log-message {
  color: #333;
}
</style>
