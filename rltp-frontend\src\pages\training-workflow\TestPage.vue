<template>
  <q-page class="test-page q-pa-md">
    <div class="text-h4 q-mb-md">训练工作流测试页面</div>
    
    <div class="row q-gutter-md">
      <!-- 测试按钮 -->
      <div class="col-12">
        <q-card>
          <q-card-section>
            <div class="text-h6">快速测试</div>
            <div class="q-mt-md">
              <q-btn
                color="primary"
                label="访问工作流概览"
                @click="goToWorkflowOverview"
                class="q-mr-md"
              />
              <q-btn
                color="secondary"
                label="创建测试工作流"
                @click="createTestWorkflow"
                class="q-mr-md"
              />
              <q-btn
                color="positive"
                label="访问步骤页面"
                @click="goToStepPage"
                :disable="!testWorkflowId"
              />
            </div>
          </q-card-section>
        </q-card>
      </div>

      <!-- 事件日志 -->
      <div class="col-12">
        <q-card>
          <q-card-section>
            <div class="text-h6 q-mb-md">事件日志</div>
            <q-btn
              flat
              icon="clear"
              label="清空日志"
              @click="clearLogs"
              class="q-mb-md"
            />
            
            <div class="log-container">
              <div
                v-for="(log, index) in eventLogs"
                :key="index"
                class="log-item"
                :class="`log-${log.type}`"
              >
                <span class="log-time">{{ log.time }}</span>
                <span class="log-message">{{ log.message }}</span>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <!-- 数据状态 -->
      <div class="col-12">
        <q-card>
          <q-card-section>
            <div class="text-h6 q-mb-md">当前数据状态</div>
            
            <div class="data-display">
              <div class="data-section">
                <div class="data-title">测试工作流ID:</div>
                <div class="data-value">{{ testWorkflowId || '未创建' }}</div>
              </div>
              
              <div class="data-section">
                <div class="data-title">本地存储数据:</div>
                <pre class="data-value">{{ JSON.stringify(localStorageData, null, 2) }}</pre>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useQuasar } from 'quasar'

const router = useRouter()
const $q = useQuasar()

// 响应式数据
const eventLogs = ref([])
const testWorkflowId = ref(null)
const localStorageData = ref({})

// 方法
const addLog = (message, type = 'info') => {
  eventLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message,
    type
  })
  
  // 限制日志数量
  if (eventLogs.value.length > 50) {
    eventLogs.value = eventLogs.value.slice(0, 50)
  }
}

const clearLogs = () => {
  eventLogs.value = []
  addLog('日志已清空', 'info')
}

const goToWorkflowOverview = () => {
  addLog('跳转到工作流概览页面', 'info')
  router.push('/ai-model/training-workflow')
}

const createTestWorkflow = () => {
  addLog('创建测试工作流', 'info')
  
  // 创建一个测试工作流ID
  const newWorkflowId = 'test-' + Date.now()
  testWorkflowId.value = newWorkflowId
  
  // 保存到本地存储
  const workflowData = {
    id: newWorkflowId,
    name: '测试工作流',
    task_type: 'object_detection_yolov8',
    current_step: 'draft',
    status: 'draft',
    step1_data_config: {},
    step2_model_config: {},
    step3_training_config: {},
    step4_evaluation_config: {},
    step5_deployment_config: {},
    created_at: new Date().toISOString()
  }
  
  localStorage.setItem(`workflow_${newWorkflowId}`, JSON.stringify(workflowData))
  updateLocalStorageData()
  
  addLog(`测试工作流已创建: ${newWorkflowId}`, 'success')
  
  $q.notify({
    type: 'positive',
    message: '测试工作流已创建'
  })
}

const goToStepPage = () => {
  if (!testWorkflowId.value) {
    addLog('请先创建测试工作流', 'error')
    return
  }
  
  addLog(`跳转到工作流步骤页面: ${testWorkflowId.value}`, 'info')
  router.push(`/ai-model/training-workflow/${testWorkflowId.value}/step1`)
}

const updateLocalStorageData = () => {
  const data = {}
  
  // 获取所有工作流相关的本地存储数据
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i)
    if (key && key.startsWith('workflow_')) {
      try {
        data[key] = JSON.parse(localStorage.getItem(key))
      } catch (error) {
        data[key] = localStorage.getItem(key)
      }
    }
  }
  
  localStorageData.value = data
}

// 监听本地存储变化
const handleStorageChange = (event) => {
  if (event.key && event.key.startsWith('workflow_')) {
    addLog(`本地存储更新: ${event.key}`, 'info')
    updateLocalStorageData()
  }
}

// 生命周期
onMounted(() => {
  addLog('测试页面已加载', 'info')
  updateLocalStorageData()
  
  // 监听本地存储变化
  window.addEventListener('storage', handleStorageChange)
  
  // 检查是否有现有的测试工作流
  const existingWorkflows = Object.keys(localStorageData.value).filter(key => 
    key.startsWith('workflow_test-')
  )
  
  if (existingWorkflows.length > 0) {
    const latestWorkflow = existingWorkflows[existingWorkflows.length - 1]
    testWorkflowId.value = latestWorkflow.replace('workflow_', '')
    addLog(`发现现有测试工作流: ${testWorkflowId.value}`, 'info')
  }
})

// 清理
const cleanup = () => {
  window.removeEventListener('storage', handleStorageChange)
}

// 组件卸载时清理
import { onUnmounted } from 'vue'
onUnmounted(cleanup)
</script>

<style scoped>
.test-page {
  max-width: 1200px;
  margin: 0 auto;
}

.log-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px;
  background-color: #f9f9f9;
}

.log-item {
  display: flex;
  margin-bottom: 4px;
  font-family: monospace;
  font-size: 12px;
}

.log-time {
  color: #666;
  margin-right: 8px;
  min-width: 80px;
}

.log-message {
  flex: 1;
}

.log-info {
  color: #333;
}

.log-success {
  color: #4caf50;
}

.log-error {
  color: #f44336;
}

.log-warning {
  color: #ff9800;
}

.data-display {
  font-family: monospace;
}

.data-section {
  margin-bottom: 16px;
}

.data-title {
  font-weight: bold;
  color: #666;
  margin-bottom: 4px;
}

.data-value {
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  white-space: pre-wrap;
  font-size: 12px;
}
</style>
