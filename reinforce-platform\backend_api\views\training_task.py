from rest_framework import generics, permissions, status
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from django.shortcuts import get_object_or_404
from django.utils import timezone
from ..models.training_task import WorkflowTrainingTask
from ..serializers.training_task import TrainingTaskSerializer
import logging

logger = logging.getLogger(__name__)


class TrainingTaskListCreateView(generics.ListCreateAPIView):
    """训练任务列表和创建"""
    serializer_class = TrainingTaskSerializer
    permission_classes = [permissions.AllowAny]
    
    def get_queryset(self):
        return WorkflowTrainingTask.objects.all().order_by('-created_at')
    
    def perform_create(self, serializer):
        try:
            # 如果状态是training，设置开始时间
            if serializer.validated_data.get('status') == 'training':
                serializer.save(started_at=timezone.now())
            else:
                serializer.save()
        except Exception as e:
            logger.error(f"创建训练任务失败: {e}")
            logger.error(f"验证数据: {serializer.validated_data}")
            raise

    def create(self, request, *args, **kwargs):
        logger.info(f"接收到创建训练任务请求: {request.data}")
        try:
            return super().create(request, *args, **kwargs)
        except Exception as e:
            logger.error(f"创建训练任务异常: {e}")
            logger.error(f"请求数据: {request.data}")
            raise


class TrainingTaskDetailView(generics.RetrieveUpdateDestroyAPIView):
    """训练任务详情、更新和删除"""
    serializer_class = TrainingTaskSerializer
    permission_classes = [permissions.AllowAny]
    
    def get_queryset(self):
        return WorkflowTrainingTask.objects.all()
    
    def perform_update(self, serializer):
        instance = self.get_object()
        
        # 如果状态从非training变为training，设置开始时间
        if (instance.status != 'training' and 
            serializer.validated_data.get('status') == 'training' and
            not instance.started_at):
            serializer.save(started_at=timezone.now())
        
        # 如果状态变为completed，设置完成时间
        elif (instance.status != 'completed' and 
              serializer.validated_data.get('status') == 'completed'):
            serializer.save(completed_at=timezone.now())
        
        else:
            serializer.save()


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def training_task_stats(request):
    """获取训练任务统计信息"""
    total_tasks = WorkflowTrainingTask.objects.count()
    running_tasks = WorkflowTrainingTask.objects.filter(status='training').count()
    completed_tasks = WorkflowTrainingTask.objects.filter(status='completed').count()
    failed_tasks = WorkflowTrainingTask.objects.filter(status='failed').count()
    
    return Response({
        'total_tasks': total_tasks,
        'running_tasks': running_tasks,
        'completed_tasks': completed_tasks,
        'failed_tasks': failed_tasks,
    })


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def start_training_task(request, pk):
    """启动训练任务"""
    task = get_object_or_404(WorkflowTrainingTask, pk=pk)
    
    if task.status != 'configuring':
        return Response(
            {'error': '只有配置中的任务才能启动'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    task.status = 'training'
    task.started_at = timezone.now()
    task.save()
    
    return Response({
        'message': '训练任务已启动',
        'task_id': task.id,
        'status': task.status
    })


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def stop_training_task(request, pk):
    """停止训练任务"""
    task = get_object_or_404(WorkflowTrainingTask, pk=pk)
    
    if task.status not in ['training', 'evaluating']:
        return Response(
            {'error': '只有运行中的任务才能停止'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    task.status = 'cancelled'
    task.completed_at = timezone.now()
    task.save()
    
    return Response({
        'message': '训练任务已停止',
        'task_id': task.id,
        'status': task.status
    })


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def training_task_logs(request, pk):
    """获取训练任务日志"""
    task = get_object_or_404(WorkflowTrainingTask, pk=pk)
    
    # 这里可以返回实际的日志内容
    # 目前返回模拟数据
    logs = [
        f"[{timezone.now().strftime('%Y-%m-%d %H:%M:%S')}] 任务 {task.name} 开始执行",
        f"[{timezone.now().strftime('%Y-%m-%d %H:%M:%S')}] 当前步骤: {task.get_step_display()}",
        f"[{timezone.now().strftime('%Y-%m-%d %H:%M:%S')}] 进度: {task.progress}%",
        f"[{timezone.now().strftime('%Y-%m-%d %H:%M:%S')}] 状态: {task.status_display}",
    ]
    
    return Response({
        'task_id': task.id,
        'logs': logs
    })
