# Generated by Django 4.2.7 on 2025-08-15 11:24

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('backend_api', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='TrainingWorkflow',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='任务名称')),
                ('description', models.TextField(blank=True, verbose_name='任务描述')),
                ('task_type', models.CharField(choices=[('object_detection_yolov8', '目标识别-YoloV8'), ('object_detection_yolov5', '目标识别-YoloV5'), ('image_classification', '图像分类'), ('semantic_segmentation', '语义分割'), ('instance_segmentation', '实例分割')], max_length=50, verbose_name='任务类型')),
                ('current_step', models.CharField(choices=[('draft', '草稿'), ('step1_data', '步骤1-数据配置'), ('step2_model', '步骤2-模型配置'), ('step3_training', '步骤3-训练配置'), ('step4_evaluation', '步骤4-结果评估'), ('completed', '已完成'), ('failed', '失败')], default='draft', max_length=20, verbose_name='当前步骤')),
                ('status', models.CharField(choices=[('draft', '草稿'), ('step1_data', '步骤1-数据配置'), ('step2_model', '步骤2-模型配置'), ('step3_training', '步骤3-训练配置'), ('step4_evaluation', '步骤4-结果评估'), ('completed', '已完成'), ('failed', '失败')], default='draft', max_length=20, verbose_name='任务状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('started_at', models.DateTimeField(blank=True, null=True, verbose_name='开始训练时间')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='完成时间')),
                ('step1_data_config', models.JSONField(blank=True, default=dict, verbose_name='步骤1数据配置')),
                ('step2_model_config', models.JSONField(blank=True, default=dict, verbose_name='步骤2模型配置')),
                ('step3_training_config', models.JSONField(blank=True, default=dict, verbose_name='步骤3训练配置')),
                ('step4_evaluation_config', models.JSONField(blank=True, default=dict, verbose_name='步骤4评估配置')),
                ('training_task_id', models.IntegerField(blank=True, null=True, verbose_name='关联的训练任务ID')),
                ('model_path', models.CharField(blank=True, max_length=500, verbose_name='训练完成的模型路径')),
                ('best_metrics', models.JSONField(blank=True, default=dict, verbose_name='最佳训练指标')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
            ],
            options={
                'verbose_name': '训练工作流',
                'verbose_name_plural': '训练工作流',
                'db_table': 'training_workflow',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ModelEvaluation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('overall_accuracy', models.FloatField(blank=True, null=True, verbose_name='总体准确率')),
                ('class_accuracies', models.JSONField(blank=True, default=dict, verbose_name='各类别准确率')),
                ('confusion_matrix', models.JSONField(blank=True, default=list, verbose_name='混淆矩阵')),
                ('test_loss', models.FloatField(blank=True, null=True, verbose_name='测试损失')),
                ('test_samples_count', models.IntegerField(blank=True, null=True, verbose_name='测试样本数量')),
                ('evaluation_report', models.JSONField(blank=True, default=dict, verbose_name='详细评估报告')),
                ('evaluation_images', models.JSONField(blank=True, default=list, verbose_name='评估结果图片')),
                ('evaluated_at', models.DateTimeField(auto_now_add=True, verbose_name='评估时间')),
                ('workflow', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='evaluation', to='backend_api.trainingworkflow')),
            ],
            options={
                'verbose_name': '模型评估',
                'verbose_name_plural': '模型评估',
                'db_table': 'model_evaluation',
            },
        ),
        migrations.CreateModel(
            name='InferenceModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('model_name', models.CharField(max_length=200, verbose_name='模型名称')),
                ('model_version', models.CharField(default='v1.0', max_length=50, verbose_name='模型版本')),
                ('model_path', models.CharField(max_length=500, verbose_name='模型文件路径')),
                ('model_size', models.BigIntegerField(blank=True, null=True, verbose_name='模型文件大小(字节)')),
                ('status', models.CharField(choices=[('training', '训练中'), ('ready', '就绪'), ('deployed', '已部署'), ('archived', '已归档')], default='training', max_length=20, verbose_name='模型状态')),
                ('inference_time', models.FloatField(blank=True, null=True, verbose_name='推理时间(ms)')),
                ('memory_usage', models.FloatField(blank=True, null=True, verbose_name='内存使用(MB)')),
                ('deployment_config', models.JSONField(blank=True, default=dict, verbose_name='部署配置')),
                ('api_endpoint', models.URLField(blank=True, verbose_name='API端点')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('deployed_at', models.DateTimeField(blank=True, null=True, verbose_name='部署时间')),
                ('workflow', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='inference_model', to='backend_api.trainingworkflow')),
            ],
            options={
                'verbose_name': '推理模型',
                'verbose_name_plural': '推理模型',
                'db_table': 'inference_model',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='TrainingMetricsRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('training_task_id', models.IntegerField(verbose_name='训练任务ID')),
                ('epoch', models.IntegerField(verbose_name='训练轮次')),
                ('train_loss', models.FloatField(blank=True, null=True, verbose_name='训练损失')),
                ('val_loss', models.FloatField(blank=True, null=True, verbose_name='验证损失')),
                ('train_accuracy', models.FloatField(blank=True, null=True, verbose_name='训练准确率')),
                ('val_accuracy', models.FloatField(blank=True, null=True, verbose_name='验证准确率')),
                ('precision', models.FloatField(blank=True, null=True, verbose_name='精确率')),
                ('recall', models.FloatField(blank=True, null=True, verbose_name='召回率')),
                ('map50', models.FloatField(blank=True, null=True, verbose_name='mAP@0.5')),
                ('map50_95', models.FloatField(blank=True, null=True, verbose_name='mAP@0.5:0.95')),
                ('learning_rate', models.FloatField(blank=True, null=True, verbose_name='学习率')),
                ('gpu_memory', models.FloatField(blank=True, null=True, verbose_name='GPU内存使用')),
                ('training_time', models.FloatField(blank=True, null=True, verbose_name='训练时间(秒)')),
                ('additional_metrics', models.JSONField(blank=True, default=dict, verbose_name='其他指标')),
                ('recorded_at', models.DateTimeField(auto_now_add=True, verbose_name='记录时间')),
                ('workflow', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='metrics_records', to='backend_api.trainingworkflow')),
            ],
            options={
                'verbose_name': '训练指标记录',
                'verbose_name_plural': '训练指标记录',
                'db_table': 'training_metrics_record',
                'ordering': ['workflow', 'epoch'],
                'unique_together': {('workflow', 'training_task_id', 'epoch')},
            },
        ),
    ]
