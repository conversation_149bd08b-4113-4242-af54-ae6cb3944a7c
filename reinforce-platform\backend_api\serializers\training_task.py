from rest_framework import serializers
from ..models.training_task import WorkflowTrainingTask


class TrainingTaskSerializer(serializers.ModelSerializer):
    """训练任务序列化器"""
    
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    step_display = serializers.CharField(source='get_step_display', read_only=True)
    duration = serializers.SerializerMethodField()
    
    class Meta:
        model = WorkflowTrainingTask
        fields = [
            'id', 'name', 'task_type', 'status', 'status_display',
            'workflow_id', 'current_step', 'step_display', 'progress',
            'dataset_name', 'train_samples', 'validation_samples',
            'model_config', 'training_config', 'evaluation_results', 'deployment_config',
            'cpu_usage', 'memory_usage', 'gpu_usage',
            'created_at', 'updated_at', 'started_at', 'completed_at',
            'duration', 'created_by'
        ]
        read_only_fields = ['created_at', 'updated_at', 'duration']
    
    def get_duration(self, obj):
        """获取任务持续时间（秒）"""
        duration = obj.duration
        if duration:
            return int(duration.total_seconds())
        return None
    
    def create(self, validated_data):
        """创建训练任务"""
        # 如果没有指定创建者，尝试从请求中获取
        if 'created_by' not in validated_data:
            request = self.context.get('request')
            if request and hasattr(request, 'user') and request.user.is_authenticated:
                validated_data['created_by'] = request.user
        
        return super().create(validated_data)


class TrainingTaskListSerializer(serializers.ModelSerializer):
    """训练任务列表序列化器（简化版）"""
    
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    step_display = serializers.CharField(source='get_step_display', read_only=True)
    duration = serializers.SerializerMethodField()
    
    class Meta:
        model = WorkflowTrainingTask
        fields = [
            'id', 'name', 'task_type', 'status', 'status_display',
            'current_step', 'step_display', 'progress',
            'cpu_usage', 'memory_usage', 'gpu_usage',
            'created_at', 'started_at', 'duration'
        ]
    
    def get_duration(self, obj):
        """获取任务持续时间（格式化字符串）"""
        duration = obj.duration
        if duration:
            total_seconds = int(duration.total_seconds())
            hours = total_seconds // 3600
            minutes = (total_seconds % 3600) // 60
            seconds = total_seconds % 60
            
            if hours > 0:
                return f"{hours}h {minutes}m {seconds}s"
            elif minutes > 0:
                return f"{minutes}m {seconds}s"
            else:
                return f"{seconds}s"
        return None
