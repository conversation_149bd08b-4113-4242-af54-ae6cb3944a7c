import { LocalStorage, Notify, throttle } from 'quasar'
import { boot } from 'quasar/wrappers'
import axios from 'axios'

const api = axios.create({ baseURL: import.meta.env.VITE_API || `${location.origin}/` })
api.defaults.timeout = 3000

export default boot(({ router }) => {
  const handleTokenInvalid = throttle(function () {
    Notify.create({
      message: '请重新登录',
      type: 'negative',
    })
    router.push('/login')
  }, 500)

  api.interceptors.request.use(
    config => {
      config.headers.Authorization = `Bearer ${LocalStorage.getItem('token')}`
      return config
    },
    error => {
      return Promise.reject(error)
    }
  )

  api.interceptors.response.use(
    res => {
      const { data } = res
      return data
    },
    error => {
      // 安全地获取响应数据，避免解构undefined
      const data = error.response?.data

      // 处理认证相关错误
      if (data && (data.code == 'token_not_valid' || data.code == 'user_not_found')) {
        handleTokenInvalid()
      }

      // 处理超时错误
      if (error.code == 'ECONNABORTED' && error.message && error.message.indexOf('timeout') !== -1) {
        Notify.create({
          message: '请求超时',
          type: 'negative',
        })
      }

      // 处理网络错误
      if (!error.response) {
        Notify.create({
          message: '网络连接失败，请检查网络状态',
          type: 'negative',
        })
        return Promise.reject({ message: '网络连接失败', code: 'NETWORK_ERROR' })
      }

      // 返回错误数据，如果没有响应数据则返回错误对象
      return Promise.reject(data || { message: error.message, code: error.code })
    }
  )

  // 路由拦截器
  router.beforeEach((to, from, next) => {

    if (to.meta.notRequireAuth || LocalStorage.getItem('token')) {
      next()
    } else {
      next({
        path: '/login',
      })
    }
  })
})

export { api, axios }
