# 训练工作流右侧任务列表功能

## 功能概述

在训练工作流页面中添加了右侧任务列表面板，用户在完成每个步骤并点击"下一步"时，会自动保存当前步骤的数据，并在右侧列表中显示训练任务的实时状态。

## 实现的功能

### 1. 页面布局调整
- 将原来的单列布局改为左右两列布局
- 左侧（8列）：原有的训练工作流步骤内容
- 右侧（4列）：新增的训练任务列表面板

### 2. 右侧任务列表面板 (TrainingTaskPanel.vue)
- **实时显示训练任务**：显示所有训练任务，包括正在进行的和已完成的
- **任务状态展示**：
  - 训练中：显示进度条和实时进度百分比
  - 已完成：显示完成状态
  - 其他状态：草稿、配置中等
- **任务信息展示**：
  - 任务名称（如：目标识别-YoloV8-2024-12-15 09:30:15）
  - 训练类型（目标识别-YoloV8、目标识别-YoloV5等）
  - 训练状态
  - 资源配置（CPU数量）
  - 创建时间
- **自动刷新**：每5秒自动更新训练中任务的进度
- **任务交互**：点击任务可以跳转到对应的工作流

### 3. 自动保存和列表更新
- **步骤保存时自动更新**：每次点击"下一步"保存数据后，自动刷新右侧任务列表
- **训练启动时更新**：启动训练任务后，立即刷新列表显示新的训练状态
- **工作流完成时更新**：完成整个工作流后，更新任务状态为已完成

## 文件结构

```
rltp-frontend/src/pages/training-workflow/
├── WorkflowStepPage.vue          # 主页面（已修改）
├── components/
│   ├── TrainingTaskPanel.vue     # 新增：右侧任务列表组件
│   └── TrainingMetricsChart.vue  # 原有：训练指标图表
├── steps/                        # 原有：各步骤组件
│   ├── DataConfigStep.vue
│   ├── ModelConfigStep.vue
│   ├── TrainingConfigStep.vue
│   └── EvaluationStep.vue
└── OverviewPage.vue              # 原有：概览页面
```

## 主要修改内容

### 1. WorkflowStepPage.vue
- 调整页面布局为左右两列
- 添加 TrainingTaskPanel 组件引用
- 修改 `saveStepData`、`startTraining`、`completeWorkflow` 方法，在操作完成后刷新任务列表
- 添加 `handleTaskSelected` 方法处理任务选择

### 2. TrainingTaskPanel.vue（新增）
- 实现任务列表的显示和管理
- 支持实时进度更新
- 提供任务状态的可视化展示
- 暴露 `loadTasks` 方法供父组件调用

## 使用方式

1. **访问训练工作流页面**：
   - 导航到 `/training-workflow` 查看概览
   - 点击任务进入具体的工作流步骤页面

2. **查看右侧任务列表**：
   - 右侧面板会自动显示所有训练任务
   - 训练中的任务会显示实时进度
   - 可以点击任务跳转到对应的工作流

3. **步骤操作**：
   - 在任何步骤中点击"保存并下一步"
   - 数据会自动保存，右侧列表会实时更新
   - 启动训练后，任务状态会立即更新为"训练中"

## 数据来源

- **真实数据**：从后端API获取工作流列表 (`/backend/workflows/`)
- **模拟数据**：为了演示效果，添加了一些模拟的训练中任务
- **实时更新**：通过定时器模拟训练进度的实时更新

## 样式特点

- **科技感设计**：使用渐变背景和现代化卡片设计
- **状态区分**：不同状态的任务使用不同颜色标识
- **响应式布局**：适配不同屏幕尺寸
- **交互反馈**：鼠标悬停和点击有视觉反馈

## 扩展功能

可以进一步扩展的功能：
1. 添加任务筛选和搜索
2. 支持任务的暂停、恢复、取消操作
3. 添加更详细的训练指标展示
4. 支持任务的批量操作
5. 添加任务完成的通知功能

## 技术栈

- **Vue 3 Composition API**：响应式数据管理
- **Quasar Framework**：UI组件库
- **Axios**：HTTP请求
- **CSS3**：样式和动画效果
