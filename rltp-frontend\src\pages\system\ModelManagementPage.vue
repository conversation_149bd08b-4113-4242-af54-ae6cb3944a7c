<template>
  <div class="q-pa-md" style="padding-bottom: 0;">
    <!-- <div class="text-h5 q-mb-md labelColor">模型管理</div> -->

    <!-- 标签页导航 -->
    <q-tabs v-model="activeTab" class="text-primary q-mb-md" align="left">
      <q-tab name="models" label="模型文件管理" />
      <q-tab name="workflow" label="训练工作流" />
    </q-tabs>

    <!-- 标签页内容 -->
    <q-tab-panels v-model="activeTab" animated>
      <!-- 模型文件管理标签页 -->
      <q-tab-panel name="models" class="q-pa-none">

    <div class="row q-col-gutter-lg">
      <!-- 左侧：模型文件管理 -->
      <div class="col-12 col-md-8">
        <q-card flat bordered class="q-mb-md">
          <q-card-section>
            <div class="row q-col-gutter-md">
              <div class="col-md-4 col-sm-6 col-12 flexbox">
                <div class="labelT">上传人</div>
                <q-input class="noLabel" style="flex: 1;" outlined dense :label="filterOptions.creator ? '' : '请输入'"
                  v-model="filterOptions.creator" clearable>
                  <template v-slot:append>
                    <q-icon name="person" />
                  </template>
                </q-input>
              </div>

              <div class="col-md-4 col-sm-6 col-12 flexbox">
                <div class="labelT">上传时间</div>
                <!-- element plus 时间选择器 -->
                <el-date-picker class="datePicker" style="flex: 1;" size="large" v-model="filterOptions.dateVal"
                  type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                  value-format="YYYY-MM-DD" @change="dateChange" />
              </div>

              <div class="col-md-4 col-sm-6 col-12 flexbox">
                <div class="labelT">模型类型</div>
                <q-select style="flex: 1;" :label="filterOptions.model_type ? '' : '请输入'" outlined dense
                  v-model="filterOptions.model_type" :options="modelTypeOptions" emit-value map-options clearable>
                  <template v-slot:append>
                    <q-icon name="category" />
                  </template>
                </q-select>
              </div>

              <div class="col-12" style="display: flex;justify-content: center;">
                <div class="flexCenter">
                  <q-btn color="primary" label="查询" class="q-mr-sm roundBox" @click="handleSearch" />
                  <q-btn outline color="primary" class="grayBox roundBox" label="重置" @click="resetSearch" />
                </div>
              </div>

            </div>
          </q-card-section>
        </q-card>

        <q-table flat bordered :rows="modelFiles" :columns="columns" row-key="id" selection="multiple"
          v-model:selected="selected" :pagination="pagination.value" :loading="loading" :rows-per-page-options="[0]"
          hide-selection-column>
          <template v-slot:body-cell-status="props">
            <q-td :props="props">
              <q-badge :style="getStatusStyle(props.row.model_status)"
                :label="getStatusLabel(props.row.model_status)" />
            </q-td>
          </template>

          <template v-slot:body-cell-operations="props">
            <q-td :props="props">
              <q-btn flat dense size="sm" color="primary" label="下载" @click="downloadModel(props.row)"
                v-if="props.row.download_url" />
              <q-btn class="customText" flat dense size="sm" color="primary" label="编辑" @click="viewFile(props.row)" />
              <q-btn class="customText" flat dense size="sm" color="negative" label="删除"
                @click="confirmDelete(props.row)" />
            </q-td>
          </template>

          <template v-slot:bottom>
            <div class="row items-center full-width paginationEl">
              <div style="margin-right: .25rem;">总计 {{ pagination.total }} 条</div>
              <div>
                <q-select class="unsetHeight" v-model="pagination.rowsPerPage" :options="[5, 10, 15]" outlined dense options-dense
                  emit-value map-options style="min-width: 1.25rem" @update:model-value="onRowsPerPageChange">
                  <!-- 自定义显示值 -->
                  <template v-slot:selected>
                    {{ pagination.rowsPerPage }}/页
                  </template>
                </q-select>
              </div>

              <q-pagination class="unsetHeight" style="margin-left: 80px;margin-right: 80px;" v-model="pagination.page"
                :max="Math.ceil(pagination.total / pagination.rowsPerPage)" :max-pages="5" boundary-numbers
                direction-links />

              <div class="flexbox">
                <div style="margin-right: 10px;">跳到</div>
                <div class="roundBox">
                  <q-input class="dynamic-label-input" v-model="jumpText" style="width:.625rem;" dense
                    @keyup.enter="goJump">
                  </q-input>
                </div>
                <q-btn class="custom-btn" label="跳转" @click="goJump" />
              </div>

            </div>
          </template>
        </q-table>
      </div>

      <!-- 右侧：模型存量 -->
      <div class="col-12 col-md-4">
        <q-card flat bordered>
          <q-card-section class="noPadingBot" style="padding-bottom: 0px !important;">
            <div class="text-h7 labelColor">模型存量</div>
          </q-card-section>

          <q-separator class="mt10"></q-separator>

          <q-card-section style="padding: 0  !important;">
            <div class="q-pa-md flex justify-center" style="padding:0px !important;">
              <div class="model-distribution-chart">
                <div ref="pieChart" style="width:100%; height:3.75rem;"></div>
                <div class="noData" v-if="!modelTypeDistribution.length">暂无数据</div>
              </div>
            </div>
          </q-card-section>
        </q-card>

        <q-card class="mt10">
          <q-card-section class="noPadingBot" style="padding-bottom: 0px !important;">
            <div class="q-mb-sm labelColor">内存使用量</div>
          </q-card-section>
          <q-separator class="mt10"></q-separator>
          <div class="flexReverse">
            <div class="labelColor positionLeft" style="margin-top: .25rem;flex:0 0 auto;">
              <div class="smallText">总计: {{ totalStorage }}</div>
              <div class="smallText">已使用: {{ usedStorage }}</div>
            </div>
            <div ref="gaugeChart" style="width:100%; height: 3.75rem;"></div>
          </div>
        </q-card>

        <q-card class="mt10">
          <div class="btnCon">
            <q-btn color="primary" label="上传模型" class="q-mr-sm roundBox btn50" @click="uploadDialog = true" />
            <q-btn color="secondary" label="导出模型" class="roundBox btn50" @click="exportModel" />
          </div>
        </q-card>
      </div>
    </div>

    <!-- 上传模型弹窗 -->
    <q-dialog v-model="uploadDialog">
      <q-card style="width: 7.5rem !important; max-width: 95vw;" class="blackCard">
        <q-card-section class="row items-center">
          <div class="labelColor">上传模型</div>
          <q-space />
          <q-btn class="closeBtn" icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section class="q-ml-md q-pa-md tk">
          <q-form @submit="onSubmit" class="q-gutter-md">
            <div class="row q-col-gutter-md">
              <div class="col-12">
                <q-input v-model="newModel.name" :label="newModel.name ? '' : '模型名称'" outlined dense
                  :rules="[val => !!val || '请输入模型名称']" />
              </div>

              <div class="col-12">
                <q-input v-model="newModel.desc" :label="newModel.desc ? '' : '模型描述'" outlined dense type="textarea"
                  autogrow />
              </div>

              <div class="col-sm-6 col-xs-12">
                <q-select v-model="newModel.model_type" :options="modelTypeOptions"
                  :label="newModel.model_type ? '' : '模型类型'" outlined dense map-options emit-value
                  :rules="[val => !!val || '请选择模型类型']" />
              </div>

              <div class="col-sm-6 col-xs-12">
                <q-select v-model="newModel.model_status" :options="statusOptions"
                  :label="newModel.model_status ? '' : '模型状态'" outlined dense map-options emit-value
                  :rules="[val => !!val || '请选择模型状态']" />
              </div>

              <div class="col-sm-6 col-xs-12">
                <q-input v-model="newModel.model_size" :label="newModel.model_size ? '' : '模型大小 (MB)'" outlined dense
                  type="number" :rules="[
                    val => !!val || '请输入模型大小',
                    val => val > 0 || '模型大小必须大于0'
                  ]" />
              </div>
            </div>

            <q-separator class="q-my-md" />

            <div class="row q-col-gutter-md">
              <div class="col-12">
                <q-file v-model="uploadFiles.model_file" :label="uploadFiles.model_file ? '' : '模型文件'" outlined dense
                  counter accept=".pb,.h5,.pth,.pt,.onnx,.tflite,.weights" :rules="[val => !!val || '请选择模型文件']"
                  hint="上传模型文件">
                  <template v-slot:prepend>
                    <q-icon name="description" />
                  </template>
                </q-file>
              </div>

              <div class="col-12">
                <q-file v-model="uploadFiles.config_file" :label="uploadFiles.config_file ? '' : '配置文件 (可选)'" outlined
                  dense counter accept=".json,.yaml,.yml,.xml,.txt,.config" hint="上传模型配置文件">
                  <template v-slot:prepend>
                    <q-icon name="settings" />
                  </template>
                </q-file>
              </div>
            </div>

            <div class="row justify-end q-mt-md">
              <q-btn label="上传" type="submit" color="primary" :loading="submitting" class="roundBox minW88" style="margin-right: .125rem;" />
              <q-btn label="取消" v-close-popup class="roundBox grayBox minW88" />
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- 删除确认弹窗 -->
    <q-dialog v-model="deleteDialog" persistent>
      <q-card class="blackCard">
        <q-card-section class="row items-center">
          <q-avatar icon="delete" color="negative" text-color="white" class="delAvatar" />
          <span class="q-ml-sm delContent">确认删除此模型文件?</span>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="primary" v-close-popup />
          <q-btn flat label="删除" color="negative" @click="deleteFile" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 编辑模型弹窗 -->
    <q-dialog v-model="editDialog">
      <q-card style="width: 7.5rem; max-width: 95vw;" class="blackCard">
        <q-card-section class="row items-center">
          <div class="labelColor">编辑模型</div>
          <q-space />
          <q-btn class="closeBtn" icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section class="q-ml-md q-pa-md editDialog">
          <q-form @submit="submitEditModel" class="q-gutter-md">
            <div class="row q-col-gutter-md">
              <div class="col-12 flexC">
                <div class="labelT">模型名称</div>
                <q-input class="flex1 " v-model="editingModel.name" :label="editingModel.name ? '' : '模型名称'" outlined
                  dense :rules="[val => !!val || '请输入模型名称']" />
              </div>

              <div class="col-12  flexC">
                <div class="labelT">模型描述</div>
                <q-input class="flex1 textarea" v-model="editingModel.desc" :label="editingModel.desc ? '' : '模型描述'"
                  outlined dense type="textarea" autogrow />
              </div>

              <div class="col-sm-6 col-xs-12">
                <q-select class="unsetHeight" v-model="editingModel.model_type" :options="modelTypeOptions"
                  :label="editingModel.model_type ? '' : '模型类型'" outlined dense map-options emit-value
                  :rules="[val => !!val || '请选择模型类型']" />
              </div>

              <div class="col-sm-6 col-xs-12">
                <q-select class="unsetHeight" v-model="editingModel.model_status" :options="statusOptions"
                  :label="editingModel.model_status ? '' : '模型状态'" outlined dense map-options emit-value
                  :rules="[val => !!val || '请选择模型状态']" />
              </div>
            </div>

            <div class="row justify-end q-mt-md">
              <q-btn label="保存" type="submit" class="roundBox minW88" color="primary" :loading="submitting"
                style="margin-right: .125rem;" />
              <q-btn label="取消" v-close-popup class="q-mr-sm grayBox roundBox minW88" />
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </q-dialog>
      </q-tab-panel>

      <!-- 训练工作流标签页 -->
      <q-tab-panel name="workflow" class="q-pa-none">
        <div class="workflow-container">
          <div class="workflow-header q-mb-md">
            <div class="text-h6 labelColor">训练工作流管理</div>
            <p class="text-grey-6">管理和监控AI模型的训练工作流程</p>
          </div>

          <div class="workflow-actions q-mb-md">
            <q-btn
              color="primary"
              label="创建新工作流"
              icon="add"
              class="q-mr-sm"
              @click="createNewWorkflow"
            />
            <q-btn
              outline
              color="primary"
              label="查看所有工作流"
              icon="list"
              @click="viewAllWorkflows"
            />
          </div>

          <!-- 工作流快速访问卡片 -->
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6 col-lg-4">
              <q-card flat bordered class="workflow-card">
                <q-card-section>
                  <div class="text-h6 q-mb-sm">深度学习训练</div>
                  <p class="text-grey-6 q-mb-md">创建和管理深度学习模型的训练工作流</p>
                  <q-btn
                    color="primary"
                    label="开始训练"
                    size="sm"
                    @click="startDeepLearningWorkflow"
                  />
                </q-card-section>
              </q-card>
            </div>

            <div class="col-12 col-md-6 col-lg-4">
              <q-card flat bordered class="workflow-card">
                <q-card-section>
                  <div class="text-h6 q-mb-sm">强化学习训练</div>
                  <p class="text-grey-6 q-mb-md">创建和管理强化学习模型的训练工作流</p>
                  <q-btn
                    color="primary"
                    label="开始训练"
                    size="sm"
                    @click="startReinforcementWorkflow"
                  />
                </q-card-section>
              </q-card>
            </div>

            <div class="col-12 col-md-6 col-lg-4">
              <q-card flat bordered class="workflow-card">
                <q-card-section>
                  <div class="text-h6 q-mb-sm">大模型训练</div>
                  <p class="text-grey-6 q-mb-md">创建和管理大语言模型的训练工作流</p>
                  <q-btn
                    color="primary"
                    label="开始训练"
                    size="sm"
                    @click="startLargeModelWorkflow"
                  />
                </q-card-section>
              </q-card>
            </div>
          </div>
        </div>
      </q-tab-panel>
    </q-tab-panels>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { usePlugin } from 'composables/plugin'
import { api } from 'boot/axios'
import { date } from 'quasar'
import { formatFileSize } from 'assets/utils'
import { useRoute, useRouter } from 'vue-router'
import * as echarts from 'echarts';

const { notify, dialog } = usePlugin()
const route = useRoute()
const router = useRouter()

// 标签页状态
const activeTab = ref('models')

const pieChart = ref(null);
let pieChartInstance = null;

// 初始化饼图
const initPieChart = () => {
  if (!pieChart.value) return;

  // 没有数据时，销毁实例并清空画布
  if (!modelTypeDistribution.value.length) {
    if (pieChartInstance) {
      pieChartInstance.dispose();
      pieChartInstance = null;
    }
    pieChart.value.innerHTML = '';
    return;
  }

  // 销毁旧实例
  if (pieChartInstance) {
    pieChartInstance.dispose();
  }

  // 初始化图表
  pieChartInstance = echarts.init(pieChart.value);

  // 准备数据
  const chartData = modelTypeDistribution.value.map(item => ({
    value: item.count,
    name: item.name,
    itemStyle: { color: item.color }
  }));

  // 配置项
  const option = {
    tooltip: {
      trigger: 'item',
    },
    legend: {
      top: 'center',
      right: '10%',
      orient: 'vertical',
      itemStyle: {
        color: 'inherit'
      },
      textStyle: {
        color: '#fff',
        fontSize: 16,
      }
    },
    series: [
      {
        name: '模型类型分布',
        type: 'pie',
        center: ['40%', '50%'],
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: true,
          color: '#fff',
          formatter: '{c}',
          fontSize: 14,
          fontWeight: 'bold',
          position: 'inside',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 28,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: chartData
      }
    ]
  };

  // 设置配置项
  pieChartInstance.setOption(option);

  // 响应式调整
  window.addEventListener('resize', () => pieChartInstance.resize());
}

const gaugeChart = ref(null);
let gaugeChartInstance = null;

// 初始化仪表盘
const initGaugeChart = () => {
  if (!gaugeChart.value) return;

  if (gaugeChartInstance) {
    gaugeChartInstance.dispose();
  }

  gaugeChartInstance = echarts.init(gaugeChart.value);

  const option = {
    series: [{
      type: 'gauge',
      progress: {
        show: false,
        width: 18,
        itemStyle: {
          color: 'auto'  // 自动匹配分段颜色
        }
      },
      axisLine: {
        lineStyle: {
          width: 18,
          color: [
            [0.3, '#165DFF'],  // 0-30% 蓝色
            [0.7, '#F7BA1E'],  // 30-70% 黄色
            [1, '#F53F3F']     // 70-100% 红色
          ]
        }
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: '#fff'
        }
      },
      splitLine: {
        show: false
      },
      axisLabel: {
        show: true,
        color: '#fff'
      },
      pointer: {
        itemStyle: {
          color: 'auto'
        }
      },
      detail: {
        valueAnimation: true,
        fontSize: 24,
        offsetCenter: [0, '70%'],
        color: '#fff',
        formatter: '{value}%'
      },
      data: [{
        value: diskUsage.value
      }]
    }]
  };

  gaugeChartInstance.setOption(option);
  window.addEventListener('resize', () => gaugeChartInstance.resize());
};

const onRowsPerPageChange = (e) => {
  console.log(8888, e)
  pagination.value.rowsPerPage = e
  // fetchModelFiles()
}

// 搜索过滤器
const filterOptions = reactive({
  creator: '',
  name: '',
  model_type: null,
  status: null,
  start_date: '',
  end_date: '',
  dateVal: [],
})

const start_date = computed(() => filterOptions.dateVal?.[0] || '');
const end_date = computed(() => filterOptions.dateVal?.[1] || '');

const jumpText = ref('')//跳转

// 模型类型选项
const modelTypeOptions = [
  { label: '目标检测车', value: '目标检测车' },
  { label: '野战指挥车', value: '野战指挥车' },
  { label: '远程精式火箭炮', value: '远程精式火箭炮' },
  { label: '无人机', value: '无人机' },
  { label: '智能火控', value: '智能火控' },
  { label: '无人车', value: '无人车' }
]

// 状态选项
const statusOptions = [
  { label: '训练中', value: '训练中' },
  { label: '启用', value: '启用' },
  { label: '禁用', value: '禁用' },
  { label: '完成', value: '完成' },
  { label: '中断', value: '中断' }
]

// 表格配置
const loading = ref(false)
const selected = ref([])
const pagination = ref({
  rowsPerPage: 5,//每页几条数据
  page: 1,
  total: 0,
})

const columns = [
  { name: 'name', align: 'center', label: '模型名称', field: 'name', sortable: true },
  { name: 'size', align: 'center', label: '模型大小', field: 'size', sortable: true },
  { name: 'uploader', align: 'center', label: '上传人', field: 'creater_name' },
  { name: 'status', align: 'center', label: '模型状态', field: 'model_status', format: val => val || '未知' },
  { name: 'model_type', align: 'center', label: '模型类型', field: 'model_type', format: val => val || '未分类' },
  {
    name: 'create_time', align: 'center', label: '创建时间', field: 'model_create_time', sortable: true,
    format: val => date.formatDate(val, 'YYYY-MM-DD HH:mm:ss')
  },
  { name: 'operations', align: 'center', label: '操作', field: 'operations' }
]

// 模型文件数据
const modelFiles = ref([])

// 全量数据
const allData = ref([])

// 模型类型分布数据
const modelTypeDistribution = ref([])

// 仿真环境存量数据
const diskUsage = ref(74)
const totalStorage = ref('384TB')
const usedStorage = ref('284.16TB')

// 弹窗控制
const uploadDialog = ref(false)
const deleteDialog = ref(false)
const fileToDelete = ref(null)
const submitting = ref(false)

// 上传相关
const uploadFiles = reactive({
  model_file: null,
  config_file: null
})

const newModel = reactive({
  name: '',
  desc: '',
  model_type: '',
  model_size: 20,
  model_status: '训练中'
})

const dateChange = () => {
  console.log('dateChange >>>', filterOptions.dateVal)
  console.log("开始", start_date.value)
  console.log("结束", end_date.value)
}

// 编辑相关
const editDialog = ref(false)
const editingModel = reactive({
  id: null,
  name: '',
  desc: '',
  model_type: '',
  model_status: ''
})

// 获取状态颜色
const statusStyleMap = {
  '训练中': {
    background: '#638c0b',
    borderColor: '#32b16c',
    textColor: '#ffffff'
  },
  '启用': {
    background: '#214d70',
    borderColor: '#63d4ff',
    textColor: '#ffffff'
  },
  '禁用': {
    background: '#78421f',
    borderColor: '#ed4824',
    textColor: '#ffffff'
  },
  '完成': {
    background: '#4ab4ff',
    borderColor: '#63d4ff',
    textColor: '#ffffff'
  },
  '中断': {
    background: '#b60505',
    borderColor: '#ff0000',
    textColor: '#ffffff'
  }
}

const getStatusStyle = (status) => {
  const style = statusStyleMap[status] || {
    background: '#9e9e9e',
    borderColor: '#616161',
    textColor: '#ffffff'
  }

  return {
    'width': '48px',
    'height': '25px',
    'display': 'flex',
    'justify-content': 'center',
    'align-items': 'center',
    'border-radius': '5px',
    'border': `0.025rem solid ${style.borderColor}`,
    'background': style.background,
    'color': style.textColor,
    'font-size': '12px',
    // 'font-weight': 'bold'
  }
}

// 获取状态标签
function getStatusLabel(status) {
  return status || '未知'
}

// 跳转（支持回车键）
const goJump = () => {
  console.log("回车");
  pagination.value.page = Number(jumpText.value)
}

// 获取模型文件列表
function fetchModelFiles() {
  loading.value = true
  console.log('开始获取模型列表 表格数据')

  // 构建查询参数
  const params = new URLSearchParams()
  if (filterOptions.creator) params.append('creater_name', filterOptions.creator)
  if (filterOptions.name) params.append('name', filterOptions.name)
  if (filterOptions.model_type) params.append('model_type', filterOptions.model_type) //模型类型
  if (filterOptions.status) params.append('status', filterOptions.status)
  if (start_date.value) params.append('start_date', start_date.value)// 开始时间
  if (end_date.value) params.append('end_date', end_date.value) //结束时间
  console.log("查询参数paramas>>>", params)
  if (filterOptions.status) params.append('model_status', filterOptions.status)


  // 添加分页参数
  params.append('page', pagination.value.page.toString())
  params.append('page_size', pagination.value.rowsPerPage.toString())

  api.get(`backend/models/?${params.toString()}`)
    .then(res => {
      if (res && Array.isArray(res.results)) {
        modelFiles.value = res.results.map(item => {
          return {
            id: item.id,
            name: item.name || item.file || '未命名模型',
            size: formatFileSize(item.size || 0),
            creater_name: item.creater_name || '-',
            model_status: item.model_status || '未知',
            model_type: item.model_type || '未分类',
            model_create_time: item.model_create_time || item.create_time,
            address: item.address || '',
            download_url: item.download_url || '',
            desc: item.desc || ''
          }
        })

        console.log("模型数据 >>>", modelFiles)

        // 更新总数
        if (res.count !== undefined) {
          pagination.value.total = res.count
        }
      } else {
        console.error('API返回数据格式不正确:', res)
        notify('获取数据格式异常', 'negative')
        modelFiles.value = []
      }

      loading.value = false
      // 更新模型类型分布
      updateModelTypeDistribution()
    })
    .catch(err => {
      console.error('获取模型文件列表失败:', err)
      notify('获取模型文件列表失败')
      loading.value = false
      modelFiles.value = []
    })
}

// 首次加载获取全量数据
function getEchartsData() {
  console.log('getEchartsData 表格数据')
  api.get(`backend/models/?`)
    .then(res => {
      if (res && Array.isArray(res.results)) {
        allData.value = res.results.map(item => {
          return {
            id: item.id,
            name: item.name || item.file || '未命名模型',
            size: formatFileSize(item.size || 0),
            creater_name: item.creater_name || '-',
            model_status: item.model_status || '未知',
            model_type: item.model_type || '未分类',
            model_create_time: item.model_create_time || item.create_time,
            address: item.address || '',
            download_url: item.download_url || '',
            desc: item.desc || ''
          }
        })

        console.log("模型数据 >>>", allData.value)

        // 更新总数
        if (res.count !== undefined) {
          pagination.value.total = res.count
        }
      } else {
        console.error('全量数据获取失败', res)
        allData.value = []
      }
      // 更新模型类型分布
      updateModelTypeDistribution()
    })
    .catch(err => {
      console.error('全量数据获取失败:', err)
      allData.value = [];
      updateModelTypeDistribution();
    })
}

// 更新模型类型分布数据
function updateModelTypeDistribution() {
  console.log('更新模型类型分布数据')
  // 创建类型计数对象
  const typeCounts = {}
  let total = 0

  // 计算各类型数量
  allData.value.forEach(file => {
    if (!typeCounts[file.model_type]) {
      typeCounts[file.model_type] = 0
    }
    typeCounts[file.model_type]++
    total++
  })

  if (total === 0) {
    modelTypeDistribution.value = [];
    initPieChart();
    return;
  }

  // 固定的颜色映射
  const typeColorMap = {
    '目标检测车': '#1976D2',  // 蓝色
    '野战指挥车': '#26A69A',  // 青色
    '远程精式火箭炮': '#FFC107', // 黄色
    '无人机': '#673AB7',      // 紫色
    '智能火控': '#F44336',    // 红色
    '无人车': '#FF9800',      // 橙色
    '水面舰艇': '#4CAF50',    // 绿色
    '平台决策': '#E91E63',    // 粉色
    '通信组网': '#9C27B0',    // 深紫色
    '预警探测': '#607D8B'     // 蓝灰色
  }

  // 其他类型的备用颜色
  const fallbackColors = ['#795548', '#9E9E9E', '#3F51B5', '#009688', '#CDDC39', '#03A9F4', '#8BC34A', '#FF5722'];

  // 生成分布数据
  const distribution = [];
  let colorIndex = 0;

  for (const type in typeCounts) {
    const color = typeColorMap[type] || fallbackColors[colorIndex % fallbackColors.length];
    colorIndex++;

    distribution.push({
      name: type,
      percentage: Math.round((typeCounts[type] / total) * 100),
      color: color,
      count: typeCounts[type]
    });
  }

  // 按百分比从大到小排序
  distribution.sort((a, b) => b.percentage - a.percentage);

  modelTypeDistribution.value = distribution;
  console.log("modelTypeDistribution.value", modelTypeDistribution.value)
  initPieChart();
}

// 获取模型类型标签
function getModelTypeLabel(value) {
  const option = modelTypeOptions.find(opt => opt.value === value)
  return option ? option.label : value
}

// 查看文件详情
function viewFile(file) {
  editDialog.value = true
  editingModel.id = file.id
  editingModel.name = file.name
  editingModel.desc = file.desc
  editingModel.model_type = file.model_type
  editingModel.model_status = file.model_status
}

// 下载模型
function downloadModel(file) {
  if (file.download_url) {
    window.open(import.meta.env.VITE_API + file.download_url)
  } else if (file.address) {
    window.open(import.meta.env.VITE_API + file.address)
  } else {
    notify('无法下载，文件地址不存在', 'negative')
  }
}

// 删除文件 >>> 打开确认弹窗
function confirmDelete(file) {
  fileToDelete.value = file
  deleteDialog.value = true

}

// 删除文件 >>> 确认删除 >>> 调接口 >>> 删除成功 >>> 刷新列表
function deleteFile() {
  if (fileToDelete.value) {
    loading.value = true

    api.delete(`backend/models/${fileToDelete.value.id}/`)
      .then(() => {
        const index = modelFiles.value.findIndex(f => f.id === fileToDelete.value.id)
        if (index !== -1) {
          modelFiles.value.splice(index, 1)
        }
        fetchModelFiles() //获取数据
        notify('删除成功', 'positive')
        fileToDelete.value = null
        loading.value = false

        // 新增：刷新模型存量 ECharts
        getEchartsData()

        // 更新模型类型分布
        updateModelTypeDistribution()
      })
      .catch(err => {
        console.error('删除文件失败:', err)
        notify('删除文件失败')
        loading.value = false
      })
  }
}

// 上传模型文件
function onSubmit() {
  if (!uploadFiles.model_file) {
    notify('请选择模型文件')
    return
  }

  submitting.value = true

  const formData = new FormData()
  formData.append('name', newModel.name)
  formData.append('desc', newModel.desc || newModel.name)
  formData.append('model_type', newModel.model_type)
  formData.append('model_size', newModel.model_size)
  formData.append('model_status', newModel.model_status)

  if (uploadFiles.model_file) {
    formData.append('model_file', uploadFiles.model_file)
  }

  if (uploadFiles.config_file) {
    formData.append('config_file', uploadFiles.config_file)
  }

  api.post('backend/models/', formData)
    .then(res => {
      const newFile = {
        id: res.id,
        name: res.name,
        size: formatFileSize(res.size),
        creater_name: res.creater_name,
        model_status: res.model_status || newModel.model_status,
        model_type: res.model_type || newModel.model_type,
        model_create_time: res.model_create_time,
        address: res.address,
        download_url: res.download_url
      }

      modelFiles.value.unshift(newFile)

      // 再次获取模型表格数据
      pagination.value.page = 1
      fetchModelFiles()

      // 新增：刷新模型存量 ECharts
      getEchartsData()

      uploadDialog.value = false
      notify('上传成功', 'positive')

      // 重置表单
      resetNewModelForm()

      // 更新模型类型分布
      updateModelTypeDistribution()
    })
    .catch(err => {
      console.error('上传失败:', err)
      notify('上传失败: ' + (err.message || '未知错误'), 'negative')
    })
    .finally(() => {
      submitting.value = false
    })
}

// 导出模型
function exportModel() {
  if (selected.value.length === 0) {
    notify('请选择要导出的模型', 'warning')
    return
  }

  const ids = selected.value.map(item => item.id).join(',')

  // 获取模型数据
  api.get('backend/models/', {
    params: {
      page_size: 1000,
      ids: ids
    }
  })
    .then(response => {
      if (!response || !response.results) {
        notify('导出失败', 'negative')
        return
      }

      // 转换为CSV格式
      const headers = ['模型编号', '模型名称', '模型类型', '版本', '大小', '创建人', '状态', '创建时间', '描述']
      const rows = response.results.map(model => [
        model.id,
        model.name || '',
        model.model_type || '',
        model.version || '',
        model.size || '',
        model.creator || '',
        model.status || '',
        model.created_at || '',
        model.description || ''
      ])

      // 创建CSV内容
      const csvContent = [
        headers.join(','),
        ...rows.map(row => row.join(','))
      ].join('\n')

      // 创建并下载文件
      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `模型列表_${date.formatDate(new Date(), 'YYYY-MM-DD_HH-mm')}.csv`)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      notify('导出成功', 'positive')
    })
    .catch(error => {
      console.error('导出失败:', error)
      notify('导出失败', 'negative')
    })
}

function resetNewModelForm() {
  newModel.name = ''
  newModel.desc = ''
  newModel.model_type = ''
  newModel.model_size = 20
  newModel.model_status = '训练中'
  uploadFiles.model_file = null
  uploadFiles.config_file = null
}

// 搜索功能
function handleSearch() {
  pagination.value.page = 1 // 重置到第一页
  fetchModelFiles()
}

function resetSearch() {
  // 重置所有过滤条件
  Object.keys(filterOptions).forEach(key => {
    if (typeof filterOptions[key] === 'boolean') {
      filterOptions[key] = null
    } else {
      filterOptions[key] = ''
    }
  })

  pagination.value.page = 1 // 重置到第一页
  fetchModelFiles()
}

// 监听分页变化
watch(() => pagination.value.rowsPerPage, (newVal, oldVal) => {
  // 当 rowsPerPage 变化时，重置页码为 1
  pagination.value.page = 1
  fetchModelFiles()
})

// 保留原有的 page 监听（单独处理页码变化）
watch(() => pagination.value.page, () => {
  fetchModelFiles()
})

// 生成饼图切片的SVG路径
function generatePieSlices() {
  const slices = [];
  const radius = 45;
  const centerX = 50;
  const centerY = 50;

  let startAngle = 0;

  modelTypeDistribution.value.forEach(item => {
    const angle = (item.percentage / 100) * 360;
    const endAngle = startAngle + angle;

    // 计算SVG路径
    const startRad = (startAngle - 90) * Math.PI / 180;
    const endRad = (endAngle - 90) * Math.PI / 180;

    const x1 = centerX + radius * Math.cos(startRad);
    const y1 = centerY + radius * Math.sin(startRad);
    const x2 = centerX + radius * Math.cos(endRad);
    const y2 = centerY + radius * Math.sin(endRad);

    // 确定是大弧还是小弧
    const largeArcFlag = angle > 180 ? 1 : 0;

    // 构建SVG path
    const path = `M ${centerX} ${centerY} L ${x1} ${y1} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2} Z`;

    slices.push({
      path: path,
      color: item.color
    });

    startAngle = endAngle;
  });

  return slices;
}

// 提交编辑
function submitEditModel() {
  if (!editingModel.id) return

  submitting.value = true
  api.put(`backend/models/${editingModel.id}/`, {
    name: editingModel.name,
    desc: editingModel.desc,
    model_type: editingModel.model_type,
    model_status: editingModel.model_status
  })
    .then(res => {
      // 更新本地数据
      const index = modelFiles.value.findIndex(f => f.id === editingModel.id)
      if (index !== -1) {
        modelFiles.value[index] = {
          ...modelFiles.value[index],
          name: res.name,
          desc: res.desc,
          model_type: res.model_type,
          model_status: res.model_status
        }
      }

      editDialog.value = false
      notify('更新成功', 'positive')

      // 更新模型类型分布
      updateModelTypeDistribution()
    })
    .catch(err => {
      console.error('更新失败:', err)
      notify('更新失败: ' + (err.message || '未知错误'), 'negative')
    })
    .finally(() => {
      submitting.value = false
    })
}

// 初始加载
onMounted(() => {

  fetchModelFiles()

  getEchartsData()

  initGaugeChart();// 初始化仪表盘

  // 检查URL参数，如果有openUploadDialog=true则自动打开上传对话框
  if (route.query.openUploadDialog === 'true') {
    uploadDialog.value = true
  }
})

// 训练工作流相关方法
const createNewWorkflow = () => {
  router.push('/ai-model/training-workflow')
}

const viewAllWorkflows = () => {
  router.push('/ai-model/training-workflow')
}

const startDeepLearningWorkflow = () => {
  router.push('/ai-model/training-workflow?type=deep-learning')
}

const startReinforcementWorkflow = () => {
  router.push('/ai-model/training-workflow?type=reinforcement')
}

const startLargeModelWorkflow = () => {
  router.push('/ai-model/training-workflow?type=large-model')
}
</script>

<style lang="scss" scoped>
// $
.full-height {
  height: 100%;
}

.model-distribution-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  position: relative;
  .noData{
    font-size: .175rem;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}

.pie-chart {
  width: 200px;
  height: 200px;
  position: relative;
  margin-bottom: 20px;
}

.pie-chart svg {
  width: 100%;
  height: 100%;
}

.pie-chart path {
  transition: transform 0.2s;
}

.pie-chart path:hover {
  transform: translateX(2px) translateY(2px);
  filter: drop-shadow(0px 0px 3px rgba(0, 0, 0, 0.3));
}

.pie-chart-legend {
  width: 100%;
  max-width: 300px;
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  flex: 1;
}

.pie-chart-legend .flex {
  justify-content: flex-start;
  margin-bottom: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.pie-chart-legend .flex:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.text {
  padding-left: 0;
  position: relative;
  left: 4px;
  top: 6px;
  width: 11px;
}

@media (max-width: 599px) {
  .col-md-7 {
    margin-bottom: 16px;
  }

  .col-md-7 .row {
    margin: 0;
  }

  .col-5 {
    padding: 0 4px;
  }

  .text {
    line-height: 40px;
  }

  .flex.justify-start {
    justify-content: flex-start;
  }

  .q-btn {
    min-width: 80px;
  }
}

:deep(.q-field--auto-height.q-field--labeled .q-field__control-container) {
  padding-top: 0;
}

:deep(.q-field--labeled .q-field__native) {
  padding-top: 0;
  padding-bottom: 0;
}

:deep(.q-textarea.q-field--dense.q-field--labeled .q-field__control-container) {
  padding: 0 !important;
}

:deep(.q-textarea.q-field--dense.q-field--labeled .q-field__native) {
  height: 100% !important;
  line-height: .45rem;
}

.flexbox {
  display: flex;
  align-items: center;
}

.paginationEl {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 默认
// :deep(.el-input__wrapper) {
//   border-radius: 0% !important;
//   background: var(--q-inputBG);
//   border: 1px solid transparent;
//   border-image: linear-gradient(to right, #3c66a4, #b2d7f1) 1;
// }

// :deep(.el-date-editor.el-input__wrapper){
//   box-shadow: none !important;
// }
// :deep(.el-date-editor.el-input__wrapper.is-active){
//   box-shadow: none !important;
// }

// hover
:deep(.el-date-editor.el-input__wrapper:hover) {
  // box-shadow: 0 0 0 1px var(--q-testhover) inset;
}

// 聚焦
:deep(.el-date-editor.el-input__wrapper.is-active) {
  box-shadow: 0 0 0 .025rem var(--q-primary) inset;
}

// 选择框 内容过长时 不影响布局
:deep(.q-field__native span) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 1.5rem;
}

.flexCenter {
  width: 3rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.labelT {
  font-size: .175rem;
  font-weight: 500;
  color: $slideText;
  line-height: .275rem;
  margin-right: .125rem;
}

.dynamic-label-input.q-field--with-content :deep(.q-field__label),
.dynamic-label-input.q-field--focused :deep(.q-field__label) {
  display: none;
}

.dynamic-label-input :deep(.q-field__inner) {
  border: none; //设置圆角的 必须单独取消
}

// 单独设置圆角和边框色
.dynamic-label-input :deep(.q-field__inner) {
  border-radius: 8px;
}

// 文字居中
.dynamic-label-input :deep(.q-field__native) {
  text-align: center;
}

// 分页器
:deep(.q-pagination .q-btn) {
  color: white !important;
}

:deep(.q-pagination .q-btn--standard) {
  background: #396ea4 !important;
}

:deep(.q-pagination .q-btn:hover) {
  background: #396ea4 !important;
}

.mt10 {
  margin-top: 10px;
}

.noPadingBot {
  padding-bottom: 0;
  padding-top: 10px;
}

.flexReverse {
  display: flex;
  justify-content: center;
  position: relative;
}

.btnCon {
  display: flex;
  justify-content: space-evenly;
  /* align-items: center; */
  padding: .125rem 0px;
}


// element 日期选择器
:deep(.datePicker) {
  height: 0.5rem !important;
}

:deep(.el-date-editor .el-range__icon) {
  //left icon
  font-size: .175rem;
}

:deep(.el-range-editor--large .el-range-input) {
  font-size: .175rem;
}

:deep(.el-date-editor .el-range__close-icon) {
  //close 
  font-size: .175rem;
}

:deep(.el-range-editor--large .el-range-separator) {
  //至
  font-size: .175rem;
  line-height: .5rem;
}

// 表格适配
:deep(.q-table__container) {
  // border: none !important;
  min-height: 7.5rem !important; //最小高度600px
  box-shadow: 0 .0125rem .0625rem #0003, 0 .025rem .025rem #00000024, 0 .0375rem .0125rem -0.025rem #0000001f;
}

:deep(.q-table__container .q-table thead th) {
  font-size: .175rem !important;
}

:deep(.q-table__container .q-table tbody td) {
  font-size: .175rem !important;
}

:deep(.q-table td) {
  padding: .0875rem .2rem !important;
}

:deep(.q-table tbody td) {
  height: .6rem !important;
}

// 表格内标签
:deep(.q-table .q-badge) {
  width: .6rem !important;
  height: .3125rem !important;
  border-radius: .0625rem !important;
  font-size: .15rem !important;
}

// 分页器适配
:deep(.q-pagination__middle .q-btn) {
  width: .35rem;
  height: .35rem;
}

:deep(.q-table__bottom) {
  font-size: .15rem; //文字大小
}

:deep(.q-field--dense .q-field__marginal) {
  height: .5rem !important;
}

// 跳转按钮
.custom-btn.q-btn {
  width: fit-content;
  background-color: #396ea4;
  margin-left: .875rem;
  border-radius: .0625rem;
  height: 0.45rem;
}

:deep(.q-btn__content) {
  font-size: .175rem;
}

// 按钮
:deep(.q-btn) {
  padding: .05rem .2rem;
  height: .45rem;
}

// 输入框start
.q-field {
  font-size: .175rem;
  height: .5rem;
}

:deep(.q-field--dense .q-field__control) {
  //输入高度
  height: 100% !important;
}

:deep(.q-field--dense .q-field__label) {
  font-size: .175rem !important;
  top: .125rem;
}

:deep(.q-field__label) {
  //label
  line-height: .25rem !important;
}

// 尾部带图标输入框
:deep(.q-field__marginal) {
  font-size: .3rem !important;
}

:deep(.q-field--labeled .q-field__native) {
  line-height: .3rem !important;
}

// 输入框end

.smallText {
  font-size: .175rem;
}

.positionLeft {
  position: relative;
  left: .375rem;
}

.editDialog .q-field {
  height:.75rem;
}


.flexC {
  display: flex;
  align-items: center;
}

.flexC .textarea {
  height: .625rem !important;
}

:deep(.flexC .q-field--labeled .q-field__native) {
  line-height: .625rem !important;
}

.flex1 {
  flex: 1;
}

.blackCard {
  min-width: 5.375rem;
  background: rgba(0, 0, 0, .9) !important;
  border: none !important;
  border-radius: .0625rem !important;
  // overflow: hidden !important;

  :deep(.q-btn) {
    padding: .05rem .2rem;
    height: .45rem;
    // width: 1.rem;
  }

  :deep(.q-btn__content) {
    font-size: .175rem;
  }

  .delAvatar {
    font-size: .6rem;
  }

  .delContent {
    font-size: .2rem;
  }
}

.tk .q-field--with-bottom{
  padding-bottom: 0 !important;
}
:deep(.tk .q-field__bottom){
  min-height: .25rem !important;
  padding: .1rem .15rem 0!important;
}
:deep(.tk .q-field--dense .q-field__bottom){
  font-size: .1375rem !important;
}
.q-col-gutter-md > *{
  padding-left: .25rem !important;
  padding-top:.25rem !important;
}
.tk .q-mt-md{
  margin-top:.5rem !important;
}

// 训练工作流样式
.workflow-container {
  .workflow-header {
    text-align: center;
    padding: 1rem 0;
  }

  .workflow-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
  }

  .workflow-card {
    height: 100%;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .q-card-section {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
  }
}
</style>
