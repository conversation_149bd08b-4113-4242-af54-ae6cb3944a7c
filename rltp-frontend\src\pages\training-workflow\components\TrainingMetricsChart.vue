<template>
  <div class="training-metrics-chart">
    <div class="row q-gutter-md">
      <!-- 损失函数图表 -->
      <div class="col-12 col-md-6">
        <q-card flat bordered>
          <q-card-section>
            <div class="text-subtitle1 q-mb-md">训练损失</div>
            <div ref="lossChartRef" style="height: 300px;"></div>
          </q-card-section>
        </q-card>
      </div>

      <!-- 准确率图表 -->
      <div class="col-12 col-md-6">
        <q-card flat bordered>
          <q-card-section>
            <div class="text-subtitle1 q-mb-md">准确率</div>
            <div ref="accuracyChartRef" style="height: 300px;"></div>
          </q-card-section>
        </q-card>
      </div>

      <!-- YOLO特定指标 -->
      <div class="col-12 col-md-6" v-if="isYoloTask">
        <q-card flat bordered>
          <q-card-section>
            <div class="text-subtitle1 q-mb-md">mAP指标</div>
            <div ref="mapChartRef" style="height: 300px;"></div>
          </q-card-section>
        </q-card>
      </div>

      <!-- 学习率图表 -->
      <div class="col-12 col-md-6">
        <q-card flat bordered>
          <q-card-section>
            <div class="text-subtitle1 q-mb-md">学习率</div>
            <div ref="lrChartRef" style="height: 300px;"></div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- 实时指标表格 -->
    <q-card flat bordered class="q-mt-md">
      <q-card-section>
        <div class="text-subtitle1 q-mb-md">最新指标</div>
        <q-table
          :rows="latestMetrics"
          :columns="metricsColumns"
          row-key="epoch"
          :pagination="{ rowsPerPage: 10 }"
          dense
        />
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { api } from 'boot/axios'
import * as echarts from 'echarts'

const props = defineProps({
  workflowId: {
    type: [String, Number],
    required: true
  }
})

// 响应式数据
const metricsData = ref([])
const lossChartRef = ref(null)
const accuracyChartRef = ref(null)
const mapChartRef = ref(null)
const lrChartRef = ref(null)

let lossChart = null
let accuracyChart = null
let mapChart = null
let lrChart = null
let pollingInterval = null

// 计算属性
const isYoloTask = computed(() => {
  return metricsData.value.some(metric => metric.map50 !== null)
})

const latestMetrics = computed(() => {
  return metricsData.value.slice(-10).reverse()
})

const metricsColumns = computed(() => {
  const baseColumns = [
    { name: 'epoch', label: 'Epoch', field: 'epoch', align: 'center' },
    { name: 'train_loss', label: '训练损失', field: 'train_loss', align: 'center', format: val => val?.toFixed(4) },
    { name: 'val_loss', label: '验证损失', field: 'val_loss', align: 'center', format: val => val?.toFixed(4) },
    { name: 'train_accuracy', label: '训练准确率', field: 'train_accuracy', align: 'center', format: val => val ? `${(val * 100).toFixed(2)}%` : '-' },
    { name: 'val_accuracy', label: '验证准确率', field: 'val_accuracy', align: 'center', format: val => val ? `${(val * 100).toFixed(2)}%` : '-' }
  ]

  if (isYoloTask.value) {
    baseColumns.push(
      { name: 'precision', label: '精确率', field: 'precision', align: 'center', format: val => val ? `${(val * 100).toFixed(2)}%` : '-' },
      { name: 'recall', label: '召回率', field: 'recall', align: 'center', format: val => val ? `${(val * 100).toFixed(2)}%` : '-' },
      { name: 'map50', label: 'mAP@0.5', field: 'map50', align: 'center', format: val => val ? `${(val * 100).toFixed(2)}%` : '-' },
      { name: 'map50_95', label: 'mAP@0.5:0.95', field: 'map50_95', align: 'center', format: val => val ? `${(val * 100).toFixed(2)}%` : '-' }
    )
  }

  return baseColumns
})

// 方法
const loadMetrics = async () => {
  try {
    const response = await api.get(`backend/workflows/${props.workflowId}/metrics/`)
    metricsData.value = response
    updateCharts()
  } catch (error) {
    console.error('加载训练指标失败:', error)
  }
}

const initCharts = () => {
  // 初始化损失图表
  if (lossChartRef.value) {
    lossChart = echarts.init(lossChartRef.value)
    lossChart.setOption({
      title: { text: '训练损失', left: 'center' },
      tooltip: { trigger: 'axis' },
      legend: { data: ['训练损失', '验证损失'], bottom: 0 },
      xAxis: { type: 'category', name: 'Epoch' },
      yAxis: { type: 'value', name: 'Loss' },
      series: [
        { name: '训练损失', type: 'line', data: [] },
        { name: '验证损失', type: 'line', data: [] }
      ]
    })
  }

  // 初始化准确率图表
  if (accuracyChartRef.value) {
    accuracyChart = echarts.init(accuracyChartRef.value)
    accuracyChart.setOption({
      title: { text: '准确率', left: 'center' },
      tooltip: { trigger: 'axis' },
      legend: { data: ['训练准确率', '验证准确率'], bottom: 0 },
      xAxis: { type: 'category', name: 'Epoch' },
      yAxis: { type: 'value', name: 'Accuracy', min: 0, max: 1 },
      series: [
        { name: '训练准确率', type: 'line', data: [] },
        { name: '验证准确率', type: 'line', data: [] }
      ]
    })
  }

  // 初始化mAP图表（YOLO任务）
  if (mapChartRef.value) {
    mapChart = echarts.init(mapChartRef.value)
    mapChart.setOption({
      title: { text: 'mAP指标', left: 'center' },
      tooltip: { trigger: 'axis' },
      legend: { data: ['mAP@0.5', 'mAP@0.5:0.95'], bottom: 0 },
      xAxis: { type: 'category', name: 'Epoch' },
      yAxis: { type: 'value', name: 'mAP', min: 0, max: 1 },
      series: [
        { name: 'mAP@0.5', type: 'line', data: [] },
        { name: 'mAP@0.5:0.95', type: 'line', data: [] }
      ]
    })
  }

  // 初始化学习率图表
  if (lrChartRef.value) {
    lrChart = echarts.init(lrChartRef.value)
    lrChart.setOption({
      title: { text: '学习率', left: 'center' },
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'category', name: 'Epoch' },
      yAxis: { type: 'value', name: 'Learning Rate' },
      series: [{ name: '学习率', type: 'line', data: [] }]
    })
  }
}

const updateCharts = () => {
  if (!metricsData.value.length) return

  const epochs = metricsData.value.map(m => m.epoch)
  const trainLoss = metricsData.value.map(m => m.train_loss)
  const valLoss = metricsData.value.map(m => m.val_loss)
  const trainAcc = metricsData.value.map(m => m.train_accuracy)
  const valAcc = metricsData.value.map(m => m.val_accuracy)
  const learningRates = metricsData.value.map(m => m.learning_rate)

  // 更新损失图表
  if (lossChart) {
    lossChart.setOption({
      xAxis: { data: epochs },
      series: [
        { data: trainLoss },
        { data: valLoss }
      ]
    })
  }

  // 更新准确率图表
  if (accuracyChart) {
    accuracyChart.setOption({
      xAxis: { data: epochs },
      series: [
        { data: trainAcc },
        { data: valAcc }
      ]
    })
  }

  // 更新mAP图表（如果是YOLO任务）
  if (mapChart && isYoloTask.value) {
    const map50 = metricsData.value.map(m => m.map50)
    const map50_95 = metricsData.value.map(m => m.map50_95)
    
    mapChart.setOption({
      xAxis: { data: epochs },
      series: [
        { data: map50 },
        { data: map50_95 }
      ]
    })
  }

  // 更新学习率图表
  if (lrChart) {
    lrChart.setOption({
      xAxis: { data: epochs },
      series: [{ data: learningRates }]
    })
  }
}

const startPolling = () => {
  // 每5秒更新一次指标
  pollingInterval = setInterval(() => {
    loadMetrics()
  }, 5000)
}

const stopPolling = () => {
  if (pollingInterval) {
    clearInterval(pollingInterval)
    pollingInterval = null
  }
}

// 监听窗口大小变化，重新调整图表
const handleResize = () => {
  lossChart?.resize()
  accuracyChart?.resize()
  mapChart?.resize()
  lrChart?.resize()
}

// 生命周期
onMounted(() => {
  initCharts()
  loadMetrics()
  startPolling()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  stopPolling()
  window.removeEventListener('resize', handleResize)
  
  // 销毁图表实例
  lossChart?.dispose()
  accuracyChart?.dispose()
  mapChart?.dispose()
  lrChart?.dispose()
})

// 监听数据变化
watch(() => metricsData.value, () => {
  updateCharts()
}, { deep: true })
</script>

<style scoped>
.training-metrics-chart {
  width: 100%;
}
</style>
