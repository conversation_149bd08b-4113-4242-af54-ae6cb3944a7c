from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db import transaction
import logging

from backend_api.models.training_workflow import (
    TrainingWorkflow, 
    TrainingMetricsRecord, 
    ModelEvaluation, 
    InferenceModel
)
from backend_api.serializers.training_workflow import (
    TrainingWorkflowListSerializer,
    TrainingWorkflowDetailSerializer,
    TrainingWorkflowCreateSerializer,
    TrainingWorkflowUpdateSerializer,
    StepDataSerializer,
    TrainingMetricsRecordSerializer,
    TrainingMetricsCreateSerializer,
    ModelEvaluationSerializer,
    InferenceModelSerializer,
    InferenceModelListSerializer,
    WorkflowStepNavigationSerializer
)

logger = logging.getLogger(__name__)


class TrainingWorkflowListView(generics.ListCreateAPIView):
    """训练工作流列表视图"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = TrainingWorkflow.objects.filter(created_by=self.request.user)
        
        # 支持按任务类型过滤
        task_type = self.request.query_params.get('task_type')
        if task_type:
            queryset = queryset.filter(task_type=task_type)
        
        # 支持按状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        return queryset.order_by('-updated_at')
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return TrainingWorkflowCreateSerializer
        return TrainingWorkflowListSerializer
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


class TrainingWorkflowDetailView(generics.RetrieveUpdateDestroyAPIView):
    """训练工作流详情视图"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return TrainingWorkflow.objects.filter(created_by=self.request.user)
    
    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return TrainingWorkflowUpdateSerializer
        return TrainingWorkflowDetailSerializer


class WorkflowStepNavigationView(APIView):
    """工作流步骤导航视图"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """保存当前步骤数据并导航到下一步"""
        serializer = WorkflowStepNavigationSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        workflow_id = serializer.validated_data['workflow_id']
        current_step = serializer.validated_data['current_step']
        step_data = serializer.validated_data.get('step_data', {})
        next_step = serializer.validated_data.get('next_step')
        
        try:
            workflow = get_object_or_404(TrainingWorkflow, id=workflow_id, created_by=request.user)
            
            with transaction.atomic():
                # 保存当前步骤数据
                if current_step == 'step1_data':
                    workflow.step1_data_config = step_data
                elif current_step == 'step2_model':
                    workflow.step2_model_config = step_data
                elif current_step == 'step3_training':
                    workflow.step3_training_config = step_data
                elif current_step == 'step4_evaluation':
                    workflow.step4_evaluation_config = step_data
                
                # 更新当前步骤
                if next_step:
                    workflow.current_step = next_step
                    if next_step == 'step3_training' and not workflow.started_at:
                        workflow.started_at = timezone.now()
                    elif next_step == 'completed':
                        workflow.completed_at = timezone.now()
                        workflow.status = 'completed'
                
                workflow.save()
            
            return Response({
                'success': True,
                'message': '步骤数据已保存',
                'current_step': workflow.current_step,
                'progress': workflow.get_step_progress()
            })
            
        except Exception as e:
            logger.error(f"保存工作流步骤数据失败: {e}")
            return Response({
                'success': False,
                'message': f'保存失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class WorkflowStepDataView(APIView):
    """获取和更新特定步骤的数据"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request, workflow_id, step):
        """获取指定步骤的数据"""
        workflow = get_object_or_404(TrainingWorkflow, id=workflow_id, created_by=request.user)
        
        step_data = {}
        if step == 'step1_data':
            step_data = workflow.step1_data_config
        elif step == 'step2_model':
            step_data = workflow.step2_model_config
        elif step == 'step3_training':
            step_data = workflow.step3_training_config
        elif step == 'step4_evaluation':
            step_data = workflow.step4_evaluation_config
        
        return Response({
            'workflow_id': workflow.id,
            'step': step,
            'data': step_data,
            'current_step': workflow.current_step,
            'can_proceed': workflow.can_proceed_to_next_step()
        })
    
    def post(self, request, workflow_id, step):
        """更新指定步骤的数据"""
        workflow = get_object_or_404(TrainingWorkflow, id=workflow_id, created_by=request.user)
        
        step_data = request.data.get('data', {})
        
        try:
            with transaction.atomic():
                if step == 'step1_data':
                    workflow.step1_data_config = step_data
                    if workflow.current_step == 'draft':
                        workflow.current_step = 'step1_data'
                elif step == 'step2_model':
                    workflow.step2_model_config = step_data
                    if workflow.current_step == 'step1_data':
                        workflow.current_step = 'step2_model'
                elif step == 'step3_training':
                    workflow.step3_training_config = step_data
                    if workflow.current_step == 'step2_model':
                        workflow.current_step = 'step3_training'
                elif step == 'step4_evaluation':
                    workflow.step4_evaluation_config = step_data
                    if workflow.current_step == 'step3_training':
                        workflow.current_step = 'step4_evaluation'
                
                workflow.save()
            
            return Response({
                'success': True,
                'message': '数据已保存',
                'current_step': workflow.current_step,
                'progress': workflow.get_step_progress()
            })
            
        except Exception as e:
            logger.error(f"更新步骤数据失败: {e}")
            return Response({
                'success': False,
                'message': f'保存失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TrainingMetricsView(generics.ListCreateAPIView):
    """训练指标视图"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        workflow_id = self.kwargs.get('workflow_id')
        return TrainingMetricsRecord.objects.filter(
            workflow_id=workflow_id,
            workflow__created_by=self.request.user
        ).order_by('epoch')
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return TrainingMetricsCreateSerializer
        return TrainingMetricsRecordSerializer
    
    def perform_create(self, serializer):
        workflow_id = self.kwargs.get('workflow_id')
        workflow = get_object_or_404(TrainingWorkflow, id=workflow_id, created_by=self.request.user)
        serializer.save(workflow=workflow)


class ModelEvaluationView(generics.RetrieveUpdateAPIView):
    """模型评估视图"""
    
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = ModelEvaluationSerializer
    
    def get_object(self):
        workflow_id = self.kwargs.get('workflow_id')
        workflow = get_object_or_404(TrainingWorkflow, id=workflow_id, created_by=self.request.user)
        evaluation, created = ModelEvaluation.objects.get_or_create(workflow=workflow)
        return evaluation


class InferenceModelListView(generics.ListAPIView):
    """推理模型列表视图"""
    
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = InferenceModelListSerializer
    
    def get_queryset(self):
        queryset = InferenceModel.objects.filter(workflow__created_by=self.request.user)
        
        # 支持按任务类型过滤
        task_type = self.request.query_params.get('task_type')
        if task_type:
            queryset = queryset.filter(workflow__task_type=task_type)
        
        # 支持按状态过滤
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        return queryset.order_by('-created_at')


class InferenceModelDetailView(generics.RetrieveUpdateAPIView):
    """推理模型详情视图"""
    
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = InferenceModelSerializer
    
    def get_queryset(self):
        return InferenceModel.objects.filter(workflow__created_by=self.request.user)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def workflow_overview(request):
    """工作流概览 - 获取各类型任务的统计信息"""
    
    user_workflows = TrainingWorkflow.objects.filter(created_by=request.user)
    
    # 按任务类型分组统计
    task_types_stats = {}
    for choice in TrainingWorkflow.TASK_TYPES:
        task_type, display_name = choice
        workflows = user_workflows.filter(task_type=task_type)
        
        task_types_stats[task_type] = {
            'display_name': display_name,
            'total_count': workflows.count(),
            'completed_count': workflows.filter(status='completed').count(),
            'training_count': workflows.filter(current_step='step3_training').count(),
            'recent_workflows': TrainingWorkflowListSerializer(
                workflows.order_by('-updated_at')[:5], many=True
            ).data
        }
    
    # 总体统计
    total_stats = {
        'total_workflows': user_workflows.count(),
        'completed_workflows': user_workflows.filter(status='completed').count(),
        'active_workflows': user_workflows.exclude(status__in=['completed', 'failed']).count(),
        'inference_models': InferenceModel.objects.filter(workflow__created_by=request.user).count()
    }
    
    return Response({
        'task_types': task_types_stats,
        'total_stats': total_stats
    })
