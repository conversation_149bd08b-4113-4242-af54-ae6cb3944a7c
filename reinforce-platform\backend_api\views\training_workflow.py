from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.generics import ListCreateAPIView, RetrieveUpdateDestroyAPIView
from django.shortcuts import get_object_or_404
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timedelta
import logging

from backend_api.models.training_workflow import TrainingWorkflow, TrainingMetrics, ModelEvaluation, InferenceModel
from backend_api.serializers.training_workflow import (
    TrainingWorkflowSerializer, TrainingWorkflowCreateSerializer, TrainingWorkflowListSerializer,
    WorkflowStepDataSerializer, TrainingMetricsSerializer, TrainingMetricsCreateSerializer,
    ModelEvaluationSerializer, ModelEvaluationCreateSerializer,
    InferenceModelSerializer, InferenceModelCreateSerializer,
    WorkflowOverviewSerializer, TrainingStartSerializer, TrainingStatusSerializer
)

logger = logging.getLogger(__name__)


class TrainingWorkflowListView(ListCreateAPIView):
    """训练工作流列表和创建视图"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return TrainingWorkflowCreateSerializer
        return TrainingWorkflowListSerializer
    
    def get_queryset(self):
        """获取当前用户的工作流"""
        return TrainingWorkflow.objects.filter(created_by=self.request.user)
    
    def list(self, request, *args, **kwargs):
        """获取工作流列表"""
        queryset = self.get_queryset()
        
        # 支持按状态筛选
        status_filter = request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # 支持按任务类型筛选
        task_type_filter = request.query_params.get('task_type')
        if task_type_filter:
            queryset = queryset.filter(task_type=task_type_filter)
        
        # 分页
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'results': serializer.data,
            'count': queryset.count()
        })
    
    def perform_create(self, serializer):
        """创建工作流时设置创建者"""
        serializer.save(created_by=self.request.user)


class TrainingWorkflowDetailView(RetrieveUpdateDestroyAPIView):
    """训练工作流详情视图"""
    
    serializer_class = TrainingWorkflowSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return TrainingWorkflow.objects.filter(created_by=self.request.user)


class WorkflowStepNavigationView(APIView):
    """工作流步骤导航视图"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """导航到下一步"""
        workflow_id = request.data.get('workflow_id')
        target_step = request.data.get('target_step')
        
        workflow = get_object_or_404(
            TrainingWorkflow, 
            id=workflow_id, 
            created_by=request.user
        )
        
        # 检查是否可以进入目标步骤
        if target_step and workflow.can_proceed_to_next_step():
            workflow.current_step = target_step
            workflow.save()
            
            return Response({
                'success': True,
                'current_step': workflow.current_step,
                'message': f'已进入{workflow.get_current_step_display()}'
            })
        
        return Response({
            'success': False,
            'message': '无法进入该步骤，请完成当前步骤的必要配置'
        }, status=status.HTTP_400_BAD_REQUEST)


class WorkflowStepDataView(APIView):
    """获取和更新特定步骤的数据"""

    permission_classes = [permissions.AllowAny]
    
    def get(self, request, workflow_id, step):
        """获取指定步骤的数据"""
        workflow = get_object_or_404(TrainingWorkflow, id=workflow_id, created_by=request.user)
        
        step_data = {}
        if step == 'step1_data':
            step_data = workflow.step1_data_config
        elif step == 'step2_model':
            step_data = workflow.step2_model_config
        elif step == 'step3_training':
            step_data = workflow.step3_training_config
        elif step == 'step4_evaluation':
            step_data = workflow.step4_evaluation_config
        elif step == 'step5_deployment':
            step_data = workflow.step5_deployment_config
        
        return Response({
            'workflow_id': workflow.id,
            'step': step,
            'data': step_data,
            'current_step': workflow.current_step,
            'can_proceed': workflow.can_proceed_to_next_step()
        })
    
    def post(self, request, workflow_id, step):
        """保存指定步骤的数据"""
        workflow = get_object_or_404(TrainingWorkflow, id=workflow_id, created_by=request.user)
        
        serializer = WorkflowStepDataSerializer(data=request.data)
        if serializer.is_valid():
            step_data = serializer.validated_data['data']
            
            # 保存步骤数据
            if step == 'step1_data':
                workflow.step1_data_config = step_data
            elif step == 'step2_model':
                workflow.step2_model_config = step_data
            elif step == 'step3_training':
                workflow.step3_training_config = step_data
            elif step == 'step4_evaluation':
                workflow.step4_evaluation_config = step_data
            elif step == 'step5_deployment':
                workflow.step5_deployment_config = step_data
            
            # 更新当前步骤
            step_mapping = {
                'step1_data': 'step1_data',
                'step2_model': 'step2_model',
                'step3_training': 'step3_training',
                'step4_evaluation': 'step4_evaluation',
                'step5_deployment': 'step5_deployment'
            }
            
            if step in step_mapping:
                workflow.current_step = step_mapping[step]
                workflow.updated_at = timezone.now()
            
            workflow.save()
            
            return Response({
                'success': True,
                'message': '数据保存成功',
                'current_step': workflow.current_step,
                'can_proceed': workflow.can_proceed_to_next_step()
            })
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class TrainingMetricsView(APIView):
    """训练指标视图"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request, workflow_id):
        """获取训练指标"""
        workflow = get_object_or_404(TrainingWorkflow, id=workflow_id, created_by=request.user)
        
        metrics = TrainingMetrics.objects.filter(workflow=workflow).order_by('epoch', 'step')
        serializer = TrainingMetricsSerializer(metrics, many=True)
        
        return Response({
            'workflow_id': workflow_id,
            'metrics': serializer.data,
            'total_records': metrics.count()
        })
    
    def post(self, request, workflow_id):
        """添加训练指标"""
        workflow = get_object_or_404(TrainingWorkflow, id=workflow_id, created_by=request.user)
        
        serializer = TrainingMetricsCreateSerializer(data=request.data)
        if serializer.is_valid():
            metrics = serializer.save(workflow=workflow)
            
            # 更新工作流的最佳指标
            if metrics.map50_95 and (not workflow.best_metrics.get('map50_95') or 
                                   metrics.map50_95 > workflow.best_metrics.get('map50_95', 0)):
                workflow.best_metrics = {
                    'map50_95': metrics.map50_95,
                    'map50': metrics.map50,
                    'precision': metrics.precision,
                    'recall': metrics.recall,
                    'epoch': metrics.epoch
                }
                workflow.save()
            
            return Response({
                'success': True,
                'message': '指标记录成功',
                'metrics': TrainingMetricsSerializer(metrics).data
            }, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ModelEvaluationView(APIView):
    """模型评估视图"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request, workflow_id):
        """获取模型评估结果"""
        workflow = get_object_or_404(TrainingWorkflow, id=workflow_id, created_by=request.user)
        
        evaluations = ModelEvaluation.objects.filter(workflow=workflow).order_by('-created_at')
        serializer = ModelEvaluationSerializer(evaluations, many=True)
        
        return Response({
            'workflow_id': workflow_id,
            'evaluations': serializer.data
        })
    
    def post(self, request, workflow_id):
        """创建模型评估任务"""
        workflow = get_object_or_404(TrainingWorkflow, id=workflow_id, created_by=request.user)
        
        serializer = ModelEvaluationCreateSerializer(data=request.data)
        if serializer.is_valid():
            evaluation = serializer.save(workflow=workflow)
            
            # 这里可以启动异步评估任务
            # start_model_evaluation_task.delay(evaluation.id)
            
            return Response({
                'success': True,
                'message': '评估任务已创建',
                'evaluation': ModelEvaluationSerializer(evaluation).data
            }, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class InferenceModelListView(ListCreateAPIView):
    """推理模型列表视图"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return InferenceModelCreateSerializer
        return InferenceModelSerializer
    
    def get_queryset(self):
        """获取当前用户的推理模型"""
        return InferenceModel.objects.filter(workflow__created_by=self.request.user)
    
    def perform_create(self, serializer):
        """创建推理模型"""
        workflow_id = self.request.data.get('workflow')
        workflow = get_object_or_404(TrainingWorkflow, id=workflow_id, created_by=self.request.user)
        serializer.save(workflow=workflow)


class InferenceModelDetailView(RetrieveUpdateDestroyAPIView):
    """推理模型详情视图"""
    
    serializer_class = InferenceModelSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return InferenceModel.objects.filter(workflow__created_by=self.request.user)


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def workflow_overview(request):
    """工作流概览"""
    try:
        user = request.user

        # 统计数据
        total_workflows = TrainingWorkflow.objects.filter(created_by=user).count()
        active_workflows = TrainingWorkflow.objects.filter(
            created_by=user,
            status__in=['configuring', 'training', 'evaluating']
        ).count()
        completed_workflows = TrainingWorkflow.objects.filter(
            created_by=user,
            status='completed'
        ).count()
        failed_workflows = TrainingWorkflow.objects.filter(
            created_by=user,
            status='failed'
        ).count()

        # 任务类型统计
        task_types = TrainingWorkflow.objects.filter(created_by=user).values('task_type').annotate(
            count=Count('id')
        ).order_by('-count')

        # 最近的工作流
        recent_workflows = TrainingWorkflow.objects.filter(created_by=user).order_by('-created_at')[:10]

        # 训练统计
        training_stats = {
            'total_training_time': 0,  # 可以从TrainingMetrics计算
            'average_accuracy': 0,     # 可以从最佳指标计算
            'models_deployed': InferenceModel.objects.filter(
                workflow__created_by=user,
                status='deployed'
            ).count()
        }

        data = {
            'total_workflows': total_workflows,
            'active_workflows': active_workflows,
            'completed_workflows': completed_workflows,
            'failed_workflows': failed_workflows,
            'task_types': list(task_types),
            'recent_workflows': TrainingWorkflowListSerializer(recent_workflows, many=True).data,
            'training_stats': training_stats
        }

        return Response(data)

    except Exception as e:
        logger.error(f"工作流概览API错误: {str(e)}")
        return Response({
            'error': str(e),
            'total_workflows': 0,
            'active_workflows': 0,
            'completed_workflows': 0,
            'failed_workflows': 0,
            'task_types': [],
            'recent_workflows': [],
            'training_stats': {
                'total_training_time': 0,
                'average_accuracy': 0,
                'models_deployed': 0
            }
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TrainingStartView(APIView):
    """启动训练任务视图"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        """启动训练任务"""
        workflow_id = request.data.get('workflow_id')
        training_config = request.data.get('training_config', {})

        workflow = get_object_or_404(TrainingWorkflow, id=workflow_id, created_by=request.user)

        try:
            # 更新工作流状态
            workflow.status = 'training'
            workflow.started_at = timezone.now()
            workflow.step3_training_config = training_config
            workflow.save()

            # 这里可以集成实际的训练启动逻辑
            # 例如调用现有的训练API或启动训练任务

            # 模拟创建训练任务ID
            training_task_id = 12345  # 实际应该从训练系统获取
            workflow.training_task_id = training_task_id
            workflow.save()

            return Response({
                'success': True,
                'message': '训练任务已启动',
                'training_task_id': training_task_id,
                'workflow_id': workflow_id
            })

        except Exception as error:
            return Response({
                'success': False,
                'message': f'启动训练失败: {str(error)}'
            }, status=status.HTTP_400_BAD_REQUEST)


class TrainingStatusView(APIView):
    """获取训练状态视图"""

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, workflow_id):
        """获取训练状态"""
        workflow = get_object_or_404(TrainingWorkflow, id=workflow_id, created_by=request.user)

        # 获取最新的训练指标
        latest_metrics = TrainingMetrics.objects.filter(workflow=workflow).order_by('-recorded_at').first()

        # 计算进度
        if workflow.step3_training_config.get('epochs'):
            total_epochs = workflow.step3_training_config['epochs']
            current_epoch = latest_metrics.epoch if latest_metrics else 0
            progress = (current_epoch / total_epochs) * 100
        else:
            progress = 0

        return Response({
            'workflow_id': workflow_id,
            'status': workflow.status,
            'progress': progress,
            'current_epoch': latest_metrics.epoch if latest_metrics else 0,
            'total_epochs': workflow.step3_training_config.get('epochs', 0),
            'latest_metrics': TrainingMetricsSerializer(latest_metrics).data if latest_metrics else None
        })
