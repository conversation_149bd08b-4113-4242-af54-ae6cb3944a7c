/**
 * @Author: Szc
 * @Date: 2025-01-01 10:00:00
 * @LastEditors: Szc  
 * @LastEditTime: 2025-01-01 10:00:00
 * @Description: 大模型表单数据存储管理
 */

import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useLargeModelStore = defineStore('largeModel', () => {
  // 第一步：数据集选择与分割
  const stepOneData = ref({
    datasetName: '',
    trainPercent: 75,
    validationPercent: 25,
    trainSamples: '',
    validationSamples: '',
    rewardCount: ''
  })

  // 第二步：模型参数配置
  const stepTwoData = ref({
    params: {
      epochs: '5',
      batchSize: '5',
      optimizer: 'Adam',
      learningRate: '1e-5',
      warmupSteps: '1000',
      logInterval: '100',
      evalInterval: '10'
    },
    resources: {
      npuCount: '1',
      cpuCount: '16'
    }
  })

  // 第三步：训练执行与监控
  const stepThreeData = ref({
    trainingStatus: 'not_started',
    trainingProgress: 0,
    currentEpoch: 0,
    totalEpochs: 0,
    trainingMetrics: {
      loss: [],
      accuracy: [],
      learningRate: [],
      epochs: []
    },
    taskId: null,
    isTraining: false
  })

  // 第四步：模型性能对比
  const stepFourData = ref({
    models: [],
    selectedModelId: null,
    comparedModels: [],
    conversionTasks: {}
  })

  // 更新各步骤数据的方法
  function updateStepOneData(data) {
    stepOneData.value = { ...stepOneData.value, ...data }
    console.log('大模型-第一步数据已更新:', stepOneData.value)
  }

  function updateStepTwoData(data) {
    stepTwoData.value = { ...stepTwoData.value, ...data }
    console.log('大模型-第二步数据已更新:', stepTwoData.value)
  }

  function updateStepThreeData(data) {
    stepThreeData.value = { ...stepThreeData.value, ...data }
    console.log('大模型-第三步数据已更新:', stepThreeData.value)
  }

  function updateStepFourData(data) {
    stepFourData.value = { ...stepFourData.value, ...data }
    console.log('大模型-第四步数据已更新:', stepFourData.value)
  }

  // 重置所有数据
  function resetAllData() {
    stepOneData.value = {
      datasetName: '',
      trainPercent: 75,
      validationPercent: 25,
      trainSamples: '',
      validationSamples: '',
      rewardCount: ''
    }
    stepTwoData.value = {
      params: {
        epochs: '5',
        batchSize: '5',
        optimizer: 'Adam',
        learningRate: '1e-5',
        warmupSteps: '1000',
        logInterval: '100',
        evalInterval: '10'
      },
      resources: {
        npuCount: '1',
        cpuCount: '16'
      }
    }
    stepThreeData.value = {
      trainingStatus: 'not_started',
      trainingProgress: 0,
      currentEpoch: 0,
      totalEpochs: 0,
      trainingMetrics: {
        loss: [],
        accuracy: [],
        learningRate: [],
        epochs: []
      },
      taskId: null,
      isTraining: false
    }
    stepFourData.value = {
      models: [],
      selectedModelId: null,
      comparedModels: [],
      conversionTasks: {}
    }
    console.log('大模型-所有数据已重置')
  }

  // 获取完整的表单数据
  function getFormData() {
    return {
      stepOne: stepOneData.value,
      stepTwo: stepTwoData.value,
      stepThree: stepThreeData.value,
      stepFour: stepFourData.value
    }
  }

  return {
    // 数据
    stepOneData,
    stepTwoData,
    stepThreeData,
    stepFourData,
    
    // 方法
    updateStepOneData,
    updateStepTwoData,
    updateStepThreeData,
    updateStepFourData,
    resetAllData,
    getFormData
  }
})