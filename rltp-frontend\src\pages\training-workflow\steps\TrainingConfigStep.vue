<template>
  <div class="training-config-step">
    <div class="text-h6 q-mb-md">训练配置</div>
    <p class="text-grey-6 q-mb-lg">配置训练参数并启动训练任务</p>

    <q-form @submit.prevent="handleSave" class="q-gutter-md">
      <!-- 训练参数 -->
      <q-card flat bordered>
        <q-card-section>
          <div class="text-subtitle1 q-mb-md">训练参数</div>
          
          <div class="row q-gutter-md">
            <div class="col-12 col-md-4">
              <q-input
                v-model.number="formData.max_epochs"
                label="最大训练轮次"
                type="number"
                min="1"
                max="1000"
                :rules="[val => val >= 1 && val <= 1000 || '请输入1-1000之间的值']"
              />
            </div>
            
            <div class="col-12 col-md-4">
              <q-input
                v-model.number="formData.patience"
                label="早停耐心值"
                type="number"
                min="5"
                max="100"
                :rules="[val => val >= 5 && val <= 100 || '请输入5-100之间的值']"
              />
            </div>
            
            <div class="col-12 col-md-4">
              <q-input
                v-model.number="formData.save_period"
                label="保存周期"
                type="number"
                min="1"
                max="50"
                :rules="[val => val >= 1 && val <= 50 || '请输入1-50之间的值']"
              />
            </div>
          </div>

          <div class="row q-gutter-md q-mt-md">
            <div class="col-12 col-md-6">
              <q-checkbox
                v-model="formData.use_gpu"
                label="使用GPU加速"
              />
            </div>
            
            <div class="col-12 col-md-6" v-if="formData.use_gpu">
              <q-input
                v-model="formData.gpu_ids"
                label="GPU设备ID"
                placeholder="0,1,2"
                hint="多个GPU用逗号分隔"
              />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 验证和监控 -->
      <q-card flat bordered>
        <q-card-section>
          <div class="text-subtitle1 q-mb-md">验证和监控</div>
          
          <div class="row q-gutter-md">
            <div class="col-12 col-md-6">
              <q-input
                v-model.number="formData.val_interval"
                label="验证间隔"
                type="number"
                min="1"
                max="20"
                suffix="epoch"
                :rules="[val => val >= 1 && val <= 20 || '请输入1-20之间的值']"
              />
            </div>
            
            <div class="col-12 col-md-6">
              <q-input
                v-model.number="formData.log_interval"
                label="日志间隔"
                type="number"
                min="10"
                max="1000"
                suffix="step"
                :rules="[val => val >= 10 && val <= 1000 || '请输入10-1000之间的值']"
              />
            </div>
          </div>

          <div class="q-mt-md">
            <q-checkbox
              v-model="formData.enable_tensorboard"
              label="启用TensorBoard监控"
            />
          </div>
        </q-card-section>
      </q-card>

      <!-- 训练状态 -->
      <q-card flat bordered v-if="workflow.training_task_id">
        <q-card-section>
          <div class="text-subtitle1 q-mb-md">训练状态</div>
          
          <div class="row items-center q-gutter-md">
            <div class="col">
              <q-chip
                :color="getStatusColor(trainingStatus)"
                text-color="white"
                :icon="getStatusIcon(trainingStatus)"
              >
                {{ getStatusText(trainingStatus) }}
              </q-chip>
            </div>
            
            <div class="col-auto">
              <q-btn
                v-if="trainingStatus === 'running'"
                color="warning"
                label="暂停训练"
                icon="pause"
                @click="pauseTraining"
                :loading="pausing"
              />
              
              <q-btn
                v-if="trainingStatus === 'paused'"
                color="positive"
                label="继续训练"
                icon="play_arrow"
                @click="resumeTraining"
                :loading="resuming"
              />
              
              <q-btn
                v-if="['running', 'paused'].includes(trainingStatus)"
                color="negative"
                label="停止训练"
                icon="stop"
                @click="stopTraining"
                :loading="stopping"
              />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 操作按钮 -->
      <div class="row justify-between q-mt-lg">
        <q-btn
          flat
          color="primary"
          label="上一步"
          icon="arrow_back"
          @click="$emit('previous')"
        />
        
        <div class="q-gutter-md">
          <q-btn
            color="primary"
            label="保存配置"
            type="submit"
            :loading="saving"
          />
          
          <q-btn
            v-if="!workflow.training_task_id"
            color="positive"
            label="启动训练"
            icon="play_arrow"
            @click="handleStartTraining"
            :loading="starting"
            :disable="!isFormValid"
          />
          
          <q-btn
            v-if="trainingStatus === 'completed'"
            color="primary"
            label="下一步"
            @click="$emit('next')"
          />
        </div>
      </div>
    </q-form>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useQuasar } from 'quasar'
import { api } from 'boot/axios'

const props = defineProps({
  workflow: {
    type: Object,
    required: true
  },
  stepData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['save', 'next', 'previous', 'start-training'])

const $q = useQuasar()

// 响应式数据
const formData = ref({
  max_epochs: 100,
  patience: 20,
  save_period: 10,
  use_gpu: true,
  gpu_ids: '0',
  val_interval: 1,
  log_interval: 100,
  enable_tensorboard: true
})

const saving = ref(false)
const starting = ref(false)
const pausing = ref(false)
const resuming = ref(false)
const stopping = ref(false)
const trainingStatus = ref('pending')

let statusPollingInterval = null

// 计算属性
const isFormValid = computed(() => {
  return formData.value.max_epochs > 0 &&
         formData.value.patience > 0 &&
         formData.value.save_period > 0
})

// 方法
const getStatusColor = (status) => {
  const colors = {
    'pending': 'grey',
    'running': 'positive',
    'paused': 'warning',
    'completed': 'positive',
    'failed': 'negative',
    'cancelled': 'negative'
  }
  return colors[status] || 'grey'
}

const getStatusIcon = (status) => {
  const icons = {
    'pending': 'schedule',
    'running': 'play_arrow',
    'paused': 'pause',
    'completed': 'check_circle',
    'failed': 'error',
    'cancelled': 'cancel'
  }
  return icons[status] || 'help'
}

const getStatusText = (status) => {
  const texts = {
    'pending': '等待中',
    'running': '训练中',
    'paused': '已暂停',
    'completed': '已完成',
    'failed': '失败',
    'cancelled': '已取消'
  }
  return texts[status] || '未知'
}

const handleSave = async () => {
  saving.value = true
  try {
    emit('save', 'step3_training', formData.value)
    $q.notify({
      type: 'positive',
      message: '训练配置已保存'
    })
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

const handleStartTraining = async () => {
  if (!isFormValid.value) {
    $q.notify({
      type: 'negative',
      message: '请完善必填信息'
    })
    return
  }

  starting.value = true
  try {
    emit('start-training', formData.value)
    $q.notify({
      type: 'positive',
      message: '训练任务启动中...'
    })
    
    // 开始轮询训练状态
    startStatusPolling()
  } catch (error) {
    console.error('启动训练失败:', error)
  } finally {
    starting.value = false
  }
}

const pauseTraining = async () => {
  pausing.value = true
  try {
    await api.post(`backend/training/${props.workflow.training_task_id}/pause`)
    $q.notify({
      type: 'warning',
      message: '训练暂停请求已提交'
    })
  } catch (error) {
    console.error('暂停训练失败:', error)
    $q.notify({
      type: 'negative',
      message: '暂停训练失败'
    })
  } finally {
    pausing.value = false
  }
}

const resumeTraining = async () => {
  resuming.value = true
  try {
    await api.post(`backend/training/${props.workflow.training_task_id}/resume`)
    $q.notify({
      type: 'positive',
      message: '训练继续请求已提交'
    })
  } catch (error) {
    console.error('继续训练失败:', error)
    $q.notify({
      type: 'negative',
      message: '继续训练失败'
    })
  } finally {
    resuming.value = false
  }
}

const stopTraining = async () => {
  stopping.value = true
  try {
    await api.post(`backend/training/${props.workflow.training_task_id}/cancel`)
    $q.notify({
      type: 'warning',
      message: '训练停止请求已提交'
    })
  } catch (error) {
    console.error('停止训练失败:', error)
    $q.notify({
      type: 'negative',
      message: '停止训练失败'
    })
  } finally {
    stopping.value = false
  }
}

const checkTrainingStatus = async () => {
  if (!props.workflow.training_task_id) return

  try {
    const response = await api.get(`backend/training/${props.workflow.training_task_id}/metrics`)
    if (response && response.status) {
      trainingStatus.value = response.status
      
      // 如果训练完成，停止轮询
      if (['completed', 'failed', 'cancelled'].includes(response.status)) {
        stopStatusPolling()
      }
    }
  } catch (error) {
    console.error('获取训练状态失败:', error)
  }
}

const startStatusPolling = () => {
  if (statusPollingInterval) return
  
  statusPollingInterval = setInterval(() => {
    checkTrainingStatus()
  }, 5000)
}

const stopStatusPolling = () => {
  if (statusPollingInterval) {
    clearInterval(statusPollingInterval)
    statusPollingInterval = null
  }
}

// 初始化数据
onMounted(() => {
  // 如果有已保存的数据，加载它
  if (props.stepData && Object.keys(props.stepData).length > 0) {
    formData.value = { ...formData.value, ...props.stepData }
  }
  
  // 如果有训练任务ID，开始轮询状态
  if (props.workflow.training_task_id) {
    checkTrainingStatus()
    startStatusPolling()
  }
})

onUnmounted(() => {
  stopStatusPolling()
})
</script>

<style scoped>
.training-config-step {
  max-width: 800px;
}
</style>
