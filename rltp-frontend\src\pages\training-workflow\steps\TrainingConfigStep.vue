<template>
  <div class="training-config-step">
    <div class="step-header q-mb-lg">
      <h5 class="q-my-none">训练配置</h5>
      <p class="text-grey-6">配置训练参数和启动训练任务</p>
    </div>

    <q-form @submit.prevent="handleSaveAndNext" class="training-form">
      <!-- 基础训练参数 -->
      <q-card flat bordered class="q-mb-md">
        <q-card-section>
          <div class="text-h6 q-mb-md">基础训练参数</div>
          
          <div class="row q-gutter-md">
            <div class="col-3">
              <q-input
                v-model.number="formData.epochs"
                label="训练轮次"
                type="number"
                outlined
                min="1"
                max="1000"
                :rules="[val => val > 0 || '请输入有效的训练轮次']"
              />
            </div>
            <div class="col-3">
              <q-input
                v-model.number="formData.batch_size"
                label="批次大小"
                type="number"
                outlined
                min="1"
                max="128"
              />
            </div>
            <div class="col-3">
              <q-input
                v-model.number="formData.learning_rate"
                label="学习率"
                type="number"
                outlined
                min="0.0001"
                max="1.0"
                step="0.0001"
              />
            </div>
          </div>

          <div class="row q-gutter-md q-mt-md">
            <div class="col-3">
              <q-select
                v-model="formData.optimizer"
                :options="optimizerOptions"
                label="优化器"
                outlined
              />
            </div>
            <div class="col-3">
              <q-input
                v-model.number="formData.weight_decay"
                label="权重衰减"
                type="number"
                outlined
                min="0"
                max="0.1"
                step="0.0001"
              />
            </div>
            <div class="col-3">
              <q-input
                v-model.number="formData.momentum"
                label="动量"
                type="number"
                outlined
                min="0"
                max="1"
                step="0.1"
              />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 学习率调度 -->
      <q-card flat bordered class="q-mb-md">
        <q-card-section>
          <div class="text-h6 q-mb-md">学习率调度</div>
          
          <div class="row q-gutter-md">
            <div class="col-4">
              <q-select
                v-model="formData.lr_scheduler"
                :options="schedulerOptions"
                label="学习率调度器"
                outlined
              />
            </div>
            <div class="col-3">
              <q-input
                v-model.number="formData.warmup_epochs"
                label="预热轮次"
                type="number"
                outlined
                min="0"
                max="10"
              />
            </div>
            <div class="col-3">
              <q-input
                v-model.number="formData.patience"
                label="早停耐心值"
                type="number"
                outlined
                min="1"
                max="50"
              />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 训练设备配置 -->
      <q-card flat bordered class="q-mb-md">
        <q-card-section>
          <div class="text-h6 q-mb-md">训练设备配置</div>
          
          <div class="row q-gutter-md">
            <div class="col-3">
              <q-select
                v-model="formData.device"
                :options="deviceOptions"
                label="训练设备"
                outlined
              />
            </div>
            <div class="col-3">
              <q-input
                v-model.number="formData.workers"
                label="数据加载进程数"
                type="number"
                outlined
                min="0"
                max="16"
              />
            </div>
            <div class="col-3">
              <q-toggle
                v-model="formData.mixed_precision"
                label="混合精度训练"
              />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 保存和日志配置 -->
      <q-card flat bordered class="q-mb-md">
        <q-card-section>
          <div class="text-h6 q-mb-md">保存和日志配置</div>
          
          <div class="row q-gutter-md">
            <div class="col-3">
              <q-input
                v-model.number="formData.save_period"
                label="保存间隔(轮次)"
                type="number"
                outlined
                min="1"
                max="100"
              />
            </div>
            <div class="col-3">
              <q-input
                v-model.number="formData.val_period"
                label="验证间隔(轮次)"
                type="number"
                outlined
                min="1"
                max="10"
              />
            </div>
            <div class="col-3">
              <q-toggle
                v-model="formData.save_best_only"
                label="仅保存最佳模型"
              />
            </div>
          </div>

          <div class="row q-gutter-md q-mt-md">
            <div class="col-11">
              <q-input
                v-model="formData.project_name"
                label="项目名称"
                outlined
                hint="训练结果保存的项目名称"
              />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 训练状态 -->
      <q-card flat bordered class="q-mb-md" v-if="trainingStatus">
        <q-card-section>
          <div class="text-h6 q-mb-md">训练状态</div>
          
          <div class="training-status">
            <div class="status-item">
              <q-icon :name="getStatusIcon(trainingStatus.status)" :color="getStatusColor(trainingStatus.status)" size="md" />
              <span class="status-text">{{ getStatusText(trainingStatus.status) }}</span>
            </div>
            
            <div v-if="trainingStatus.status === 'training'" class="progress-info q-mt-md">
              <div class="progress-row">
                <span class="progress-label">训练进度:</span>
                <q-linear-progress
                  :value="trainingStatus.progress / 100"
                  color="primary"
                  size="12px"
                  class="progress-bar"
                />
                <span class="progress-text">{{ trainingStatus.progress }}%</span>
              </div>
              <div class="info-row">
                <span class="info-label">当前轮次:</span>
                <span class="info-value">{{ trainingStatus.current_epoch }} / {{ trainingStatus.total_epochs }}</span>
              </div>
              <div class="info-row" v-if="trainingStatus.latest_metrics">
                <span class="info-label">最新损失:</span>
                <span class="info-value">{{ trainingStatus.latest_metrics.train_loss?.toFixed(4) }}</span>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 操作按钮 -->
      <div class="step-actions q-mt-lg">
        <q-btn
          flat
          label="上一步"
          @click="$emit('previous')"
          class="q-mr-md"
        />
        
        <q-btn
          color="secondary"
          label="保存配置"
          @click="handleSave"
          :loading="saving"
          class="q-mr-md"
        />
        
        <q-btn
          color="primary"
          label="启动训练"
          @click="handleStartTraining"
          :loading="starting"
          :disable="!isFormValid || trainingStatus?.status === 'training'"
          class="q-mr-md"
        />
        
        <q-btn
          color="positive"
          label="保存并下一步"
          type="submit"
          :loading="saving"
          :disable="!isFormValid"
        />
      </div>
    </q-form>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useQuasar } from 'quasar'
import { api } from 'boot/axios'

const props = defineProps({
  workflow: {
    type: Object,
    required: true
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['save', 'next', 'previous', 'start-training'])

const $q = useQuasar()

// 响应式数据
const formData = ref({
  epochs: 100, // 设置默认值
  batch_size: 16,
  learning_rate: 0.01,
  optimizer: 'SGD',
  weight_decay: 0.0005,
  momentum: 0.937,
  lr_scheduler: 'cosine',
  warmup_epochs: 3,
  patience: 50,
  device: 'auto',
  workers: 8,
  mixed_precision: true,
  save_period: 10,
  val_period: 1,
  save_best_only: true,
  project_name: 'training_project'
})

const optimizerOptions = ref([
  { label: 'SGD', value: 'SGD' },
  { label: 'Adam', value: 'Adam' },
  { label: 'AdamW', value: 'AdamW' },
  { label: 'RMSprop', value: 'RMSprop' }
])

const schedulerOptions = ref([
  { label: '余弦退火', value: 'cosine' },
  { label: '线性', value: 'linear' },
  { label: '多步长', value: 'multistep' },
  { label: '指数', value: 'exponential' }
])

const deviceOptions = ref([
  { label: '自动选择', value: 'auto' },
  { label: 'CPU', value: 'cpu' },
  { label: 'GPU', value: 'cuda' },
  { label: 'NPU', value: 'npu' }
])

const saving = ref(false)
const starting = ref(false)
const trainingStatus = ref(null)

// 计算属性
const isFormValid = computed(() => {
  return formData.value.epochs > 0 && 
         formData.value.batch_size > 0 && 
         formData.value.learning_rate > 0
})

// 监听props变化
watch(() => props.data, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(formData.value, newData)
  }
}, { immediate: true })

// 方法
const handleSave = async () => {
  saving.value = true
  try {
    console.log('TrainingConfigStep: 保存配置')
    emit('save', 'step3_training', formData.value)
    
    $q.notify({
      type: 'positive',
      message: '训练配置已保存'
    })
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

const handleStartTraining = async () => {
  if (!isFormValid.value) {
    $q.notify({
      type: 'negative',
      message: '请完善必填信息'
    })
    return
  }

  starting.value = true
  try {
    // 先保存配置
    await handleSave()
    
    // 启动训练
    console.log('TrainingConfigStep: 启动训练')
    emit('start-training', formData.value)
    
    // 模拟训练状态
    trainingStatus.value = {
      status: 'training',
      progress: 0,
      current_epoch: 0,
      total_epochs: formData.value.epochs,
      latest_metrics: null
    }
    
    $q.notify({
      type: 'positive',
      message: '训练任务已启动'
    })
    
  } catch (error) {
    console.error('启动训练失败:', error)
    $q.notify({
      type: 'negative',
      message: '启动训练失败'
    })
  } finally {
    starting.value = false
  }
}

const handleSaveAndNext = async () => {
  console.log('TrainingConfigStep: 点击保存并下一步')
  
  if (!isFormValid.value) {
    $q.notify({
      type: 'negative',
      message: '请完善必填信息'
    })
    return
  }

  saving.value = true
  try {
    console.log('发送save事件:', 'step3_training', formData.value)
    emit('save', 'step3_training', formData.value)
    
    console.log('发送next事件:', formData.value)
    emit('next', formData.value)
    
    $q.notify({
      type: 'positive',
      message: '步骤3数据已保存'
    })
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

const getStatusIcon = (status) => {
  const icons = {
    pending: 'hourglass_empty',
    training: 'play_circle',
    completed: 'check_circle',
    failed: 'error'
  }
  return icons[status] || 'help'
}

const getStatusColor = (status) => {
  const colors = {
    pending: 'orange',
    training: 'blue',
    completed: 'green',
    failed: 'red'
  }
  return colors[status] || 'grey'
}

const getStatusText = (status) => {
  const texts = {
    pending: '等待中',
    training: '训练中',
    completed: '已完成',
    failed: '失败'
  }
  return texts[status] || '未知'
}
</script>

<style scoped>
.training-config-step {
  padding: 24px;
}

.step-header {
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 16px;
}

.training-form .q-card {
  margin-bottom: 16px;
}

.training-status {
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-text {
  font-size: 16px;
  font-weight: 500;
}

.progress-info {
  background: white;
  padding: 16px;
  border-radius: 8px;
}

.progress-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.progress-label {
  min-width: 80px;
  color: #666;
  font-weight: 500;
}

.progress-bar {
  flex: 1;
}

.progress-text {
  min-width: 40px;
  text-align: right;
  font-weight: 500;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.info-label {
  color: #666;
  font-weight: 500;
}

.info-value {
  color: #333;
}

.step-actions {
  display: flex;
  justify-content: flex-end;
  padding-top: 24px;
  border-top: 1px solid #e0e0e0;
}
</style>
