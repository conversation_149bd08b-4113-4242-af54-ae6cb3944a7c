<template>
    <div class="main-Form">
        <!-- 按钮区 -->
        <div class="Top">
            <TitleBtn ref="titleBtnRef"></TitleBtn>
            <q-btn class="doReturn" @click="returnToOverview">
                <img class="returnIcon" src="../../assets/images/icon_fh.png" alt="">
                <div class="labelColor">返回模型概览</div>
            </q-btn>
        </div>

        <!-- 根据当前步骤显示对应表单 -->

    </div>


</template>


<script setup>
import { ref, computed,  } from 'vue'
import TitleBtn from './components/TitleBtn.vue';



</script>


<style lang="scss" scoped>
.main-Form {
    background-color: #131520;
    height: calc(100vh - 1rem); // 减去顶部padding和margin
    display: flex;
    flex-direction: column;
}

.Top {
    margin-top: 1rem;
    margin-bottom: .125rem;
    position: relative;
    .doReturn{
        position: absolute;
        left:0;
        top:0;
        display: flex;
        .returnIcon{
            width: .375rem;
            height:.375rem;
            margin-right: .125rem;
        }
    }
}
</style>