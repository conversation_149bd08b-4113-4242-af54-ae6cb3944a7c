#!/usr/bin/env python
"""
测试训练工作流API的脚本
"""

import os
import sys
import django
import requests

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'reinforce_platform.settings')
django.setup()

from django.contrib.auth.models import User
from backend_api.models.training_workflow import TrainingWorkflow

def test_api():
    """测试API接口"""
    
    # 创建测试用户（如果不存在）
    try:
        user = User.objects.get(username='testuser')
    except User.DoesNotExist:
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        print(f"创建测试用户: {user.username}")
    
    # 创建测试工作流
    workflow = TrainingWorkflow.objects.create(
        name='测试工作流',
        description='这是一个测试工作流',
        task_type='object_detection_yolov8',
        created_by=user,
        step1_data_config={'dataset_id': 1},
        step2_model_config={'model_type': 'yolov8n'},
        step3_training_config={'epochs': 100},
        step4_evaluation_config={'evaluation_name': 'test'},
        step5_deployment_config={'model_name': 'test_model'}
    )
    
    print(f"创建测试工作流: {workflow.id} - {workflow.name}")
    
    # 测试API调用
    try:
        # 首先获取CSRF token
        session = requests.Session()
        
        # 登录
        login_data = {
            'username': 'testuser',
            'password': 'testpass123'
        }
        
        # 获取登录页面的CSRF token
        login_page = session.get('http://127.0.0.1:8000/admin/login/')
        csrf_token = None
        
        if 'csrftoken' in session.cookies:
            csrf_token = session.cookies['csrftoken']
        
        if csrf_token:
            login_data['csrfmiddlewaretoken'] = csrf_token
            
        # 执行登录
        login_response = session.post('http://127.0.0.1:8000/admin/login/', data=login_data)
        
        if login_response.status_code == 200:
            print("登录成功")
            
            # 测试工作流概览API
            overview_response = session.get('http://127.0.0.1:8000/backend/workflows/overview/')
            
            if overview_response.status_code == 200:
                data = overview_response.json()
                print("API调用成功!")
                print(f"总工作流数: {data.get('total_workflows', 0)}")
                print(f"活跃工作流数: {data.get('active_workflows', 0)}")
                print(f"已完成工作流数: {data.get('completed_workflows', 0)}")
                print(f"失败工作流数: {data.get('failed_workflows', 0)}")
                print(f"最近工作流数: {len(data.get('recent_workflows', []))}")
            else:
                print(f"API调用失败: {overview_response.status_code}")
                print(f"响应内容: {overview_response.text}")
        else:
            print(f"登录失败: {login_response.status_code}")
            
    except Exception as e:
        print(f"API测试失败: {str(e)}")
    
    # 清理测试数据
    workflow.delete()
    print("清理测试数据完成")

if __name__ == '__main__':
    test_api()
